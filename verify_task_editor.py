# -*- coding: utf-8 -*-
"""
任务编辑器功能验证脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_imports():
    """验证所有必要的模块导入"""
    print("🔍 验证模块导入...")
    
    try:
        # 验证任务编辑器
        from src.gui.components.task_editor import TaskEditorDialog
        print("✅ TaskEditorDialog 导入成功")
        
        # 验证配置管理器
        from task.config_manager import ConfigManager, ConfigValidator
        print("✅ ConfigManager 导入成功")
        
        # 验证拖拽组件
        from src.gui.components.drag_drop import DragDropTaskList, DragDropCanvas
        print("✅ 拖拽组件导入成功")
        
        # 验证主窗口
        from src.gui.main_window_v2 import MainWindowV2
        print("✅ MainWindowV2 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def verify_task_editor_structure():
    """验证任务编辑器结构"""
    print("\n🏗️ 验证任务编辑器结构...")
    
    try:
        from src.gui.components.task_editor import TaskEditorDialog
        
        # 检查关键方法
        required_methods = [
            '_create_basic_info_tab',
            '_create_parameters_tab',
            '_create_triggers_tab',
            '_create_validation_tab',
            '_create_click_task_form',
            '_create_wait_task_form',
            '_create_ocr_task_form',
            '_create_swipe_task_form',
            '_create_key_task_form',
            '_build_config',
            '_build_parameters_from_form',
            '_load_config',
            'show'
        ]
        
        for method in required_methods:
            if hasattr(TaskEditorDialog, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 结构验证失败: {e}")
        return False


def verify_config_templates():
    """验证配置模板"""
    print("\n📋 验证配置模板...")
    
    try:
        from task.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        templates = config_manager.get_available_templates()
        
        print(f"✅ 找到 {len(templates)} 个配置模板")
        
        for template in templates:
            print(f"   - {template['name']} ({template['category']})")
        
        return len(templates) > 0
        
    except Exception as e:
        print(f"❌ 模板验证失败: {e}")
        return False


def verify_main_window_integration():
    """验证主窗口集成"""
    print("\n🔗 验证主窗口集成...")
    
    try:
        from src.gui.main_window_v2 import MainWindowV2
        import src.gui.main_window_v2 as main_module
        
        # 检查编辑器组件可用性
        if hasattr(main_module, 'EDITOR_COMPONENTS_AVAILABLE'):
            print(f"✅ 编辑器组件可用性: {main_module.EDITOR_COMPONENTS_AVAILABLE}")
        else:
            print("❌ 编辑器组件可用性标志缺失")
        
        # 检查关键方法
        required_methods = [
            '_new_task_with_editor',
            '_edit_task_with_editor',
            '_get_task_config',
            '_save_task_config',
            '_on_task_dropped'
        ]
        
        for method in required_methods:
            if hasattr(MainWindowV2, method):
                print(f"✅ 主窗口方法 {method} 存在")
            else:
                print(f"❌ 主窗口方法 {method} 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口集成验证失败: {e}")
        return False


def verify_file_structure():
    """验证文件结构"""
    print("\n📁 验证文件结构...")
    
    required_files = [
        'src/gui/components/task_editor.py',
        'src/gui/components/flow_editor.py',
        'src/gui/components/workflow_editor.py',
        'src/gui/components/drag_drop.py',
        'src/gui/components/execution_monitor.py',
        'src/gui/components/flow_visualizer.py',
        'task/config_manager.py',
        'task/templates/click_task_template.json',
        'task/templates/wait_task_template.json'
    ]
    
    all_exist = True
    
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 缺失")
            all_exist = False
    
    return all_exist


def main():
    """主验证函数"""
    print("🔍 SnowZone V2 任务编辑器功能验证")
    print("=" * 50)
    
    verifications = [
        ("模块导入", verify_imports),
        ("任务编辑器结构", verify_task_editor_structure),
        ("配置模板", verify_config_templates),
        ("主窗口集成", verify_main_window_integration),
        ("文件结构", verify_file_structure),
    ]
    
    results = []
    
    for verification_name, verification_func in verifications:
        try:
            result = verification_func()
            results.append((verification_name, result))
            
            if result:
                print(f"✅ {verification_name} 验证通过")
            else:
                print(f"❌ {verification_name} 验证失败")
                
        except Exception as e:
            print(f"❌ {verification_name} 验证异常: {e}")
            results.append((verification_name, False))
    
    # 输出验证结果汇总
    print("\n" + "=" * 50)
    print("📊 验证结果汇总")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for verification_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{verification_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 总计: {passed} 个验证通过, {failed} 个验证失败")
    
    if failed == 0:
        print("\n🎉 所有任务编辑器功能验证通过！")
        print("\n💡 任务编辑器完整功能已集成:")
        print("   ✅ 多类型任务编辑支持 (点击、等待、OCR、滑动、按键)")
        print("   ✅ 表单模式和JSON模式切换")
        print("   ✅ 完整的参数验证系统")
        print("   ✅ 配置模板加载功能")
        print("   ✅ 触发器和验证配置")
        print("   ✅ 与主窗口完全集成")
        print("   ✅ 拖拽操作支持")
        print("\n🚀 现在可以在GUI V2中使用:")
        print("   1. 点击'新建任务'按钮 → 打开任务编辑器")
        print("   2. 双击任务列表中的任务 → 编辑现有任务")
        print("   3. 从左侧拖拽任务到编辑器 → 快速添加")
        print("   4. 使用模板快速创建标准任务")
    else:
        print(f"\n⚠️ 有 {failed} 个验证失败，请检查相关功能。")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
