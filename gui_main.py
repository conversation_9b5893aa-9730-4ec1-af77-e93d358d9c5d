# -*- coding: utf-8 -*-
"""
SnowZone OCR自动化工具 - GUI启动脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    try:
        # 导入GUI主窗口
        from src.gui.main_window import SnowZoneMainWindow
        
        print("启动SnowZone OCR自动化工具GUI...")
        
        # 创建并运行主窗口
        app = SnowZoneMainWindow()
        app.run()
        
    except ImportError as e:
        print(f"导入GUI模块失败: {e}")
        print("请确保已安装所有必需的依赖包")
        print("运行: pip install -r requirements.txt")
        sys.exit(1)
        
    except Exception as e:
        print(f"启动GUI失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
