{"id": "collect_rewards", "name": "收集奖励", "description": "自动收集各种奖励", "category": "daily", "enabled": true, "priority": 2, "config": {"task_info": {"name": "收集奖励", "description": "自动收集各种奖励", "version": "0_0001", "enabled": true, "priority": 2}, "triggers": [{"id": "reward_button", "type": "text_recognition", "target_text": "领取", "confidence": 0.8, "region": {"x": 0, "y": 0, "width": 1920, "height": 1080}}], "actions": [{"id": "click_collect", "type": "click", "position": {"type": "auto", "target_text": "领取"}, "timing": {"delay_before": 0.5, "delay_after": 1.5}}], "behaviors": [{"id": "collect_click_behavior", "type": "click", "enabled": true, "delay_before": 0.3, "delay_after": 0.8, "parameters": {"click_type": "left", "position": null, "offset_x": 0, "offset_y": 0}}], "flow_control": {"execution_order": ["reward_button"], "loop_enabled": true, "loop_count": 5, "continue_on_error": true}, "conditions": {"time_range": {"start": "00:00", "end": "23:59"}, "cooldown": 3600, "max_executions_per_day": 10}}, "created_time": "2025-07-11T15:43:00", "modified_time": "2025-07-11T15:43:00"}