# -*- coding: utf-8 -*-
"""
窗口捕获模块
负责定位游戏窗口、捕获窗口画面和管理窗口状态
"""

import time
import numpy as np
from typing import Optional, Tuple, List
import cv2

try:
    import win32gui
    import win32ui
    import win32con
    import win32api
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("警告: win32gui模块未安装，窗口捕获功能将受限")


class WindowCapture:
    """窗口捕获器类"""
    
    def __init__(self, window_title: str = "尘白禁区", process_name: str = "game.exe"):
        """
        初始化窗口捕获器
        
        Args:
            window_title: 目标窗口标题
            process_name: 目标进程名称
        """
        self.window_title = window_title
        self.process_name = process_name
        self.window_handle = None
        self.window_rect = None
        self.last_capture_time = 0
        self.capture_cache = None
        
        if not WIN32_AVAILABLE:
            raise ImportError("需要安装pywin32包: pip install pywin32")
    
    def find_game_window(self, timeout: float = 30.0) -> bool:
        """
        查找游戏窗口
        
        Args:
            timeout: 超时时间(秒)
            
        Returns:
            是否找到窗口
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 枚举所有窗口
            windows = self._enumerate_windows()
            
            for hwnd, title in windows:
                if self.window_title in title:
                    self.window_handle = hwnd
                    self.window_rect = self.get_window_rect(hwnd)
                    print(f"找到游戏窗口: {title} (句柄: {hwnd})")
                    return True
            
            time.sleep(1.0)  # 等待1秒后重试
        
        print(f"未找到窗口: {self.window_title} (超时: {timeout}秒)")
        return False
    
    def _enumerate_windows(self) -> List[Tuple[int, str]]:
        """
        枚举所有可见窗口
        
        Returns:
            窗口句柄和标题的列表
        """
        windows = []
        
        def enum_callback(hwnd, results):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title:
                    results.append((hwnd, window_title))
            return True
        
        win32gui.EnumWindows(enum_callback, windows)
        return windows
    
    def get_window_rect(self, hwnd: int) -> Optional[Tuple[int, int, int, int]]:
        """
        获取窗口矩形区域
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            窗口矩形 (left, top, right, bottom)
        """
        try:
            rect = win32gui.GetWindowRect(hwnd)
            return rect
        except Exception as e:
            print(f"获取窗口矩形失败: {e}")
            return None
    
    def capture_window(self, hwnd: Optional[int] = None, use_cache: bool = True) -> Optional[np.ndarray]:
        """
        捕获窗口画面
        
        Args:
            hwnd: 窗口句柄，如果为None则使用已找到的窗口
            use_cache: 是否使用缓存
            
        Returns:
            捕获的图像数组 (BGR格式)
        """
        if hwnd is None:
            hwnd = self.window_handle
        
        if hwnd is None:
            print("错误: 未指定窗口句柄")
            return None
        
        # 检查缓存
        current_time = time.time()
        if use_cache and self.capture_cache is not None:
            if current_time - self.last_capture_time < 0.1:  # 100ms缓存
                return self.capture_cache
        
        try:
            # 获取窗口设备上下文
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # 获取窗口尺寸
            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            # 创建位图
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # 复制窗口内容到位图
            result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            if result:
                # 获取位图数据
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)
                
                # 转换为numpy数组
                img = np.frombuffer(bmpstr, dtype='uint8')
                img.shape = (height, width, 4)  # BGRA格式
                
                # 转换为BGR格式
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                
                # 更新缓存
                self.capture_cache = img
                self.last_capture_time = current_time
                
                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)
                
                return img
            else:
                print("窗口捕获失败")
                return None
                
        except Exception as e:
            print(f"捕获窗口异常: {e}")
            return None
    
    def capture_region(self, region: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """
        捕获窗口指定区域
        
        Args:
            region: 区域坐标 (x, y, width, height)
            
        Returns:
            捕获的图像数组
        """
        full_image = self.capture_window()
        if full_image is None:
            return None
        
        x, y, width, height = region
        
        # 确保区域在图像范围内
        img_height, img_width = full_image.shape[:2]
        x = max(0, min(x, img_width))
        y = max(0, min(y, img_height))
        width = min(width, img_width - x)
        height = min(height, img_height - y)
        
        if width <= 0 or height <= 0:
            print("错误: 无效的捕获区域")
            return None
        
        return full_image[y:y+height, x:x+width]
    
    def is_window_valid(self) -> bool:
        """
        检查窗口是否仍然有效
        
        Returns:
            窗口是否有效
        """
        if self.window_handle is None:
            return False
        
        try:
            return win32gui.IsWindow(self.window_handle) and win32gui.IsWindowVisible(self.window_handle)
        except:
            return False
    
    def bring_window_to_front(self) -> bool:
        """
        将窗口置于前台
        
        Returns:
            操作是否成功
        """
        if self.window_handle is None:
            return False
        
        try:
            win32gui.SetForegroundWindow(self.window_handle)
            return True
        except Exception as e:
            print(f"置顶窗口失败: {e}")
            return False
    
    def get_window_info(self) -> dict:
        """
        获取窗口信息
        
        Returns:
            窗口信息字典
        """
        if self.window_handle is None:
            return {}
        
        try:
            rect = win32gui.GetWindowRect(self.window_handle)
            title = win32gui.GetWindowText(self.window_handle)
            
            return {
                "handle": self.window_handle,
                "title": title,
                "rect": rect,
                "width": rect[2] - rect[0],
                "height": rect[3] - rect[1],
                "valid": self.is_window_valid()
            }
        except Exception as e:
            print(f"获取窗口信息失败: {e}")
            return {}
    
    def save_screenshot(self, filepath: str) -> bool:
        """
        保存窗口截图
        
        Args:
            filepath: 保存路径
            
        Returns:
            保存是否成功
        """
        image = self.capture_window()
        if image is None:
            return False
        
        try:
            cv2.imwrite(filepath, image)
            print(f"截图已保存: {filepath}")
            return True
        except Exception as e:
            print(f"保存截图失败: {e}")
            return False


if __name__ == "__main__":
    # 测试窗口捕获功能
    print("测试窗口捕获功能...")
    
    capture = WindowCapture()
    
    # 查找窗口
    if capture.find_game_window(timeout=5.0):
        print("窗口信息:", capture.get_window_info())
        
        # 捕获截图
        if capture.save_screenshot("test_screenshot.png"):
            print("测试截图保存成功")
    else:
        print("未找到游戏窗口，显示所有可见窗口:")
        windows = capture._enumerate_windows()
        for hwnd, title in windows[:10]:  # 显示前10个窗口
            print(f"  {hwnd}: {title}")
