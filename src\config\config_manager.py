# -*- coding: utf-8 -*-
"""
配置管理器
负责加载、解析、验证和管理配置文件
"""

import os
import json
import jsonschema
from typing import Dict, Any, Optional, List
from pathlib import Path
import threading
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class ConfigFileHandler(FileSystemEventHandler):
    """配置文件变化监听器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('.json'):
            print(f"配置文件已修改: {event.src_path}")
            self.config_manager._reload_config_file(event.src_path)


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.configs = {}
        self.schemas = {}
        self.file_watchers = {}
        self.observer = None
        self.lock = threading.RLock()
        
        # 回调函数
        self.callbacks = {
            'on_config_loaded': [],
            'on_config_changed': [],
            'on_config_error': []
        }
        
        # 初始化
        self._ensure_config_dir()
        self._load_schemas()
        self._load_all_configs()
        self._start_file_watching()
    
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 确保任务配置目录存在
        tasks_dir = self.config_dir / "tasks"
        tasks_dir.mkdir(exist_ok=True)
    
    def _load_schemas(self):
        """加载配置文件的JSON Schema"""
        # 主配置文件Schema
        self.schemas['main_config'] = {
            "type": "object",
            "required": ["version", "game_settings", "ocr_settings"],
            "properties": {
                "version": {"type": "string"},
                "game_settings": {
                    "type": "object",
                    "required": ["window_title", "process_name"],
                    "properties": {
                        "window_title": {"type": "string"},
                        "process_name": {"type": "string"},
                        "capture_interval": {"type": "number", "minimum": 0.1}
                    }
                },
                "ocr_settings": {
                    "type": "object",
                    "properties": {
                        "language": {"type": "string"},
                        "confidence_threshold": {"type": "number", "minimum": 0, "maximum": 1}
                    }
                }
            }
        }
        
        # 任务配置文件Schema
        self.schemas['task_config'] = {
            "type": "object",
            "required": ["task_info", "triggers", "actions"],
            "properties": {
                "task_info": {
                    "type": "object",
                    "required": ["name"],
                    "properties": {
                        "name": {"type": "string"},
                        "description": {"type": "string"},
                        "enabled": {"type": "boolean"}
                    }
                },
                "triggers": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "object",
                        "required": ["type"],
                        "properties": {
                            "type": {"type": "string"},
                            "target_text": {"type": "string"},
                            "confidence": {"type": "number", "minimum": 0, "maximum": 1}
                        }
                    }
                },
                "actions": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "object",
                        "required": ["type"],
                        "properties": {
                            "type": {"type": "string"}
                        }
                    }
                }
            }
        }
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        # 加载主配置文件
        main_config_path = self.config_dir / "default_config.json"
        if main_config_path.exists():
            self.load_config("main", str(main_config_path), "main_config")
        
        # 加载任务配置文件
        tasks_dir = self.config_dir / "tasks"
        if tasks_dir.exists():
            for task_file in tasks_dir.glob("*.json"):
                task_name = task_file.stem
                self.load_config(f"task_{task_name}", str(task_file), "task_config")
    
    def load_config(self, config_name: str, file_path: str, 
                   schema_name: Optional[str] = None) -> bool:
        """
        加载配置文件
        
        Args:
            config_name: 配置名称
            file_path: 文件路径
            schema_name: Schema名称
            
        Returns:
            加载是否成功
        """
        with self.lock:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 验证配置
                if schema_name and schema_name in self.schemas:
                    self._validate_config(config_data, schema_name)
                
                # 存储配置
                self.configs[config_name] = {
                    'data': config_data,
                    'file_path': file_path,
                    'schema_name': schema_name,
                    'last_modified': os.path.getmtime(file_path)
                }
                
                print(f"配置已加载: {config_name} <- {file_path}")
                
                # 触发回调
                self._trigger_callbacks('on_config_loaded', config_name, config_data)
                
                return True
                
            except Exception as e:
                print(f"加载配置失败: {file_path} - {e}")
                self._trigger_callbacks('on_config_error', config_name, str(e))
                return False
    
    def _validate_config(self, config_data: Dict[str, Any], schema_name: str):
        """
        验证配置数据
        
        Args:
            config_data: 配置数据
            schema_name: Schema名称
        """
        if schema_name not in self.schemas:
            return
        
        try:
            jsonschema.validate(config_data, self.schemas[schema_name])
        except jsonschema.ValidationError as e:
            raise ValueError(f"配置验证失败: {e.message}")
    
    def get_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """
        获取配置数据
        
        Args:
            config_name: 配置名称
            
        Returns:
            配置数据
        """
        with self.lock:
            config_info = self.configs.get(config_name)
            if config_info:
                return config_info['data'].copy()
            return None
    
    def get_config_value(self, config_name: str, key_path: str, default: Any = None) -> Any:
        """
        获取配置中的特定值
        
        Args:
            config_name: 配置名称
            key_path: 键路径，如 "game_settings.window_title"
            default: 默认值
            
        Returns:
            配置值
        """
        config = self.get_config(config_name)
        if not config:
            return default
        
        keys = key_path.split('.')
        value = config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def update_config(self, config_name: str, updates: Dict[str, Any], 
                     save_to_file: bool = True) -> bool:
        """
        更新配置
        
        Args:
            config_name: 配置名称
            updates: 更新数据
            save_to_file: 是否保存到文件
            
        Returns:
            更新是否成功
        """
        with self.lock:
            if config_name not in self.configs:
                print(f"配置不存在: {config_name}")
                return False
            
            try:
                config_info = self.configs[config_name]
                old_data = config_info['data'].copy()
                
                # 深度合并更新
                new_data = self._deep_merge(config_info['data'], updates)
                
                # 验证更新后的配置
                schema_name = config_info.get('schema_name')
                if schema_name:
                    self._validate_config(new_data, schema_name)
                
                # 更新内存中的配置
                config_info['data'] = new_data
                
                # 保存到文件
                if save_to_file:
                    self._save_config_to_file(config_name)
                
                print(f"配置已更新: {config_name}")
                
                # 触发回调
                self._trigger_callbacks('on_config_changed', config_name, new_data, old_data)
                
                return True
                
            except Exception as e:
                print(f"更新配置失败: {config_name} - {e}")
                self._trigger_callbacks('on_config_error', config_name, str(e))
                return False
    
    def _deep_merge(self, base: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并字典
        
        Args:
            base: 基础字典
            updates: 更新字典
            
        Returns:
            合并后的字典
        """
        result = base.copy()
        
        for key, value in updates.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _save_config_to_file(self, config_name: str):
        """保存配置到文件"""
        config_info = self.configs.get(config_name)
        if not config_info:
            return
        
        file_path = config_info['file_path']
        config_data = config_info['data']
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        # 更新修改时间
        config_info['last_modified'] = os.path.getmtime(file_path)
    
    def _start_file_watching(self):
        """启动文件监控"""
        try:
            self.observer = Observer()
            handler = ConfigFileHandler(self)
            self.observer.schedule(handler, str(self.config_dir), recursive=True)
            self.observer.start()
            print("配置文件监控已启动")
        except Exception as e:
            print(f"启动文件监控失败: {e}")
    
    def _reload_config_file(self, file_path: str):
        """重新加载配置文件"""
        # 查找对应的配置名称
        for config_name, config_info in self.configs.items():
            if config_info['file_path'] == file_path:
                # 检查文件是否真的被修改了
                current_mtime = os.path.getmtime(file_path)
                if current_mtime > config_info['last_modified']:
                    schema_name = config_info.get('schema_name')
                    self.load_config(config_name, file_path, schema_name)
                break
    
    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        with self.lock:
            return {name: info['data'].copy() for name, info in self.configs.items()}
    
    def get_task_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务配置"""
        task_configs = {}
        for name, config in self.get_all_configs().items():
            if name.startswith('task_'):
                task_name = name[5:]  # 移除 'task_' 前缀
                task_configs[task_name] = config
        return task_configs
    
    def add_callback(self, event: str, callback):
        """
        添加回调函数
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def _trigger_callbacks(self, event: str, *args):
        """触发回调函数"""
        callbacks = self.callbacks.get(event, [])
        for callback in callbacks:
            try:
                callback(*args)
            except Exception as e:
                print(f"回调函数执行失败: {e}")
    
    def stop(self):
        """停止配置管理器"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            print("配置文件监控已停止")


if __name__ == "__main__":
    # 测试配置管理器
    print("测试配置管理器...")
    
    try:
        config_manager = ConfigManager()
        
        # 获取主配置
        main_config = config_manager.get_config("main")
        if main_config:
            print(f"主配置版本: {main_config.get('version')}")
        
        # 获取任务配置
        task_configs = config_manager.get_task_configs()
        print(f"找到 {len(task_configs)} 个任务配置")
        
        print("配置管理器测试完成")
        
    except Exception as e:
        print(f"配置管理器测试失败: {e}")
    
    finally:
        if 'config_manager' in locals():
            config_manager.stop()
