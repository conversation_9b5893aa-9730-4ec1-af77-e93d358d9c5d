# -*- coding: utf-8 -*-
"""
窗口捕获测试组件
用于测试窗口捕获功能
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable
import threading
import time
import io

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

from ..utils.gui_helpers import (
    GUIStyles, create_tooltip, show_error, show_info,
    ProgressDialog, ThreadSafeGUI
)


class WindowCaptureTestWidget(ttk.Frame):
    """窗口捕获测试控件"""
    
    def __init__(self, parent: tk.Widget, window_capture=None, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.window_capture = window_capture
        self.thread_safe_gui = ThreadSafeGUI(parent.winfo_toplevel())
        
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
    
    def _create_widgets(self):
        """创建控件"""
        # 标题
        self.title_label = ttk.Label(
            self,
            text="窗口捕获测试",
            font=GUIStyles.FONTS['title']
        )
        
        # 控制面板
        self.control_frame = ttk.LabelFrame(self, text="控制面板")
        
        # 游戏窗口设置
        self.window_settings_frame = ttk.Frame(self.control_frame)
        
        ttk.Label(
            self.window_settings_frame,
            text="窗口标题:"
        ).grid(row=0, column=0, sticky=tk.W, padx=(0, GUIStyles.PADDING['small']))
        
        self.window_title_var = tk.StringVar(value="尘白禁区")
        self.window_title_entry = ttk.Entry(
            self.window_settings_frame,
            textvariable=self.window_title_var,
            width=20
        )
        self.window_title_entry.grid(row=0, column=1, padx=(0, GUIStyles.PADDING['medium']))
        
        # 测试按钮
        self.test_find_btn = ttk.Button(
            self.window_settings_frame,
            text="查找窗口",
            command=self._test_find_window
        )
        self.test_find_btn.grid(row=0, column=2, padx=(0, GUIStyles.PADDING['small']))
        
        self.test_capture_btn = ttk.Button(
            self.window_settings_frame,
            text="捕获截图",
            command=self._test_capture_window,
            state=tk.DISABLED
        )
        self.test_capture_btn.grid(row=0, column=3, padx=(0, GUIStyles.PADDING['small']))
        
        self.save_screenshot_btn = ttk.Button(
            self.window_settings_frame,
            text="保存截图",
            command=self._save_screenshot,
            state=tk.DISABLED
        )
        self.save_screenshot_btn.grid(row=0, column=4)
        
        # 结果显示
        self.result_frame = ttk.LabelFrame(self, text="测试结果")
        
        # 窗口信息
        self.info_frame = ttk.Frame(self.result_frame)
        
        self.info_text = tk.Text(
            self.info_frame,
            height=6,
            width=50,
            font=GUIStyles.FONTS['code'],
            state=tk.DISABLED
        )
        
        self.info_scroll = ttk.Scrollbar(
            self.info_frame,
            orient=tk.VERTICAL,
            command=self.info_text.yview
        )
        self.info_text.configure(yscrollcommand=self.info_scroll.set)
        
        # 截图预览
        self.preview_frame = ttk.LabelFrame(self.result_frame, text="截图预览")
        
        self.preview_canvas = tk.Canvas(
            self.preview_frame,
            width=300,
            height=200,
            bg='white'
        )
        
        self.preview_scroll_x = ttk.Scrollbar(
            self.preview_frame,
            orient=tk.HORIZONTAL,
            command=self.preview_canvas.xview
        )
        self.preview_scroll_y = ttk.Scrollbar(
            self.preview_frame,
            orient=tk.VERTICAL,
            command=self.preview_canvas.yview
        )
        
        self.preview_canvas.configure(
            xscrollcommand=self.preview_scroll_x.set,
            yscrollcommand=self.preview_scroll_y.set
        )
        
        # 状态标签
        self.status_label = ttk.Label(
            self,
            text="就绪",
            font=GUIStyles.FONTS['small']
        )
        
        # 存储当前截图
        self.current_screenshot = None
    
    def _setup_layout(self):
        """设置布局"""
        # 主布局
        self.title_label.pack(pady=GUIStyles.PADDING['medium'])
        
        # 控制面板布局
        self.window_settings_frame.pack(
            fill=tk.X,
            padx=GUIStyles.PADDING['medium'],
            pady=GUIStyles.PADDING['small']
        )
        self.control_frame.pack(
            fill=tk.X,
            padx=GUIStyles.PADDING['medium'],
            pady=GUIStyles.PADDING['small']
        )
        
        # 结果显示布局
        # 窗口信息
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.info_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.info_frame.pack(
            side=tk.LEFT,
            fill=tk.BOTH,
            expand=True,
            padx=GUIStyles.PADDING['small'],
            pady=GUIStyles.PADDING['small']
        )
        
        # 截图预览
        self.preview_canvas.grid(row=0, column=0, sticky='nsew')
        self.preview_scroll_x.grid(row=1, column=0, sticky='ew')
        self.preview_scroll_y.grid(row=0, column=1, sticky='ns')
        
        self.preview_frame.grid_rowconfigure(0, weight=1)
        self.preview_frame.grid_columnconfigure(0, weight=1)
        
        self.preview_frame.pack(
            side=tk.RIGHT,
            fill=tk.BOTH,
            padx=GUIStyles.PADDING['small'],
            pady=GUIStyles.PADDING['small']
        )
        
        self.result_frame.pack(
            fill=tk.BOTH,
            expand=True,
            padx=GUIStyles.PADDING['medium'],
            pady=GUIStyles.PADDING['small']
        )
        
        # 状态栏
        self.status_label.pack(
            side=tk.LEFT,
            padx=GUIStyles.PADDING['medium'],
            pady=GUIStyles.PADDING['small']
        )
    
    def _bind_events(self):
        """绑定事件"""
        # 为预览画布绑定鼠标滚轮
        def on_mousewheel(event):
            self.preview_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
        self.preview_canvas.bind("<MouseWheel>", on_mousewheel)
        
        # 绑定画布点击事件（用于坐标选择）
        self.preview_canvas.bind("<Button-1>", self._on_canvas_click)
    
    def _on_canvas_click(self, event):
        """画布点击事件"""
        if self.current_screenshot:
            # 获取实际图像坐标
            canvas_x = self.preview_canvas.canvasx(event.x)
            canvas_y = self.preview_canvas.canvasy(event.y)
            
            # 显示坐标信息
            self._update_status(f"点击坐标: ({int(canvas_x)}, {int(canvas_y)})")
    
    def _test_find_window(self):
        """测试查找窗口"""
        if not self.window_capture:
            show_error(self, "错误", "窗口捕获模块未初始化")
            return
        
        window_title = self.window_title_var.get().strip()
        if not window_title:
            show_error(self, "错误", "请输入窗口标题")
            return
        
        # 在后台线程中执行
        def find_window():
            try:
                self.window_capture.window_title = window_title
                found = self.window_capture.find_game_window(timeout=5.0)
                
                if found:
                    window_info = self.window_capture.get_window_info()
                    self.thread_safe_gui.call_in_main_thread(
                        self._on_window_found, window_info
                    )
                else:
                    self.thread_safe_gui.call_in_main_thread(
                        self._on_window_not_found
                    )
            except Exception as e:
                self.thread_safe_gui.call_in_main_thread(
                    self._on_find_error, str(e)
                )
        
        self._update_status("正在查找窗口...")
        self.test_find_btn.config(state=tk.DISABLED)
        
        thread = threading.Thread(target=find_window, daemon=True)
        thread.start()
    
    def _on_window_found(self, window_info):
        """窗口找到回调"""
        self._update_info_text(f"""窗口查找成功！

窗口信息:
- 句柄: {window_info.get('handle', 'N/A')}
- 标题: {window_info.get('title', 'N/A')}
- 位置: {window_info.get('rect', 'N/A')}
- 宽度: {window_info.get('width', 'N/A')}
- 高度: {window_info.get('height', 'N/A')}
- 有效: {window_info.get('valid', 'N/A')}
""")
        
        self._update_status("窗口查找成功")
        self.test_find_btn.config(state=tk.NORMAL)
        self.test_capture_btn.config(state=tk.NORMAL)
    
    def _on_window_not_found(self):
        """窗口未找到回调"""
        self._update_info_text("窗口查找失败！\n\n请确认：\n1. 游戏是否正在运行\n2. 窗口标题是否正确\n3. 窗口是否可见")
        self._update_status("窗口查找失败")
        self.test_find_btn.config(state=tk.NORMAL)
        self.test_capture_btn.config(state=tk.DISABLED)
    
    def _on_find_error(self, error_msg):
        """查找错误回调"""
        self._update_info_text(f"窗口查找出错：\n{error_msg}")
        self._update_status("查找出错")
        self.test_find_btn.config(state=tk.NORMAL)
        self.test_capture_btn.config(state=tk.DISABLED)
    
    def _test_capture_window(self):
        """测试捕获窗口"""
        if not self.window_capture or not self.window_capture.window_handle:
            show_error(self, "错误", "请先成功查找窗口")
            return
        
        def capture_window():
            try:
                image = self.window_capture.capture_window()
                if image is not None:
                    self.thread_safe_gui.call_in_main_thread(
                        self._on_capture_success, image
                    )
                else:
                    self.thread_safe_gui.call_in_main_thread(
                        self._on_capture_failed
                    )
            except Exception as e:
                self.thread_safe_gui.call_in_main_thread(
                    self._on_capture_error, str(e)
                )
        
        self._update_status("正在捕获截图...")
        self.test_capture_btn.config(state=tk.DISABLED)
        
        thread = threading.Thread(target=capture_window, daemon=True)
        thread.start()
    
    def _on_capture_success(self, image):
        """捕获成功回调"""
        try:
            # 保存原始截图
            self.current_screenshot = image

            if PIL_AVAILABLE:
                # 转换为PIL图像
                if len(image.shape) == 3:
                    # BGR转RGB
                    image_rgb = image[:, :, ::-1]
                    pil_image = Image.fromarray(image_rgb)
                else:
                    pil_image = Image.fromarray(image)

                # 缩放以适应预览
                preview_width, preview_height = 300, 200
                pil_image.thumbnail((preview_width, preview_height), Image.Resampling.LANCZOS)

                # 转换为Tkinter图像
                self.photo = ImageTk.PhotoImage(pil_image)

                # 显示在画布上
                self.preview_canvas.delete("all")
                self.preview_canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
                self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))
            else:
                # 没有PIL，显示文本信息
                self.preview_canvas.delete("all")
                self.preview_canvas.create_text(
                    150, 100,
                    text="需要安装PIL库\n才能显示图像预览",
                    anchor=tk.CENTER,
                    font=("Arial", 12)
                )

            # 更新信息
            height, width = image.shape[:2]
            self._update_info_text(f"""截图捕获成功！

图像信息:
- 尺寸: {width} x {height}
- 通道数: {image.shape[2] if len(image.shape) == 3 else 1}
- 数据类型: {image.dtype}
- 大小: {image.nbytes} 字节

提示: 点击预览图像可查看坐标
""")

            self._update_status("截图捕获成功")
            self.save_screenshot_btn.config(state=tk.NORMAL)

        except Exception as e:
            self._update_info_text(f"显示截图失败：\n{str(e)}")
            self._update_status("显示失败")

        self.test_capture_btn.config(state=tk.NORMAL)
    
    def _on_capture_failed(self):
        """捕获失败回调"""
        self._update_info_text("截图捕获失败！\n\n可能原因：\n1. 窗口被遮挡\n2. 窗口已关闭\n3. 权限不足")
        self._update_status("截图捕获失败")
        self.test_capture_btn.config(state=tk.NORMAL)
    
    def _on_capture_error(self, error_msg):
        """捕获错误回调"""
        self._update_info_text(f"截图捕获出错：\n{error_msg}")
        self._update_status("捕获出错")
        self.test_capture_btn.config(state=tk.NORMAL)
    
    def _save_screenshot(self):
        """保存截图"""
        if self.current_screenshot is None:
            show_error(self, "错误", "没有可保存的截图")
            return
        
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            parent=self,
            title="保存截图",
            defaultextension=".png",
            filetypes=[
                ("PNG图像", "*.png"),
                ("JPEG图像", "*.jpg"),
                ("所有文件", "*.*")
            ]
        )
        
        if filename:
            try:
                if self.window_capture.save_screenshot(filename):
                    show_info(self, "成功", f"截图已保存到：\n{filename}")
                else:
                    show_error(self, "错误", "保存截图失败")
            except Exception as e:
                show_error(self, "错误", f"保存截图出错：\n{str(e)}")
    
    def _update_info_text(self, text: str):
        """更新信息文本"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete('1.0', tk.END)
        self.info_text.insert('1.0', text)
        self.info_text.config(state=tk.DISABLED)
    
    def _update_status(self, text: str):
        """更新状态"""
        self.status_label.config(text=text)
    
    def set_window_capture(self, window_capture):
        """设置窗口捕获模块"""
        self.window_capture = window_capture
