# -*- coding: utf-8 -*-
"""
任务编辑器测试脚本
"""

import tkinter as tk
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_task_editor():
    """测试任务编辑器"""
    print("🧪 测试任务编辑器")
    
    try:
        from src.gui.components.task_editor import TaskEditorDialog
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("1. 测试新建任务")
        dialog = TaskEditorDialog(root)
        result = dialog.show()
        
        if result:
            print(f"✅ 新建任务成功: {result.get('name', '未命名')}")
            print(f"   任务类型: {result.get('type', '未知')}")
            print(f"   任务分类: {result.get('category', '未知')}")
            print(f"   参数数量: {len(result.get('parameters', {}))}")
        else:
            print("❌ 用户取消了任务创建")
        
        print("\n2. 测试编辑现有任务")
        # 创建一个示例任务配置
        sample_config = {
            'id': 'test_click_001',
            'name': '测试点击任务',
            'description': '这是一个测试用的点击任务',
            'type': 'click',
            'category': 'basic',
            'enabled': True,
            'priority': 1,
            'timeout': 10.0,
            'retry_count': 3,
            'retry_delay': 1.0,
            'parameters': {
                'position': {
                    'type': 'absolute',
                    'x': 100,
                    'y': 200
                },
                'click_type': 'left',
                'double_click': False,
                'delay_before': 0.5,
                'delay_after': 0.5
            },
            'validation': {
                'expected_result': 'success',
                'timeout': 5.0
            }
        }
        
        dialog2 = TaskEditorDialog(root, sample_config, 'click')
        result2 = dialog2.show()
        
        if result2:
            print(f"✅ 编辑任务成功: {result2.get('name', '未命名')}")
            print(f"   任务类型: {result2.get('type', '未知')}")
            print(f"   参数数量: {len(result2.get('parameters', {}))}")
        else:
            print("❌ 用户取消了任务编辑")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 任务编辑器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_different_task_types():
    """测试不同类型的任务编辑器"""
    print("\n🎯 测试不同任务类型的编辑器")
    
    try:
        from src.gui.components.task_editor import TaskEditorDialog
        
        root = tk.Tk()
        root.withdraw()
        
        # 测试不同任务类型的配置
        task_types = [
            ('click', '点击任务'),
            ('wait', '等待任务'),
            ('ocr', 'OCR任务'),
            ('swipe', '滑动任务'),
            ('key', '按键任务')
        ]
        
        for task_type, type_name in task_types:
            print(f"\n测试 {type_name} 编辑器...")
            
            # 创建对应类型的示例配置
            sample_config = {
                'id': f'test_{task_type}_001',
                'name': f'测试{type_name}',
                'description': f'这是一个测试用的{type_name}',
                'type': task_type,
                'category': 'basic',
                'enabled': True,
                'priority': 1,
                'timeout': 10.0,
                'retry_count': 3,
                'retry_delay': 1.0,
                'parameters': {},
                'validation': {
                    'expected_result': 'success',
                    'timeout': 5.0
                }
            }
            
            # 根据任务类型添加特定参数
            if task_type == 'click':
                sample_config['parameters'] = {
                    'position': {'type': 'absolute', 'x': 100, 'y': 200},
                    'click_type': 'left',
                    'double_click': False
                }
            elif task_type == 'wait':
                sample_config['parameters'] = {
                    'wait_type': 'time',
                    'duration': 2.0
                }
            elif task_type == 'ocr':
                sample_config['parameters'] = {
                    'region': {'x': 0, 'y': 0, 'width': 100, 'height': 50},
                    'target_text': '确定',
                    'confidence': 0.8
                }
            elif task_type == 'swipe':
                sample_config['parameters'] = {
                    'start_position': {'x': 100, 'y': 200},
                    'end_position': {'x': 300, 'y': 200},
                    'duration': 1.0
                }
            elif task_type == 'key':
                sample_config['parameters'] = {
                    'key_type': 'single',
                    'key_value': 'Enter',
                    'duration': 0.1
                }
            
            try:
                dialog = TaskEditorDialog(root, sample_config, task_type)
                print(f"✅ {type_name}编辑器创建成功")
                
                # 这里可以添加自动化测试逻辑
                # 暂时只测试创建，不显示对话框
                dialog.dialog.destroy()
                
            except Exception as e:
                print(f"❌ {type_name}编辑器创建失败: {e}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 任务类型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_validation():
    """测试配置验证功能"""
    print("\n🔍 测试配置验证功能")
    
    try:
        from task.config_manager import ConfigValidator
        
        validator = ConfigValidator()
        
        # 测试有效配置
        valid_config = {
            'id': 'test_001',
            'name': '测试任务',
            'category': 'basic',
            'type': 'click',
            'parameters': {
                'position': {
                    'type': 'absolute',
                    'x': 100,
                    'y': 100
                },
                'click_type': 'left'
            }
        }
        
        is_valid, errors = validator.validate_config(valid_config, 'click_task')
        print(f"✅ 有效配置验证: {'通过' if is_valid else '失败'}")
        if errors:
            for error in errors:
                print(f"   - {error}")
        
        # 测试无效配置
        invalid_config = {
            'id': '',  # 无效的ID
            'name': '测试任务',
            'category': 'invalid_category',  # 无效的分类
            'type': 'click',
            'parameters': {
                'position': {
                    'type': 'absolute'
                    # 缺少x和y坐标
                },
                'click_type': 'invalid_type'  # 无效的点击类型
            }
        }
        
        is_valid, errors = validator.validate_config(invalid_config, 'click_task')
        print(f"✅ 无效配置验证: {'失败' if not is_valid else '意外通过'}")
        if errors:
            print("   发现的错误:")
            for error in errors:
                print(f"   - {error}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 任务编辑器完整测试")
    print("=" * 50)
    
    tests = [
        ("任务编辑器基本功能", test_task_editor),
        ("不同任务类型编辑器", test_different_task_types),
        ("配置验证功能", test_config_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试完成")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("\n🎉 所有任务编辑器测试通过！")
        print("\n💡 任务编辑器功能完整可用:")
        print("   - 支持多种任务类型编辑")
        print("   - 表单模式和JSON模式切换")
        print("   - 完整的参数验证")
        print("   - 模板加载功能")
        print("   - 触发器和验证配置")
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查相关功能。")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
