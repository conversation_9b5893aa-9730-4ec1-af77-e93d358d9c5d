# -*- coding: utf-8 -*-
"""
任务编辑器自动化测试脚本（非交互式）
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_task_editor_components():
    """测试任务编辑器组件"""
    print("🧪 测试任务编辑器组件")
    
    try:
        from src.gui.components.task_editor import TaskEditorDialog
        print("✅ 任务编辑器模块导入成功")
        
        # 测试类是否可以实例化（不显示对话框）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建示例配置
        sample_config = {
            'id': 'test_001',
            'name': '测试任务',
            'description': '测试描述',
            'type': 'click',
            'category': 'basic',
            'enabled': True,
            'priority': 1,
            'timeout': 10.0,
            'retry_count': 3,
            'retry_delay': 1.0,
            'parameters': {
                'position': {
                    'type': 'absolute',
                    'x': 100,
                    'y': 100
                },
                'click_type': 'left',
                'double_click': False
            }
        }
        
        # 测试对话框创建
        dialog = TaskEditorDialog(root, sample_config, 'click')
        print("✅ 任务编辑器对话框创建成功")
        
        # 测试配置构建方法
        if hasattr(dialog, '_build_config'):
            try:
                config = dialog._build_config()
                print("✅ 配置构建方法测试成功")
                print(f"   配置包含 {len(config)} 个字段")
            except Exception as e:
                print(f"❌ 配置构建方法测试失败: {e}")
        
        # 测试参数构建方法
        if hasattr(dialog, '_build_parameters_from_form'):
            try:
                params = dialog._build_parameters_from_form()
                print("✅ 参数构建方法测试成功")
                print(f"   参数包含 {len(params)} 个字段")
            except Exception as e:
                print(f"❌ 参数构建方法测试失败: {e}")
        
        # 清理
        dialog.dialog.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 任务编辑器组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_manager():
    """测试配置管理器"""
    print("\n⚙️ 测试配置管理器")
    
    try:
        from task.config_manager import ConfigManager, ConfigValidator
        
        print("✅ 配置管理器模块导入成功")
        
        # 测试配置验证器
        validator = ConfigValidator()
        print("✅ 配置验证器创建成功")
        
        # 测试有效配置验证
        valid_config = {
            'id': 'test_001',
            'name': '测试任务',
            'category': 'basic',
            'type': 'click',
            'parameters': {
                'position': {
                    'type': 'absolute',
                    'x': 100,
                    'y': 100
                },
                'click_type': 'left'
            }
        }
        
        is_valid, errors = validator.validate_config(valid_config, 'click_task')
        print(f"✅ 配置验证测试: {'通过' if is_valid else '失败'}")
        if errors:
            for error in errors:
                print(f"   - {error}")
        
        # 测试配置管理器
        config_manager = ConfigManager()
        print("✅ 配置管理器创建成功")
        
        # 测试模板获取
        templates = config_manager.get_available_templates()
        print(f"✅ 模板获取成功，找到 {len(templates)} 个模板")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_task_types():
    """测试不同任务类型的支持"""
    print("\n🎯 测试任务类型支持")
    
    task_types = ['click', 'wait', 'ocr', 'swipe', 'key']
    
    try:
        import tkinter as tk
        from src.gui.components.task_editor import TaskEditorDialog
        
        root = tk.Tk()
        root.withdraw()
        
        for task_type in task_types:
            try:
                # 创建基本配置
                config = {
                    'id': f'test_{task_type}',
                    'name': f'测试{task_type}任务',
                    'type': task_type,
                    'category': 'basic',
                    'parameters': {}
                }
                
                # 创建对话框
                dialog = TaskEditorDialog(root, config, task_type)
                
                # 测试表单创建方法
                if hasattr(dialog, '_create_task_specific_forms'):
                    dialog._create_task_specific_forms()
                    print(f"✅ {task_type} 任务类型表单创建成功")
                
                dialog.dialog.destroy()
                
            except Exception as e:
                print(f"❌ {task_type} 任务类型测试失败: {e}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 任务类型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration_with_main_window():
    """测试与主窗口的集成"""
    print("\n🔗 测试主窗口集成")
    
    try:
        from src.gui.main_window_v2 import MainWindowV2
        
        print("✅ 主窗口V2模块导入成功")
        
        # 检查是否有编辑器组件可用标志
        import src.gui.main_window_v2 as main_module
        if hasattr(main_module, 'EDITOR_COMPONENTS_AVAILABLE'):
            print(f"✅ 编辑器组件可用性: {main_module.EDITOR_COMPONENTS_AVAILABLE}")
        
        # 检查主窗口是否有相关方法
        main_window_methods = [
            '_new_task_with_editor',
            '_edit_task_with_editor',
            '_get_task_config',
            '_save_task_config'
        ]
        
        for method_name in main_window_methods:
            if hasattr(MainWindowV2, method_name):
                print(f"✅ 主窗口方法 {method_name} 存在")
            else:
                print(f"❌ 主窗口方法 {method_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_drag_drop_integration():
    """测试拖拽功能集成"""
    print("\n🖱️ 测试拖拽功能集成")
    
    try:
        from src.gui.components.drag_drop import DragDropTaskList, DragDropCanvas
        
        print("✅ 拖拽组件模块导入成功")
        
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        # 测试拖拽任务列表
        task_list = DragDropTaskList(root)
        print("✅ 拖拽任务列表创建成功")
        
        # 测试添加任务
        sample_task = {
            'id': 'test_001',
            'name': '测试任务',
            'type': 'click',
            'category': 'basic',
            'enabled': True
        }
        
        task_list.add_task(sample_task)
        print("✅ 任务添加到拖拽列表成功")
        
        # 测试拖拽画布
        canvas = DragDropCanvas(root)
        print("✅ 拖拽画布创建成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 拖拽功能集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 任务编辑器自动化测试")
    print("=" * 50)
    
    tests = [
        ("任务编辑器组件", test_task_editor_components),
        ("配置管理器", test_config_manager),
        ("任务类型支持", test_task_types),
        ("主窗口集成", test_integration_with_main_window),
        ("拖拽功能集成", test_drag_drop_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试完成")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("\n🎉 所有任务编辑器测试通过！")
        print("\n💡 任务编辑器功能完整可用:")
        print("   ✅ 任务编辑器组件正常工作")
        print("   ✅ 配置管理器功能完整")
        print("   ✅ 支持多种任务类型")
        print("   ✅ 与主窗口集成完成")
        print("   ✅ 拖拽功能正常")
        print("\n🚀 现在可以在GUI V2中使用完整的任务编辑功能:")
        print("   - 点击'新建任务'按钮创建任务")
        print("   - 双击任务列表中的任务进行编辑")
        print("   - 拖拽任务到编辑器中")
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查相关功能。")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
