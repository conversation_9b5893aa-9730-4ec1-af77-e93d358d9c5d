{"generated_time": "2025-07-11T18:03:32.144857", "statistics": {"total_errors": 5, "by_category": {"network": 1, "timeout": 1, "permission": 1, "validation": 1, "logic": 1}, "by_severity": {"medium": 3, "high": 1, "low": 1}, "resolved_count": 0, "unresolved_count": 5}, "errors": [{"error_id": "717a9936-a190-4547-8890-122624b65dda", "timestamp": "2025-07-11T18:03:32.140169", "error_type": "ConnectionError", "error_message": "网络连接失败", "error_details": "{\n  \"type\": \"ConnectionError\",\n  \"message\": \"网络连接失败\",\n  \"args\": [\n    \"网络连接失败\"\n  ],\n  \"errno\": null,\n  \"strerror\": null,\n  \"filename\": null\n}", "severity": "medium", "category": "network", "context": {"test": "网络错误"}, "stack_trace": "NoneType: None\n", "recovery_action": null, "retry_count": 0, "max_retries": 3, "resolved": false}, {"error_id": "ca6d508d-2f4f-44b2-a718-907767820d92", "timestamp": "2025-07-11T18:03:32.142717", "error_type": "TimeoutError", "error_message": "操作超时", "error_details": "{\n  \"type\": \"TimeoutError\",\n  \"message\": \"操作超时\",\n  \"args\": [\n    \"操作超时\"\n  ],\n  \"errno\": null,\n  \"strerror\": null,\n  \"filename\": null\n}", "severity": "medium", "category": "timeout", "context": {"test": "超时错误"}, "stack_trace": "NoneType: None\n", "recovery_action": null, "retry_count": 0, "max_retries": 3, "resolved": false}, {"error_id": "1cc9d0a3-1b19-47b0-85bf-b0171fc11bce", "timestamp": "2025-07-11T18:03:32.142717", "error_type": "PermissionError", "error_message": "权限不足", "error_details": "{\n  \"type\": \"PermissionError\",\n  \"message\": \"权限不足\",\n  \"args\": [\n    \"权限不足\"\n  ],\n  \"errno\": null,\n  \"strerror\": null,\n  \"filename\": null\n}", "severity": "high", "category": "permission", "context": {"test": "权限错误"}, "stack_trace": "NoneType: None\n", "recovery_action": null, "retry_count": 0, "max_retries": 3, "resolved": false}, {"error_id": "8e8b946a-52f3-4d4c-a15d-76985d08f91c", "timestamp": "2025-07-11T18:03:32.143842", "error_type": "ValueError", "error_message": "参数值错误", "error_details": "{\n  \"type\": \"ValueError\",\n  \"message\": \"参数值错误\",\n  \"args\": [\n    \"参数值错误\"\n  ]\n}", "severity": "low", "category": "validation", "context": {"test": "验证错误"}, "stack_trace": "NoneType: None\n", "recovery_action": null, "retry_count": 0, "max_retries": 3, "resolved": false}, {"error_id": "a82b8bad-c37c-4dd9-8cf0-e5ed804c52ed", "timestamp": "2025-07-11T18:03:32.143842", "error_type": "RuntimeError", "error_message": "运行时错误", "error_details": "{\n  \"type\": \"RuntimeError\",\n  \"message\": \"运行时错误\",\n  \"args\": [\n    \"运行时错误\"\n  ]\n}", "severity": "medium", "category": "logic", "context": {"test": "运行时错误"}, "stack_trace": "NoneType: None\n", "recovery_action": null, "retry_count": 0, "max_retries": 3, "resolved": false}]}