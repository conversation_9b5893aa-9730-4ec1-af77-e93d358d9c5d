# -*- coding: utf-8 -*-
"""
SnowZone OCR自动化点击工具 - 主程序入口
专为《尘白禁区》游戏设计的自动化工具

版本: 0_0001
作者: SnowZone开发团队
创建日期: 2025-07-10
"""

import os
import sys
import json
import time
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger, setup_logging
from src.utils.version_manager import VersionManager
from src.utils.exception_handler import ExceptionHandler, global_exception_handler
from src.config.config_manager import ConfigManager
from src.config.task_loader import TaskLoader

# 尝试导入完整的任务调度器，如果失败则使用简化版本
try:
    from src.core.task_scheduler import TaskScheduler
    FULL_SCHEDULER_AVAILABLE = True
except ImportError:
    from src.core.simple_scheduler import SimpleTaskScheduler as TaskScheduler
    FULL_SCHEDULER_AVAILABLE = False


class SnowZoneApp:
    """SnowZone主应用程序类"""

    def __init__(self):
        """初始化应用程序"""
        self.config_manager = None
        self.task_loader = None
        self.task_scheduler = None
        self.logger = None
        self.version_manager = None
        self.exception_handler = None
        self.running = False

        # 初始化组件
        self._initialize_components()

    def _initialize_components(self):
        """初始化各个组件"""
        try:
            # 安装全局异常处理器
            global_exception_handler.install()

            # 初始化版本管理器
            self.version_manager = VersionManager()

            # 初始化配置管理器
            self.config_manager = ConfigManager()

            # 初始化日志系统
            main_config = self.config_manager.get_config("main")
            if main_config:
                setup_logging(main_config.get("logging", {}))
            self.logger = get_logger()

            # 初始化异常处理器
            self.exception_handler = ExceptionHandler(self.logger)

            # 记录启动信息
            self.logger.log_system_info()
            current_version = self.version_manager.get_current_version()
            self.logger.log_version_info(current_version, "OCR自动化点击工具")

            # 初始化任务加载器
            self.task_loader = TaskLoader(self.config_manager)

            # 初始化任务调度器
            if FULL_SCHEDULER_AVAILABLE:
                scheduler_config = main_config.get("scheduler", {}) if main_config else {}
                self.task_scheduler = TaskScheduler(scheduler_config)
            else:
                self.logger.warning("使用简化调度器（缺少完整依赖）")
                self.task_scheduler = TaskScheduler(self.logger)

            # 加载任务到调度器
            self._load_tasks_to_scheduler()

            self.logger.info("所有组件初始化完成")

        except Exception as e:
            print(f"初始化失败: {e}")
            sys.exit(1)

    def _load_tasks_to_scheduler(self):
        """将任务加载到调度器"""
        enabled_tasks = self.task_loader.get_enabled_tasks()

        for task_id, task_info in enabled_tasks.items():
            if FULL_SCHEDULER_AVAILABLE:
                success = self.task_scheduler.add_task(task_id, task_info.config)
            else:
                success = self.task_scheduler.add_task(task_id, task_info.config)

            if success:
                self.logger.info(f"任务已加载到调度器: {task_id}")
            else:
                self.logger.warning(f"任务加载失败: {task_id}")

        scheduler_type = "完整" if FULL_SCHEDULER_AVAILABLE else "简化"
        self.logger.info(f"共加载 {len(enabled_tasks)} 个任务到{scheduler_type}调度器")

    def check_dependencies(self) -> bool:
        """检查依赖环境"""
        self.logger.info("检查依赖环境...")

        required_modules = [
            ("cv2", "opencv-python"),
            ("pytesseract", "pytesseract"),
            ("PIL", "Pillow"),
            ("numpy", "numpy"),
            ("win32gui", "pywin32"),
            ("pyautogui", "pyautogui"),
            ("watchdog", "watchdog"),
            ("jsonschema", "jsonschema")
        ]

        missing_modules = []

        for module_name, package_name in required_modules:
            try:
                __import__(module_name)
                self.logger.debug(f"依赖检查通过: {module_name}")
            except ImportError:
                missing_modules.append(package_name)
                self.logger.error(f"缺少依赖: {module_name} ({package_name})")

        if missing_modules:
            self.logger.error("请安装缺少的依赖包:")
            self.logger.error(f"pip install {' '.join(missing_modules)}")
            return False

        self.logger.info("依赖环境检查完成")
        return True

    def start_scheduler(self) -> bool:
        """启动任务调度器"""
        try:
            if self.task_scheduler.start_scheduler():
                self.logger.info("任务调度器启动成功")
                return True
            else:
                self.logger.error("任务调度器启动失败")
                return False
        except Exception as e:
            self.logger.error(f"启动调度器异常: {e}")
            return False

    def run_main_loop(self):
        """运行主循环"""
        self.logger.info("启动主循环...")
        self.running = True

        # 启动任务调度器
        if not self.start_scheduler():
            self.logger.error("无法启动任务调度器，程序退出")
            return

        try:
            # 主循环只负责保持程序运行和处理用户输入
            while self.running:
                time.sleep(1.0)

                # 可以在这里添加用户交互逻辑
                # 例如：检查键盘输入、处理命令等

        except KeyboardInterrupt:
            self.logger.info("接收到中断信号，正在停止...")
            self.stop()
        except Exception as e:
            self.logger.error(f"主循环异常: {e}")
            self.exception_handler.handle_exception(e)
            self.stop()

    def stop(self):
        """停止应用程序"""
        self.logger.info("正在停止SnowZone应用程序...")
        self.running = False

        # 停止任务调度器
        if self.task_scheduler:
            self.task_scheduler.stop_scheduler()

        # 停止配置管理器
        if self.config_manager:
            self.config_manager.stop()

        # 卸载全局异常处理器
        global_exception_handler.uninstall()

    def run(self):
        """运行应用程序"""
        self.logger.info("SnowZone OCR自动化工具启动")

        # 检查依赖
        if not self.check_dependencies():
            self.logger.error("依赖检查失败，程序退出")
            return False

        # 显示任务信息
        self._show_task_summary()

        # 运行主循环
        self.run_main_loop()

        self.logger.info("SnowZone应用程序已停止")
        return True

    def _show_task_summary(self):
        """显示任务摘要"""
        summary = self.task_loader.get_task_summary()

        self.logger.info("=" * 50)
        self.logger.info("任务摘要:")
        self.logger.info(f"  总任务数: {summary['total_tasks']}")
        self.logger.info(f"  启用任务: {summary['enabled_tasks']}")
        self.logger.info(f"  禁用任务: {summary['disabled_tasks']}")

        if summary['task_list']:
            self.logger.info("任务列表:")
            for task in summary['task_list']:
                status = "启用" if task['enabled'] else "禁用"
                self.logger.info(f"  - {task['name']} ({task['id']}) [{status}] 优先级:{task['priority']}")

        self.logger.info("=" * 50)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="SnowZone OCR自动化点击工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py                    # 正常启动
  python main.py --debug            # 调试模式
  python main.py --version          # 显示版本信息
  python main.py --list-tasks       # 列出所有任务
  python main.py --enable-task ID   # 启用指定任务
  python main.py --disable-task ID  # 禁用指定任务
        """
    )

    parser.add_argument(
        "--debug", "-d",
        action="store_true",
        help="启用调试模式"
    )

    parser.add_argument(
        "--version", "-v",
        action="store_true",
        help="显示版本信息"
    )

    parser.add_argument(
        "--list-tasks",
        action="store_true",
        help="列出所有任务"
    )

    parser.add_argument(
        "--enable-task",
        type=str,
        metavar="TASK_ID",
        help="启用指定任务"
    )

    parser.add_argument(
        "--disable-task",
        type=str,
        metavar="TASK_ID",
        help="禁用指定任务"
    )

    parser.add_argument(
        "--create-task",
        type=str,
        metavar="TASK_NAME",
        help="创建新任务模板"
    )

    parser.add_argument(
        "--no-scheduler",
        action="store_true",
        help="不启动任务调度器"
    )

    parser.add_argument(
        "--gui",
        action="store_true",
        help="启动图形界面"
    )

    return parser.parse_args()


def handle_task_management(args):
    """处理任务管理命令"""
    config_manager = ConfigManager()
    task_loader = TaskLoader(config_manager)

    if args.list_tasks:
        summary = task_loader.get_task_summary()
        print("\n任务列表:")
        print("=" * 60)
        print(f"{'ID':<20} {'名称':<20} {'状态':<8} {'优先级':<8}")
        print("-" * 60)

        for task in summary['task_list']:
            status = "启用" if task['enabled'] else "禁用"
            print(f"{task['id']:<20} {task['name']:<20} {status:<8} {task['priority']:<8}")

        print("-" * 60)
        print(f"总计: {summary['total_tasks']} 个任务 (启用: {summary['enabled_tasks']}, 禁用: {summary['disabled_tasks']})")
        return True

    if args.enable_task:
        if task_loader.enable_task(args.enable_task):
            print(f"任务已启用: {args.enable_task}")
        else:
            print(f"启用任务失败: {args.enable_task}")
        return True

    if args.disable_task:
        if task_loader.disable_task(args.disable_task):
            print(f"任务已禁用: {args.disable_task}")
        else:
            print(f"禁用任务失败: {args.disable_task}")
        return True

    if args.create_task:
        task_id = args.create_task.lower().replace(" ", "_")
        template = task_loader.create_task_template(task_id, args.create_task)

        if task_loader.save_task(task_id, template):
            print(f"任务模板已创建: {task_id}")
            print(f"配置文件: config/tasks/{task_id}.json")
            print("请编辑配置文件以自定义任务行为")
        else:
            print(f"创建任务模板失败: {task_id}")
        return True

    return False


def main():
    """主函数"""
    args = parse_arguments()

    # 显示版本信息
    if args.version:
        vm = VersionManager()
        vm.print_version_info()
        return

    # 处理任务管理命令
    if handle_task_management(args):
        return

    # 启动GUI模式
    if args.gui:
        try:
            from src.gui.main_window import SnowZoneMainWindow
            print("启动GUI模式...")
            app = SnowZoneMainWindow()
            app.run()
            return
        except ImportError as e:
            print(f"GUI模块导入失败: {e}")
            print("请确保已安装GUI相关依赖")
            sys.exit(1)
        except Exception as e:
            print(f"启动GUI失败: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

    # 创建并运行命令行应用程序
    try:
        app = SnowZoneApp()

        # 设置调试模式
        if args.debug:
            main_config = app.config_manager.get_config("main")
            if main_config:
                app.config_manager.update_config("main", {
                    "logging": {"level": "DEBUG"}
                }, save_to_file=False)
                setup_logging({"level": "DEBUG"})

        # 设置不启动调度器模式
        if args.no_scheduler:
            app.logger.info("调度器已禁用，程序将在配置模式下运行")
            return

        # 运行应用程序
        success = app.run()
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
