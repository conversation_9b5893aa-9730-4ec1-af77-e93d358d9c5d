# -*- coding: utf-8 -*-
"""
工作流类
管理任务流的编排和调度
"""

import json
import time
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import threading
import queue

from .task_flow import TaskFlow, FlowStatus, FlowResult


class WorkflowStatus(Enum):
    """工作流状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    SUCCESS = "success"      # 执行成功
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消
    PAUSED = "paused"        # 已暂停
    SCHEDULED = "scheduled"  # 已调度


class ScheduleType(Enum):
    """调度类型枚举"""
    ONCE = "once"           # 单次执行
    INTERVAL = "interval"   # 间隔执行
    CRON = "cron"          # Cron表达式
    MANUAL = "manual"      # 手动触发


@dataclass
class WorkflowStep:
    """工作流步骤"""
    id: str
    flow: TaskFlow
    order: int
    enabled: bool = True
    delay_before: float = 0.0
    delay_after: float = 0.0
    continue_on_error: bool = True
    condition: Optional[Callable[[], bool]] = None
    parameters: Optional[Dict[str, Any]] = None


@dataclass
class WorkflowResult:
    """工作流执行结果"""
    workflow_id: str
    status: WorkflowStatus
    message: str
    flow_results: List[FlowResult]
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    success_count: int = 0
    failed_count: int = 0


@dataclass
class ScheduleConfig:
    """调度配置"""
    schedule_type: ScheduleType
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    interval_seconds: Optional[int] = None
    cron_expression: Optional[str] = None
    max_executions: Optional[int] = None
    enabled: bool = True


class Workflow:
    """
    工作流类
    管理多个任务流的编排和调度
    """
    
    def __init__(self, workflow_id: str, name: str, description: str = ""):
        """
        初始化工作流
        
        Args:
            workflow_id: 工作流ID
            name: 工作流名称
            description: 工作流描述
        """
        self.workflow_id = workflow_id
        self.name = name
        self.description = description
        self.steps: List[WorkflowStep] = []
        self.status = WorkflowStatus.PENDING
        self.continue_on_error = True
        self.logger = None
        
        # 调度配置
        self.schedule_config: Optional[ScheduleConfig] = None
        self.execution_count = 0
        self.last_execution_time = None
        self.next_execution_time = None
        
        # 执行状态
        self.current_step = 0
        self.start_time = None
        self.end_time = None
        self.flow_results = []
        
        # 线程控制
        self._stop_event = threading.Event()
        self._pause_event = threading.Event()
        self._execution_thread = None
        
    def add_flow(self, flow: TaskFlow, order: int = None,
                 delay_before: float = 0.0, delay_after: float = 0.0,
                 continue_on_error: bool = True,
                 condition: Optional[Callable[[], bool]] = None,
                 parameters: Optional[Dict[str, Any]] = None) -> str:
        """
        添加任务流到工作流中
        
        Args:
            flow: 任务流
            order: 执行顺序
            delay_before: 执行前延迟
            delay_after: 执行后延迟
            continue_on_error: 出错时是否继续
            condition: 执行条件函数
            parameters: 流程参数
            
        Returns:
            步骤ID
        """
        if order is None:
            order = len(self.steps)
            
        step_id = f"{self.workflow_id}_step_{len(self.steps)}"
        step = WorkflowStep(
            id=step_id,
            flow=flow,
            order=order,
            delay_before=delay_before,
            delay_after=delay_after,
            continue_on_error=continue_on_error,
            condition=condition,
            parameters=parameters
        )
        
        self.steps.append(step)
        # 按顺序排序
        self.steps.sort(key=lambda x: x.order)
        
        return step_id
    
    def remove_flow(self, step_id: str) -> bool:
        """
        从工作流中移除任务流
        
        Args:
            step_id: 步骤ID
            
        Returns:
            是否移除成功
        """
        for i, step in enumerate(self.steps):
            if step.id == step_id:
                self.steps.pop(i)
                return True
        return False
    
    def set_schedule(self, schedule_config: ScheduleConfig):
        """设置调度配置"""
        self.schedule_config = schedule_config
        if schedule_config.start_time:
            self.next_execution_time = schedule_config.start_time
    
    def run(self) -> WorkflowResult:
        """
        执行工作流
        
        Returns:
            工作流执行结果
        """
        if not self.steps:
            return WorkflowResult(
                workflow_id=self.workflow_id,
                status=WorkflowStatus.FAILED,
                message="工作流为空",
                flow_results=[]
            )
        
        self.status = WorkflowStatus.RUNNING
        self.start_time = datetime.now()
        self.flow_results = []
        self.execution_count += 1
        self.last_execution_time = self.start_time
        
        try:
            result = self._execute_workflow()
        except Exception as e:
            result = WorkflowResult(
                workflow_id=self.workflow_id,
                status=WorkflowStatus.FAILED,
                message=f"工作流执行异常: {str(e)}",
                flow_results=self.flow_results
            )
        
        self.end_time = datetime.now()
        result.start_time = self.start_time
        result.end_time = self.end_time
        result.duration = (self.end_time - self.start_time).total_seconds()
        
        self.status = result.status
        
        # 计算下次执行时间
        self._calculate_next_execution_time()
        
        return result

    def _execute_workflow(self) -> WorkflowResult:
        """执行工作流的核心逻辑"""
        success_count = 0
        failed_count = 0

        for i, step in enumerate(self.steps):
            if not step.enabled:
                continue

            # 检查是否需要停止
            if self._stop_event.is_set():
                return WorkflowResult(
                    workflow_id=self.workflow_id,
                    status=WorkflowStatus.CANCELLED,
                    message="工作流已取消",
                    flow_results=self.flow_results,
                    success_count=success_count,
                    failed_count=failed_count
                )

            # 检查是否暂停
            while self._pause_event.is_set():
                time.sleep(0.1)
                if self._stop_event.is_set():
                    return WorkflowResult(
                        workflow_id=self.workflow_id,
                        status=WorkflowStatus.CANCELLED,
                        message="工作流已取消",
                        flow_results=self.flow_results,
                        success_count=success_count,
                        failed_count=failed_count
                    )

            # 检查执行条件
            if step.condition and not step.condition():
                if self.logger:
                    self.logger.info(f"工作流步骤 {step.id} 条件不满足，跳过执行")
                continue

            # 执行前延迟
            if step.delay_before > 0:
                time.sleep(step.delay_before)

            # 执行任务流
            self.current_step = i
            if self.logger:
                self.logger.info(f"开始执行工作流步骤 {step.id}: {step.flow.name}")

            result = step.flow.run()
            self.flow_results.append(result)

            if result.status == FlowStatus.SUCCESS:
                success_count += 1
                if self.logger:
                    self.logger.info(f"工作流步骤 {step.id} 执行成功")
            else:
                failed_count += 1
                if self.logger:
                    self.logger.error(f"工作流步骤 {step.id} 执行失败: {result.message}")

                if not step.continue_on_error:
                    return WorkflowResult(
                        workflow_id=self.workflow_id,
                        status=WorkflowStatus.FAILED,
                        message=f"工作流步骤 {step.id} 执行失败，工作流终止",
                        flow_results=self.flow_results,
                        success_count=success_count,
                        failed_count=failed_count
                    )

            # 执行后延迟
            if step.delay_after > 0:
                time.sleep(step.delay_after)

        # 确定最终状态
        if failed_count == 0:
            status = WorkflowStatus.SUCCESS
            message = f"工作流执行成功，共执行 {success_count} 个任务流"
        elif success_count > 0:
            status = WorkflowStatus.SUCCESS  # 部分成功也算成功
            message = f"工作流部分成功，成功 {success_count} 个，失败 {failed_count} 个"
        else:
            status = WorkflowStatus.FAILED
            message = f"工作流执行失败，失败 {failed_count} 个任务流"

        return WorkflowResult(
            workflow_id=self.workflow_id,
            status=status,
            message=message,
            flow_results=self.flow_results,
            success_count=success_count,
            failed_count=failed_count
        )

    def _calculate_next_execution_time(self):
        """计算下次执行时间"""
        if not self.schedule_config or not self.schedule_config.enabled:
            self.next_execution_time = None
            return

        now = datetime.now()

        if self.schedule_config.schedule_type == ScheduleType.ONCE:
            # 单次执行，执行后不再调度
            self.next_execution_time = None

        elif self.schedule_config.schedule_type == ScheduleType.INTERVAL:
            # 间隔执行
            if self.schedule_config.interval_seconds:
                self.next_execution_time = now + timedelta(
                    seconds=self.schedule_config.interval_seconds
                )
            else:
                self.next_execution_time = None

        elif self.schedule_config.schedule_type == ScheduleType.CRON:
            # Cron表达式执行
            if self.schedule_config.cron_expression:
                self.next_execution_time = self._parse_cron_next_time(
                    self.schedule_config.cron_expression, now
                )
            else:
                self.next_execution_time = None

        # 检查结束时间限制
        if (self.next_execution_time and
            self.schedule_config.end_time and
            self.next_execution_time > self.schedule_config.end_time):
            self.next_execution_time = None

        # 检查最大执行次数限制
        if (self.next_execution_time and
            self.schedule_config.max_executions and
            self.execution_count >= self.schedule_config.max_executions):
            self.next_execution_time = None

    def _parse_cron_next_time(self, cron_expression: str, from_time: datetime) -> Optional[datetime]:
        """解析Cron表达式获取下次执行时间"""
        try:
            # 简化的Cron解析实现
            # 格式: 分 时 日 月 周
            # 例如: "0 9 * * *" 表示每天9点执行

            parts = cron_expression.strip().split()
            if len(parts) != 5:
                if self.logger:
                    self.logger.error(f"无效的Cron表达式: {cron_expression}")
                return None

            minute, hour, day, month, weekday = parts

            # 从当前时间开始查找下一个匹配时间
            next_time = from_time.replace(second=0, microsecond=0)

            # 简单实现：只支持数字和*
            for _ in range(366):  # 最多查找一年
                if self._cron_time_matches(next_time, minute, hour, day, month, weekday):
                    if next_time > from_time:
                        return next_time

                # 增加一分钟
                next_time += timedelta(minutes=1)

            return None

        except Exception as e:
            if self.logger:
                self.logger.error(f"解析Cron表达式失败: {e}")
            return None

    def _cron_time_matches(self, dt: datetime, minute: str, hour: str,
                          day: str, month: str, weekday: str) -> bool:
        """检查时间是否匹配Cron表达式"""
        try:
            # 检查分钟
            if minute != '*' and int(minute) != dt.minute:
                return False

            # 检查小时
            if hour != '*' and int(hour) != dt.hour:
                return False

            # 检查日期
            if day != '*' and int(day) != dt.day:
                return False

            # 检查月份
            if month != '*' and int(month) != dt.month:
                return False

            # 检查星期 (0=周日, 1=周一, ..., 6=周六)
            if weekday != '*':
                # Python的weekday(): 0=周一, 6=周日
                # 转换为Cron格式: 0=周日, 1=周一, ..., 6=周六
                cron_weekday = (dt.weekday() + 1) % 7
                if int(weekday) != cron_weekday:
                    return False

            return True

        except (ValueError, TypeError):
            return False

    def start_scheduled_execution(self):
        """启动调度执行"""
        if not self.schedule_config or not self.schedule_config.enabled:
            return False

        if self._execution_thread and self._execution_thread.is_alive():
            return False

        self.status = WorkflowStatus.SCHEDULED
        self._stop_event.clear()
        self._pause_event.clear()

        self._execution_thread = threading.Thread(target=self._scheduled_run)
        self._execution_thread.daemon = True
        self._execution_thread.start()

        return True

    def _scheduled_run(self):
        """调度执行的线程函数"""
        while not self._stop_event.is_set():
            # 检查是否到了执行时间
            if self.next_execution_time and datetime.now() >= self.next_execution_time:
                # 检查最大执行次数
                if (self.schedule_config.max_executions and
                    self.execution_count >= self.schedule_config.max_executions):
                    break

                # 执行工作流
                self.run()

                # 如果是单次执行，退出循环
                if self.schedule_config.schedule_type == ScheduleType.ONCE:
                    break

            # 短暂休眠，避免CPU占用过高
            time.sleep(1)

        self.status = WorkflowStatus.PENDING

    def pause(self):
        """暂停工作流"""
        self._pause_event.set()
        self.status = WorkflowStatus.PAUSED

    def resume(self):
        """恢复工作流"""
        self._pause_event.clear()
        if self.status == WorkflowStatus.PAUSED:
            self.status = WorkflowStatus.RUNNING

    def stop(self):
        """停止工作流"""
        self._stop_event.set()
        self._pause_event.clear()
        self.status = WorkflowStatus.CANCELLED

        if self._execution_thread and self._execution_thread.is_alive():
            self._execution_thread.join(timeout=5)

    def get_progress(self) -> Dict[str, Any]:
        """获取执行进度"""
        total_steps = len([s for s in self.steps if s.enabled])
        completed_steps = len(self.flow_results)

        return {
            'workflow_id': self.workflow_id,
            'name': self.name,
            'status': self.status.value,
            'total_steps': total_steps,
            'completed_steps': completed_steps,
            'current_step': self.current_step,
            'progress_percent': (completed_steps / total_steps * 100) if total_steps > 0 else 0,
            'execution_count': self.execution_count,
            'last_execution_time': self.last_execution_time.isoformat() if self.last_execution_time else None,
            'next_execution_time': self.next_execution_time.isoformat() if self.next_execution_time else None
        }

    def to_dict(self) -> Dict[str, Any]:
        """将工作流转换为字典格式"""
        return {
            'workflow_id': self.workflow_id,
            'name': self.name,
            'description': self.description,
            'status': self.status.value,
            'steps_count': len(self.steps),
            'enabled_steps_count': len([s for s in self.steps if s.enabled]),
            'execution_count': self.execution_count,
            'schedule_enabled': self.schedule_config.enabled if self.schedule_config else False,
            'last_execution_time': self.last_execution_time.isoformat() if self.last_execution_time else None,
            'next_execution_time': self.next_execution_time.isoformat() if self.next_execution_time else None
        }

    def clone(self, new_workflow_id: str = None, new_name: str = None) -> 'Workflow':
        """克隆工作流"""
        import uuid

        if new_workflow_id is None:
            new_workflow_id = f"{self.workflow_id}_clone_{uuid.uuid4().hex[:8]}"

        if new_name is None:
            new_name = f"{self.name} (副本)"

        # 创建新的工作流
        cloned_workflow = Workflow(new_workflow_id, new_name, self.description)
        cloned_workflow.continue_on_error = self.continue_on_error

        # 复制调度配置
        if self.schedule_config:
            cloned_workflow.schedule_config = ScheduleConfig(
                schedule_type=self.schedule_config.schedule_type,
                start_time=self.schedule_config.start_time,
                end_time=self.schedule_config.end_time,
                interval_seconds=self.schedule_config.interval_seconds,
                cron_expression=self.schedule_config.cron_expression,
                max_executions=self.schedule_config.max_executions,
                enabled=False  # 克隆的工作流默认不启用调度
            )

        # 复制步骤
        for step in self.steps:
            cloned_workflow.add_flow(
                flow=step.flow,
                order=step.order,
                delay_before=step.delay_before,
                delay_after=step.delay_after,
                continue_on_error=step.continue_on_error,
                condition=step.condition,
                parameters=step.parameters.copy() if step.parameters else None
            )

        if self.logger:
            self.logger.info(f"工作流 {self.name} 已克隆为 {new_name}")

        return cloned_workflow

    def validate(self) -> tuple[bool, list[str]]:
        """验证工作流配置"""
        errors = []

        # 检查基本信息
        if not self.workflow_id:
            errors.append("工作流ID不能为空")

        if not self.name:
            errors.append("工作流名称不能为空")

        # 检查步骤
        if not self.steps:
            errors.append("工作流至少需要一个步骤")

        # 检查步骤顺序
        orders = [step.order for step in self.steps if step.enabled]
        if len(orders) != len(set(orders)):
            errors.append("步骤顺序不能重复")

        # 检查任务流有效性
        for step in self.steps:
            if not step.flow:
                errors.append(f"步骤 {step.id} 缺少任务流")
                continue

            # 验证任务流配置
            is_valid, flow_errors = step.flow.validate()
            if not is_valid:
                errors.extend([f"步骤 {step.id} 的任务流配置错误: {err}" for err in flow_errors])

        # 检查调度配置
        if self.schedule_config and self.schedule_config.enabled:
            if self.schedule_config.schedule_type == ScheduleType.INTERVAL:
                if not self.schedule_config.interval_seconds or self.schedule_config.interval_seconds <= 0:
                    errors.append("间隔调度需要设置有效的间隔时间")

            elif self.schedule_config.schedule_type == ScheduleType.CRON:
                if not self.schedule_config.cron_expression:
                    errors.append("Cron调度需要设置Cron表达式")
                else:
                    # 简单验证Cron表达式格式
                    parts = self.schedule_config.cron_expression.strip().split()
                    if len(parts) != 5:
                        errors.append("Cron表达式格式错误，应为：分 时 日 月 周")

            # 检查时间范围
            if (self.schedule_config.start_time and
                self.schedule_config.end_time and
                self.schedule_config.start_time >= self.schedule_config.end_time):
                errors.append("调度开始时间不能晚于或等于结束时间")

        return len(errors) == 0, errors

    def get_execution_plan(self) -> dict:
        """获取执行计划"""
        enabled_steps = [step for step in self.steps if step.enabled]

        plan = {
            'workflow_id': self.workflow_id,
            'workflow_name': self.name,
            'total_steps': len(enabled_steps),
            'estimated_duration': 0,
            'schedule_info': None,
            'steps': []
        }

        # 添加调度信息
        if self.schedule_config and self.schedule_config.enabled:
            plan['schedule_info'] = {
                'type': self.schedule_config.schedule_type.value,
                'next_execution': self.next_execution_time.isoformat() if self.next_execution_time else None,
                'interval_seconds': self.schedule_config.interval_seconds,
                'cron_expression': self.schedule_config.cron_expression,
                'max_executions': self.schedule_config.max_executions,
                'current_executions': self.execution_count
            }

        # 添加步骤信息
        for step in enabled_steps:
            # 获取任务流的执行计划
            flow_plan = step.flow.get_execution_plan()

            step_info = {
                'step_id': step.id,
                'flow_name': step.flow.name,
                'flow_id': step.flow.flow_id,
                'order': step.order,
                'delay_before': step.delay_before,
                'delay_after': step.delay_after,
                'continue_on_error': step.continue_on_error,
                'has_condition': step.condition is not None,
                'flow_steps_count': flow_plan['total_steps'],
                'estimated_duration': flow_plan['estimated_duration'] + step.delay_before + step.delay_after
            }

            plan['estimated_duration'] += step_info['estimated_duration']
            plan['steps'].append(step_info)

        return plan

    def get_execution_history(self, limit: int = 10) -> list:
        """获取执行历史（简化实现）"""
        # 这里应该从数据库或日志文件中获取历史记录
        # 当前返回空列表，实际实现需要持久化存储
        return []

    def export_config(self) -> dict:
        """导出工作流配置"""
        config = {
            'workflow_id': self.workflow_id,
            'name': self.name,
            'description': self.description,
            'continue_on_error': self.continue_on_error,
            'steps': [],
            'schedule': None
        }

        # 导出步骤配置
        for step in self.steps:
            step_config = {
                'id': step.id,
                'flow_id': step.flow.flow_id,
                'order': step.order,
                'enabled': step.enabled,
                'delay_before': step.delay_before,
                'delay_after': step.delay_after,
                'continue_on_error': step.continue_on_error,
                'parameters': step.parameters
            }
            config['steps'].append(step_config)

        # 导出调度配置
        if self.schedule_config:
            config['schedule'] = {
                'schedule_type': self.schedule_config.schedule_type.value,
                'enabled': self.schedule_config.enabled,
                'start_time': self.schedule_config.start_time.isoformat() if self.schedule_config.start_time else None,
                'end_time': self.schedule_config.end_time.isoformat() if self.schedule_config.end_time else None,
                'interval_seconds': self.schedule_config.interval_seconds,
                'cron_expression': self.schedule_config.cron_expression,
                'max_executions': self.schedule_config.max_executions
            }

        return config
