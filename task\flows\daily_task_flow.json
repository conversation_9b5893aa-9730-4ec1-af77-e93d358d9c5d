{"flow_id": "daily_task_flow_001", "name": "每日任务流程", "description": "完整的每日任务自动化流程", "enabled": true, "execution_mode": "sequential", "continue_on_error": true, "steps": [{"id": "step_001", "task_id": "daily_click_task_001", "task_name": "点击每日任务", "order": 1, "enabled": true, "delay_before": 0.0, "delay_after": 1.0, "continue_on_error": false, "parameters": {}}, {"id": "step_002", "task_id": "daily_wait_reward_001", "task_name": "等待奖励出现", "order": 2, "enabled": true, "delay_before": 0.0, "delay_after": 0.5, "continue_on_error": false, "parameters": {}}, {"id": "step_003", "task_id": "daily_collect_reward_001", "task_name": "领取奖励", "order": 3, "enabled": true, "delay_before": 0.0, "delay_after": 1.0, "continue_on_error": true, "parameters": {}}, {"id": "step_004", "task_id": "daily_confirm_collection_001", "task_name": "确认领取", "order": 4, "enabled": true, "delay_before": 0.0, "delay_after": 0.0, "continue_on_error": true, "parameters": {}}], "conditions": {"time_range": {"start": "00:00", "end": "23:59"}, "cooldown": 3600, "max_executions_per_day": 1}, "metadata": {"version": "0_0002", "created_time": "2025-07-11T16:53:00", "category": "daily", "tags": ["daily", "reward", "automation"]}}