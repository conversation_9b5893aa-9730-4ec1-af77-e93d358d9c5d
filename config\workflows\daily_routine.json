{"id": "daily_routine", "name": "每日例行任务", "description": "完整的每日例行任务流程", "enabled": true, "steps": [{"id": "step_1", "task_id": "daily_login", "task_name": "每日登录", "order": 0, "enabled": true, "delay_before": 0.0, "delay_after": 5.0, "continue_on_error": true, "parameters": {}}, {"id": "step_2", "task_id": "collect_rewards", "task_name": "收集奖励", "order": 1, "enabled": true, "delay_before": 2.0, "delay_after": 3.0, "continue_on_error": true, "parameters": {}}, {"id": "step_3", "task_id": "battle_auto", "task_name": "自动战斗", "order": 2, "enabled": true, "delay_before": 5.0, "delay_after": 10.0, "continue_on_error": false, "parameters": {}}], "created_time": "2025-07-11T15:43:00", "modified_time": "2025-07-11T15:43:00"}