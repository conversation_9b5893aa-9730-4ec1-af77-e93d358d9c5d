# -*- coding: utf-8 -*-
"""
动作执行器
负责执行鼠标点击、键盘输入等自动化操作
"""

import time
import random
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass

try:
    import pyautogui
    import win32api
    import win32con
    import win32gui
    AUTOMATION_AVAILABLE = True
except ImportError:
    AUTOMATION_AVAILABLE = False
    print("警告: 自动化模块未安装，动作执行功能将受限")


@dataclass
class ActionResult:
    """动作执行结果"""
    success: bool
    message: str
    execution_time: float


class ActionExecutor:
    """动作执行器类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化动作执行器
        
        Args:
            config: 执行器配置
        """
        if not AUTOMATION_AVAILABLE:
            raise ImportError("需要安装pyautogui和pywin32包")
        
        self.config = config or self._get_default_config()
        self._setup_pyautogui()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "click_delay_min": 0.1,
            "click_delay_max": 0.3,
            "sequence_delay": 1.0,
            "retry_delay": 2.0,
            "max_action_timeout": 10.0,
            "mouse_speed": 0.5,
            "fail_safe": True,
            "confidence": 0.8
        }
    
    def _setup_pyautogui(self):
        """设置PyAutoGUI"""
        # 设置安全模式
        pyautogui.FAILSAFE = self.config.get("fail_safe", True)
        
        # 设置鼠标移动速度
        pyautogui.PAUSE = self.config.get("mouse_speed", 0.5)
        
        print("PyAutoGUI初始化完成")
    
    def click_at_position(self, x: int, y: int, button: str = "left", 
                         clicks: int = 1, delay_before: float = 0, 
                         delay_after: float = 0) -> ActionResult:
        """
        在指定位置点击
        
        Args:
            x: X坐标
            y: Y坐标
            button: 鼠标按钮 ("left", "right", "middle")
            clicks: 点击次数
            delay_before: 点击前延时
            delay_after: 点击后延时
            
        Returns:
            执行结果
        """
        start_time = time.time()
        
        try:
            # 点击前延时
            if delay_before > 0:
                time.sleep(delay_before)
            
            # 添加随机偏移，模拟人类操作
            offset_x = random.randint(-2, 2)
            offset_y = random.randint(-2, 2)
            actual_x = x + offset_x
            actual_y = y + offset_y
            
            # 执行点击
            pyautogui.click(actual_x, actual_y, clicks=clicks, button=button)
            
            # 点击后延时
            if delay_after > 0:
                time.sleep(delay_after)
            
            execution_time = time.time() - start_time
            
            return ActionResult(
                success=True,
                message=f"成功点击位置 ({actual_x}, {actual_y})",
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return ActionResult(
                success=False,
                message=f"点击失败: {e}",
                execution_time=execution_time
            )
    
    def click_on_text(self, window_image, target_text: str, ocr_engine, 
                     confidence_threshold: float = 0.8) -> ActionResult:
        """
        点击包含指定文字的区域
        
        Args:
            window_image: 窗口图像
            target_text: 目标文字
            ocr_engine: OCR引擎实例
            confidence_threshold: 置信度阈值
            
        Returns:
            执行结果
        """
        start_time = time.time()
        
        try:
            # 查找文字位置
            positions = ocr_engine.find_text_position(window_image, target_text)
            
            if not positions:
                return ActionResult(
                    success=False,
                    message=f"未找到文字: {target_text}",
                    execution_time=time.time() - start_time
                )
            
            # 选择第一个匹配的位置
            x, y, width, height = positions[0]
            
            # 计算点击位置（区域中心）
            click_x = x + width // 2
            click_y = y + height // 2
            
            # 执行点击
            return self.click_at_position(click_x, click_y)
            
        except Exception as e:
            execution_time = time.time() - start_time
            return ActionResult(
                success=False,
                message=f"文字点击失败: {e}",
                execution_time=execution_time
            )
    
    def send_key_sequence(self, keys: str, delay_between_keys: float = 0.1) -> ActionResult:
        """
        发送按键序列
        
        Args:
            keys: 按键序列
            delay_between_keys: 按键间延时
            
        Returns:
            执行结果
        """
        start_time = time.time()
        
        try:
            # 发送按键
            for key in keys:
                pyautogui.press(key)
                if delay_between_keys > 0:
                    time.sleep(delay_between_keys)
            
            execution_time = time.time() - start_time
            
            return ActionResult(
                success=True,
                message=f"成功发送按键序列: {keys}",
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return ActionResult(
                success=False,
                message=f"按键发送失败: {e}",
                execution_time=execution_time
            )
    
    def send_text(self, text: str, typing_speed: float = 0.05) -> ActionResult:
        """
        输入文本
        
        Args:
            text: 要输入的文本
            typing_speed: 打字速度（每个字符的延时）
            
        Returns:
            执行结果
        """
        start_time = time.time()
        
        try:
            # 逐字符输入，模拟真实打字
            for char in text:
                pyautogui.write(char)
                if typing_speed > 0:
                    time.sleep(typing_speed)
            
            execution_time = time.time() - start_time
            
            return ActionResult(
                success=True,
                message=f"成功输入文本: {text}",
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return ActionResult(
                success=False,
                message=f"文本输入失败: {e}",
                execution_time=execution_time
            )
    
    def wait_for_delay(self, seconds: float) -> ActionResult:
        """
        等待指定时间
        
        Args:
            seconds: 等待秒数
            
        Returns:
            执行结果
        """
        start_time = time.time()
        
        try:
            time.sleep(seconds)
            
            execution_time = time.time() - start_time
            
            return ActionResult(
                success=True,
                message=f"等待 {seconds} 秒完成",
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return ActionResult(
                success=False,
                message=f"等待失败: {e}",
                execution_time=execution_time
            )
    
    def execute_task_sequence(self, task_list: List[Dict[str, Any]], 
                            window_capture=None, ocr_engine=None) -> List[ActionResult]:
        """
        执行任务序列
        
        Args:
            task_list: 任务列表
            window_capture: 窗口捕获器
            ocr_engine: OCR引擎
            
        Returns:
            执行结果列表
        """
        results = []
        
        for i, task in enumerate(task_list):
            print(f"执行任务 {i+1}/{len(task_list)}: {task.get('type', 'unknown')}")
            
            try:
                result = self._execute_single_task(task, window_capture, ocr_engine)
                results.append(result)
                
                # 如果任务失败且不允许继续
                if not result.success and not task.get("continue_on_error", True):
                    print(f"任务失败，停止执行: {result.message}")
                    break
                
                # 任务间延时
                sequence_delay = task.get("sequence_delay", self.config.get("sequence_delay", 1.0))
                if sequence_delay > 0:
                    time.sleep(sequence_delay)
                    
            except Exception as e:
                error_result = ActionResult(
                    success=False,
                    message=f"任务执行异常: {e}",
                    execution_time=0
                )
                results.append(error_result)
                print(f"任务执行异常: {e}")
                break
        
        return results
    
    def _execute_single_task(self, task: Dict[str, Any], window_capture=None, 
                           ocr_engine=None) -> ActionResult:
        """
        执行单个任务
        
        Args:
            task: 任务配置
            window_capture: 窗口捕获器
            ocr_engine: OCR引擎
            
        Returns:
            执行结果
        """
        task_type = task.get("type", "")
        
        if task_type == "click":
            return self._execute_click_task(task, window_capture, ocr_engine)
        
        elif task_type == "key":
            keys = task.get("keys", "")
            return self.send_key_sequence(keys)
        
        elif task_type == "text":
            text = task.get("text", "")
            return self.send_text(text)
        
        elif task_type == "wait":
            delay = task.get("delay", 1.0)
            return self.wait_for_delay(delay)
        
        elif task_type == "wait_for_text":
            return self._execute_wait_for_text_task(task, window_capture, ocr_engine)
        
        else:
            return ActionResult(
                success=False,
                message=f"未知任务类型: {task_type}",
                execution_time=0
            )
    
    def _execute_click_task(self, task: Dict[str, Any], window_capture=None, 
                          ocr_engine=None) -> ActionResult:
        """执行点击任务"""
        position = task.get("position", {})
        
        if isinstance(position, dict):
            if position.get("type") == "auto":
                # 自动查找文字位置
                target_text = task.get("target_text", "")
                if not target_text or not window_capture or not ocr_engine:
                    return ActionResult(False, "自动点击缺少必要参数", 0)
                
                window_image = window_capture.capture_window()
                if window_image is None:
                    return ActionResult(False, "无法捕获窗口", 0)
                
                return self.click_on_text(window_image, target_text, ocr_engine)
            
            else:
                # 固定坐标点击
                x = position.get("x", 0)
                y = position.get("y", 0)
                return self.click_at_position(x, y)
        
        elif isinstance(position, (list, tuple)) and len(position) >= 2:
            # 坐标数组
            return self.click_at_position(position[0], position[1])
        
        else:
            return ActionResult(False, "无效的点击位置", 0)
    
    def _execute_wait_for_text_task(self, task: Dict[str, Any], window_capture=None, 
                                  ocr_engine=None) -> ActionResult:
        """执行等待文字任务"""
        target_text = task.get("target_text", "")
        timeout = task.get("timeout", 10.0)
        check_interval = task.get("check_interval", 1.0)
        
        if not target_text or not window_capture or not ocr_engine:
            return ActionResult(False, "等待文字任务缺少必要参数", 0)
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            window_image = window_capture.capture_window()
            if window_image is not None:
                if ocr_engine.is_text_present(window_image, target_text):
                    execution_time = time.time() - start_time
                    return ActionResult(
                        success=True,
                        message=f"找到目标文字: {target_text}",
                        execution_time=execution_time
                    )
            
            time.sleep(check_interval)
        
        execution_time = time.time() - start_time
        return ActionResult(
            success=False,
            message=f"等待文字超时: {target_text}",
            execution_time=execution_time
        )


if __name__ == "__main__":
    # 测试动作执行功能
    print("测试动作执行功能...")
    
    try:
        executor = ActionExecutor()
        
        # 测试等待
        result = executor.wait_for_delay(1.0)
        print(f"等待测试: {result.success} - {result.message}")
        
        print("动作执行器初始化成功")
        
    except Exception as e:
        print(f"动作执行器测试失败: {e}")
        print("请确保已安装pyautogui和pywin32包")
