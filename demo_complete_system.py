# -*- coding: utf-8 -*-
"""
完整系统功能演示
展示SnowZone V2的所有新功能
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_task_system():
    """演示任务系统功能"""
    print("=" * 60)
    print("🎯 任务系统功能演示")
    print("=" * 60)
    
    try:
        from task.base_task import create_task_config, BaseTask, TaskResult, TaskStatus
        from task.task_flow import TaskFlow, ExecutionMode
        from task.workflow import Workflow, ScheduleConfig, ScheduleType
        from datetime import datetime, timedelta
        
        # 创建测试任务类
        class DemoTask(BaseTask):
            def execute(self):
                print(f"  执行任务: {self.task_name}")
                time.sleep(0.5)  # 模拟执行时间
                return TaskResult(
                    task_id=self.task_id,
                    status=TaskStatus.SUCCESS,
                    message=f"任务 {self.task_name} 执行完成"
                )
        
        print("1. 创建基础任务")
        tasks = []
        for i in range(3):
            config = create_task_config(f"演示任务{i+1}", f"这是第{i+1}个演示任务")
            task = DemoTask(config)
            tasks.append(task)
            print(f"  ✅ 创建任务: {task.task_name}")
        
        print("\n2. 创建任务流")
        flow = TaskFlow("demo_flow", "演示任务流", "用于演示的任务流")
        
        for i, task in enumerate(tasks):
            flow.add_task(task, order=i, delay_after=0.2)
        
        print(f"  ✅ 任务流创建完成，包含 {len(flow.steps)} 个步骤")
        
        print("\n3. 执行任务流")
        result = flow.run()
        print(f"  ✅ 任务流执行完成")
        print(f"     状态: {result.status.value}")
        print(f"     成功步骤: {result.success_count}")
        print(f"     执行时间: {result.duration:.2f}秒")
        
        print("\n4. 创建工作流")
        workflow = Workflow("demo_workflow", "演示工作流", "包含多个任务流的工作流")
        
        # 创建第二个任务流
        flow2 = TaskFlow("demo_flow2", "演示任务流2", "第二个演示任务流")
        config2 = create_task_config("汇总任务", "汇总所有结果的任务")
        summary_task = DemoTask(config2)
        flow2.add_task(summary_task, order=1)
        
        # 添加任务流到工作流
        workflow.add_flow(flow, order=1)
        workflow.add_flow(flow2, order=2, delay_before=1.0)
        
        print(f"  ✅ 工作流创建完成，包含 {len(workflow.steps)} 个流程")
        
        print("\n5. 配置调度")
        schedule_config = ScheduleConfig(
            schedule_type=ScheduleType.INTERVAL,
            start_time=datetime.now(),
            interval_seconds=300,  # 5分钟间隔
            max_executions=3,
            enabled=True
        )
        workflow.set_schedule(schedule_config)
        print("  ✅ 调度配置完成: 每5分钟执行一次，最多3次")
        
        print("\n6. 执行工作流")
        workflow_result = workflow.run()
        print(f"  ✅ 工作流执行完成")
        print(f"     状态: {workflow_result.status.value}")
        print(f"     成功流程: {workflow_result.success_count}")
        print(f"     执行时间: {workflow_result.duration:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务系统演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_config_system():
    """演示配置系统功能"""
    print("\n" + "=" * 60)
    print("⚙️ 配置系统功能演示")
    print("=" * 60)
    
    try:
        from task.config_manager import ConfigManager
        
        print("1. 初始化配置管理器")
        config_manager = ConfigManager()
        print("  ✅ 配置管理器初始化成功")
        
        print("\n2. 获取可用模板")
        templates = config_manager.get_available_templates()
        print(f"  ✅ 找到 {len(templates)} 个配置模板:")
        for template in templates:
            print(f"     - {template['name']} ({template['category']})")
        
        print("\n3. 从模板创建配置")
        if templates:
            template_id = templates[0]['template_id']
            new_config = config_manager.create_config_from_template(
                template_id,
                name="演示点击任务",
                description="从模板创建的演示任务"
            )
            print(f"  ✅ 从模板创建配置成功: {new_config['name']}")
            
            print("\n4. 验证配置")
            is_valid, errors = config_manager.validator.validate_config(new_config, "click_task")
            print(f"  ✅ 配置验证: {'通过' if is_valid else '失败'}")
            if errors:
                for error in errors:
                    print(f"     - {error}")
            
            print("\n5. 保存配置")
            success, errors, file_path = config_manager.validate_and_save_config(
                new_config, "basic", "demo_task.json"
            )
            print(f"  ✅ 配置保存: {'成功' if success else '失败'}")
            if file_path:
                print(f"     保存路径: {file_path}")
        
        print("\n6. 列出所有配置")
        configs = config_manager.list_configs()
        print(f"  ✅ 找到 {len(configs)} 个配置文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置系统演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_error_handling():
    """演示错误处理功能"""
    print("\n" + "=" * 60)
    print("🛡️ 错误处理功能演示")
    print("=" * 60)
    
    try:
        from task.error_handler import ErrorHandler, get_error_handler
        
        print("1. 初始化错误处理器")
        error_handler = get_error_handler()
        print("  ✅ 错误处理器初始化成功")
        
        print("\n2. 模拟各种错误")
        test_errors = [
            (ConnectionError("网络连接失败"), "网络错误"),
            (TimeoutError("操作超时"), "超时错误"),
            (ValueError("参数值错误"), "验证错误"),
        ]
        
        for exception, description in test_errors:
            error_info = error_handler.handle_error(exception, {"demo": description})
            print(f"  ✅ {description}处理完成:")
            print(f"     错误ID: {error_info.error_id}")
            print(f"     严重程度: {error_info.severity.value}")
            print(f"     恢复动作: {error_info.recovery_action.value if error_info.recovery_action else 'None'}")
        
        print("\n3. 错误统计")
        stats = error_handler.get_error_statistics()
        print(f"  ✅ 错误统计:")
        print(f"     总错误数: {stats['total_errors']}")
        print(f"     按分类: {stats['by_category']}")
        print(f"     按严重程度: {stats['by_severity']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_persistence():
    """演示持久化功能"""
    print("\n" + "=" * 60)
    print("💾 持久化功能演示")
    print("=" * 60)
    
    try:
        from task.persistence import PersistenceManager
        from task.task_flow import TaskFlow
        from task.workflow import Workflow
        
        print("1. 初始化持久化管理器")
        persistence = PersistenceManager("demo_data")
        print("  ✅ 持久化管理器初始化成功")
        
        print("\n2. 创建演示数据")
        demo_flow = TaskFlow("persist_demo_flow", "持久化演示流程", "用于演示持久化功能")
        demo_workflow = Workflow("persist_demo_workflow", "持久化演示工作流", "用于演示持久化功能")
        
        print("\n3. 保存数据")
        # 保存任务流
        success, result = persistence.save_task_flow(demo_flow, "演示版本", "demo_user")
        print(f"  ✅ 任务流保存: {'成功' if success else '失败'}")
        
        # 保存工作流
        success, result = persistence.save_workflow(demo_workflow, "演示版本", "demo_user")
        print(f"  ✅ 工作流保存: {'成功' if success else '失败'}")
        
        print("\n4. 创建备份")
        success, backup_path = persistence.create_backup("demo")
        print(f"  ✅ 备份创建: {'成功' if success else '失败'}")
        if success:
            print(f"     备份路径: {backup_path}")
        
        print("\n5. 列出备份")
        backups = persistence.list_backups()
        print(f"  ✅ 找到 {len(backups)} 个备份")
        
        return True
        
    except Exception as e:
        print(f"❌ 持久化演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_gui_components():
    """演示GUI组件功能"""
    print("\n" + "=" * 60)
    print("🖥️ GUI组件功能演示")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from src.gui.components.execution_monitor import ExecutionMonitorWidget, ExecutionEvent
        from datetime import datetime
        
        print("1. 创建测试窗口")
        root = tk.Tk()
        root.title("GUI组件演示")
        root.geometry("800x600")
        
        print("2. 创建执行监控组件")
        monitor = ExecutionMonitorWidget(root)
        monitor.pack(fill=tk.BOTH, expand=True)
        
        print("3. 模拟执行事件")
        def simulate_events():
            time.sleep(1)
            
            # 添加一些演示事件
            events = [
                ExecutionEvent(
                    timestamp=datetime.now(),
                    event_type="start",
                    entity_type="task",
                    entity_id="demo_001",
                    entity_name="演示任务1",
                    message="开始执行演示任务1"
                ),
                ExecutionEvent(
                    timestamp=datetime.now(),
                    event_type="progress",
                    entity_type="task",
                    entity_id="demo_001",
                    entity_name="演示任务1",
                    message="任务执行中...",
                    details={"progress": "50%"}
                ),
                ExecutionEvent(
                    timestamp=datetime.now(),
                    event_type="complete",
                    entity_type="task",
                    entity_id="demo_001",
                    entity_name="演示任务1",
                    message="任务执行完成",
                    details={"duration": "2.5秒", "result": "成功"}
                )
            ]
            
            for event in events:
                monitor.add_event(event)
                time.sleep(0.5)
        
        # 在后台线程中运行事件模拟
        threading.Thread(target=simulate_events, daemon=True).start()
        
        print("  ✅ GUI组件演示窗口已创建")
        print("  ℹ️ 窗口将在5秒后自动关闭")
        
        # 5秒后关闭窗口
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI组件演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主演示函数"""
    print("🚀 SnowZone V2 完整系统功能演示")
    print(f"📅 演示时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    demos = [
        ("任务系统", demo_task_system),
        ("配置系统", demo_config_system),
        ("错误处理", demo_error_handling),
        ("持久化", demo_persistence),
        ("GUI组件", demo_gui_components),
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🔄 开始演示: {demo_name}")
            result = demo_func()
            results.append((demo_name, result))
            
            if result:
                print(f"✅ {demo_name} 演示完成")
            else:
                print(f"❌ {demo_name} 演示失败")
                
        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {e}")
            results.append((demo_name, False))
    
    # 输出演示结果汇总
    print("\n" + "=" * 60)
    print("📊 演示结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for demo_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{demo_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 总计: {passed} 个演示成功, {failed} 个演示失败")
    
    if failed == 0:
        print("\n🎉 所有功能演示成功！SnowZone V2 系统完整可用。")
        print("\n💡 现在可以启动完整的GUI系统:")
        print("   python gui_main_v2.py")
    else:
        print(f"\n⚠️ 有 {failed} 个演示失败，请检查相关功能。")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
