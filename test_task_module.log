2025-07-11 17:03:32,770 - __main__ - INFO - ==================================================
2025-07-11 17:03:32,771 - __main__ - INFO - 开始任务模块集成测试
2025-07-11 17:03:32,771 - __main__ - INFO - ==================================================
2025-07-11 17:03:32,771 - __main__ - INFO - 
1. 测试任务管理器...
2025-07-11 17:03:32,771 - __main__ - INFO - 开始测试任务管理器...
2025-07-11 17:03:32,986 - __main__ - INFO - 任务统计: {'base_tasks_count': 7, 'task_flows_count': 1, 'workflows_count': 1, 'categories': ['basic', 'daily']}
2025-07-11 17:03:32,986 - __main__ - INFO - 基础任务数量: 7
2025-07-11 17:03:32,987 - __main__ - INFO -   - 点击任务 (basic)
2025-07-11 17:03:32,987 - __main__ - INFO -   - OCR识别任务 (basic)
2025-07-11 17:03:32,987 - __main__ - INFO -   - 等待任务 (basic)
2025-07-11 17:03:32,987 - __main__ - INFO -   - 每日任务点击 (daily)
2025-07-11 17:03:32,987 - __main__ - INFO -   - 领取每日奖励 (daily)
2025-07-11 17:03:32,987 - __main__ - INFO -   - 确认奖励领取 (daily)
2025-07-11 17:03:32,987 - __main__ - INFO -   - 等待奖励出现 (daily)
2025-07-11 17:03:32,987 - __main__ - INFO - 任务流数量: 1
2025-07-11 17:03:32,987 - __main__ - INFO -   - 每日任务流程 (步骤数: 4)
2025-07-11 17:03:32,987 - __main__ - INFO - 工作流数量: 1
2025-07-11 17:03:32,988 - __main__ - INFO -   - 每日例行工作流 (步骤数: 1)
2025-07-11 17:03:32,988 - __main__ - INFO - 
2. 测试基础任务执行...
2025-07-11 17:03:32,988 - __main__ - INFO - 开始测试基础任务执行...
2025-07-11 17:03:32,996 - __main__ - WARNING - 未找到等待任务
2025-07-11 17:03:32,996 - __main__ - INFO - 
3. 测试任务流执行...
2025-07-11 17:03:32,997 - __main__ - INFO - 开始测试任务流执行...
2025-07-11 17:03:33,003 - __main__ - WARNING - 未找到每日任务流
2025-07-11 17:03:33,003 - __main__ - INFO - 
4. 测试工作流执行...
2025-07-11 17:03:33,003 - __main__ - INFO - 开始测试工作流执行...
2025-07-11 17:03:33,011 - __main__ - WARNING - 未找到每日工作流
2025-07-11 17:03:33,012 - __main__ - INFO - 
==================================================
2025-07-11 17:03:33,013 - __main__ - INFO - 测试结果汇总:
2025-07-11 17:03:33,013 - __main__ - INFO - ==================================================
2025-07-11 17:03:33,013 - __main__ - INFO - 任务管理器: 通过
2025-07-11 17:03:33,013 - __main__ - INFO - 基础任务: 失败
2025-07-11 17:03:33,014 - __main__ - INFO - 任务流: 失败
2025-07-11 17:03:33,014 - __main__ - INFO - 工作流: 失败
2025-07-11 17:03:33,015 - __main__ - INFO - 
总计: 1 个测试通过, 3 个测试失败
2025-07-11 17:03:33,015 - __main__ - WARNING - ⚠️  有 3 个测试失败，需要检查相关功能。
2025-07-11 17:04:23,732 - __main__ - INFO - ==================================================
2025-07-11 17:04:23,732 - __main__ - INFO - 开始任务模块集成测试
2025-07-11 17:04:23,732 - __main__ - INFO - ==================================================
2025-07-11 17:04:23,732 - __main__ - INFO - 
1. 测试任务管理器...
2025-07-11 17:04:23,732 - __main__ - INFO - 开始测试任务管理器...
2025-07-11 17:04:23,946 - __main__ - INFO - 任务统计: {'base_tasks_count': 7, 'task_flows_count': 1, 'workflows_count': 1, 'categories': ['basic', 'daily']}
2025-07-11 17:04:23,946 - __main__ - INFO - 基础任务数量: 7
2025-07-11 17:04:23,946 - __main__ - INFO -   - 点击任务 (basic)
2025-07-11 17:04:23,946 - __main__ - INFO -   - OCR识别任务 (basic)
2025-07-11 17:04:23,946 - __main__ - INFO -   - 等待任务 (basic)
2025-07-11 17:04:23,946 - __main__ - INFO -   - 每日任务点击 (daily)
2025-07-11 17:04:23,946 - __main__ - INFO -   - 领取每日奖励 (daily)
2025-07-11 17:04:23,946 - __main__ - INFO -   - 确认奖励领取 (daily)
2025-07-11 17:04:23,946 - __main__ - INFO -   - 等待奖励出现 (daily)
2025-07-11 17:04:23,946 - __main__ - INFO - 任务流数量: 1
2025-07-11 17:04:23,946 - __main__ - INFO -   - 每日任务流程 (步骤数: 4)
2025-07-11 17:04:23,947 - __main__ - INFO - 工作流数量: 1
2025-07-11 17:04:23,947 - __main__ - INFO -   - 每日例行工作流 (步骤数: 1)
2025-07-11 17:04:23,947 - __main__ - INFO - 
2. 测试基础任务执行...
2025-07-11 17:04:23,947 - __main__ - INFO - 开始测试基础任务执行...
2025-07-11 17:04:23,953 - __main__ - INFO - 测试任务: 等待任务
2025-07-11 17:04:23,953 - __main__ - INFO - 任务执行结果: success - 默认任务执行完成
2025-07-11 17:04:23,954 - __main__ - INFO - 
3. 测试任务流执行...
2025-07-11 17:04:23,954 - __main__ - INFO - 开始测试任务流执行...
2025-07-11 17:04:23,960 - __main__ - INFO - 测试任务流: 每日任务流程
2025-07-11 17:04:23,961 - __main__ - INFO - 任务流步骤数: 4
2025-07-11 17:04:23,961 - __main__ - INFO - 开始执行步骤 daily_task_flow_001_step_0: 每日任务点击
2025-07-11 17:04:23,961 - __main__ - INFO - 步骤 daily_task_flow_001_step_0 执行成功
2025-07-11 17:04:24,962 - __main__ - INFO - 开始执行步骤 daily_task_flow_001_step_1: 等待奖励出现
2025-07-11 17:04:24,962 - __main__ - INFO - 步骤 daily_task_flow_001_step_1 执行成功
2025-07-11 17:04:25,463 - __main__ - INFO - 开始执行步骤 daily_task_flow_001_step_2: 领取每日奖励
2025-07-11 17:04:25,463 - __main__ - INFO - 步骤 daily_task_flow_001_step_2 执行成功
2025-07-11 17:04:26,464 - __main__ - INFO - 开始执行步骤 daily_task_flow_001_step_3: 确认奖励领取
2025-07-11 17:04:26,464 - __main__ - INFO - 步骤 daily_task_flow_001_step_3 执行成功
2025-07-11 17:04:26,464 - __main__ - INFO - 任务流执行结果: success - 任务流执行成功，共执行 4 个任务
2025-07-11 17:04:26,464 - __main__ - INFO - 成功步骤: 4, 失败步骤: 0
2025-07-11 17:04:26,464 - __main__ - INFO - 
4. 测试工作流执行...
2025-07-11 17:04:26,464 - __main__ - INFO - 开始测试工作流执行...
2025-07-11 17:04:26,470 - __main__ - INFO - 测试工作流: 每日例行工作流
2025-07-11 17:04:26,470 - __main__ - INFO - 工作流步骤数: 1
2025-07-11 17:04:26,470 - __main__ - INFO - 开始执行工作流步骤 daily_workflow_001_step_0: 每日任务流程
2025-07-11 17:04:28,973 - __main__ - INFO - 工作流步骤 daily_workflow_001_step_0 执行成功
2025-07-11 17:04:30,973 - __main__ - INFO - 工作流执行结果: success - 工作流执行成功，共执行 1 个任务流
2025-07-11 17:04:30,973 - __main__ - INFO - 成功流程: 1, 失败流程: 0
2025-07-11 17:04:30,973 - __main__ - INFO - 
==================================================
2025-07-11 17:04:30,973 - __main__ - INFO - 测试结果汇总:
2025-07-11 17:04:30,973 - __main__ - INFO - ==================================================
2025-07-11 17:04:30,973 - __main__ - INFO - 任务管理器: 通过
2025-07-11 17:04:30,973 - __main__ - INFO - 基础任务: 通过
2025-07-11 17:04:30,973 - __main__ - INFO - 任务流: 通过
2025-07-11 17:04:30,974 - __main__ - INFO - 工作流: 通过
2025-07-11 17:04:30,974 - __main__ - INFO - 
总计: 4 个测试通过, 0 个测试失败
2025-07-11 17:04:30,974 - __main__ - INFO - 🎉 所有测试通过！新任务模块架构工作正常。
