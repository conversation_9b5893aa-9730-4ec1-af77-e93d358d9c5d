{"id": "battle_auto", "name": "自动战斗", "description": "自动进行战斗", "category": "combat", "enabled": true, "priority": 3, "config": {"task_info": {"name": "自动战斗", "description": "自动进行战斗", "version": "0_0001", "enabled": true, "priority": 3}, "triggers": [{"id": "battle_start", "type": "text_recognition", "target_text": "开始战斗", "confidence": 0.8, "region": {"x": 0, "y": 0, "width": 1920, "height": 1080}}, {"id": "auto_battle", "type": "text_recognition", "target_text": "自动", "confidence": 0.8, "region": {"x": 0, "y": 0, "width": 1920, "height": 1080}}], "actions": [{"id": "start_battle", "type": "click", "position": {"type": "auto", "target_text": "开始战斗"}, "timing": {"delay_before": 1.0, "delay_after": 3.0}}, {"id": "enable_auto", "type": "click", "position": {"type": "auto", "target_text": "自动"}, "timing": {"delay_before": 2.0, "delay_after": 1.0}}], "behaviors": [{"id": "battle_start_behavior", "type": "click", "enabled": true, "delay_before": 1.0, "delay_after": 2.0, "parameters": {"click_type": "left", "position": null, "offset_x": 0, "offset_y": 0}}, {"id": "auto_enable_behavior", "type": "click", "enabled": true, "delay_before": 0.5, "delay_after": 1.0, "parameters": {"click_type": "left", "position": null, "offset_x": 0, "offset_y": 0}}], "flow_control": {"execution_order": ["battle_start", "auto_battle"], "loop_enabled": false, "loop_count": 1, "continue_on_error": false}, "conditions": {"time_range": {"start": "00:00", "end": "23:59"}, "cooldown": 300, "max_executions_per_day": 50}}, "created_time": "2025-07-11T15:43:00", "modified_time": "2025-07-11T15:43:00"}