# -*- coding: utf-8 -*-
"""
任务编辑器组件
提供各种任务类型的参数配置界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from task.base_task import BaseTask, TaskConfig, create_task_config
    from task.config_manager import ConfigManager
    TASK_MODULE_AVAILABLE = True
except ImportError:
    TASK_MODULE_AVAILABLE = False


class TaskEditorDialog:
    """任务编辑器对话框"""
    
    def __init__(self, parent, task_config: Dict[str, Any] = None, task_type: str = "basic"):
        """
        初始化对话框
        
        Args:
            parent: 父窗口
            task_config: 现有任务配置
            task_type: 任务类型
        """
        self.parent = parent
        self.task_config = task_config or {}
        self.task_type = task_type
        self.result = None
        self.config_manager = None
        
        # 初始化配置管理器
        if TASK_MODULE_AVAILABLE:
            try:
                self.config_manager = ConfigManager()
            except Exception as e:
                print(f"配置管理器初始化失败: {e}")
        
        self._create_dialog()
    
    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("任务编辑器")
        self.dialog.geometry("600x700")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 基本信息标签页
        self._create_basic_info_tab()
        
        # 参数配置标签页
        self._create_parameters_tab()
        
        # 触发器标签页
        self._create_triggers_tab()
        
        # 验证标签页
        self._create_validation_tab()
        
        # 按钮
        self._create_buttons(main_frame)
        
        # 加载现有配置
        if self.task_config:
            self._load_config()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _create_basic_info_tab(self):
        """创建基本信息标签页"""
        self.basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.basic_frame, text="基本信息")
        
        # 任务基本信息
        info_frame = ttk.LabelFrame(self.basic_frame, text="任务信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 任务ID
        ttk.Label(info_frame, text="任务ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.task_id_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.task_id_var).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # 任务名称
        ttk.Label(info_frame, text="任务名称:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.task_name_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.task_name_var).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # 任务描述
        ttk.Label(info_frame, text="任务描述:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.task_desc_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.task_desc_var).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # 任务类型
        ttk.Label(info_frame, text="任务类型:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.task_type_var = tk.StringVar(value=self.task_type)
        self.task_type_combo = ttk.Combobox(info_frame, textvariable=self.task_type_var,
                                          values=["click", "wait", "ocr", "swipe", "key", "custom"],
                                          state="readonly")
        self.task_type_combo.grid(row=3, column=1, sticky=tk.EW, padx=5, pady=5)
        self.task_type_combo.bind('<<ComboboxSelected>>', self._on_task_type_changed)
        
        # 任务分类
        ttk.Label(info_frame, text="任务分类:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.category_var = tk.StringVar(value="basic")
        self.category_combo = ttk.Combobox(info_frame, textvariable=self.category_var,
                                         values=["basic", "daily", "battle", "system"],
                                         state="readonly")
        self.category_combo.grid(row=4, column=1, sticky=tk.EW, padx=5, pady=5)
        
        info_frame.grid_columnconfigure(1, weight=1)
        
        # 任务选项
        options_frame = ttk.LabelFrame(self.basic_frame, text="任务选项")
        options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 启用状态
        self.enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="启用任务", variable=self.enabled_var).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        # 优先级
        ttk.Label(options_frame, text="优先级:").grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.priority_var = tk.IntVar(value=1)
        ttk.Spinbox(options_frame, from_=1, to=10, textvariable=self.priority_var, width=5).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 超时时间
        ttk.Label(options_frame, text="超时时间(秒):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.timeout_var = tk.DoubleVar(value=10.0)
        ttk.Spinbox(options_frame, from_=0.1, to=300.0, increment=0.1, textvariable=self.timeout_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 重试次数
        ttk.Label(options_frame, text="重试次数:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.retry_count_var = tk.IntVar(value=3)
        ttk.Spinbox(options_frame, from_=0, to=10, textvariable=self.retry_count_var, width=5).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 重试延迟
        ttk.Label(options_frame, text="重试延迟(秒):").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.retry_delay_var = tk.DoubleVar(value=1.0)
        ttk.Spinbox(options_frame, from_=0.0, to=60.0, increment=0.1, textvariable=self.retry_delay_var, width=10).grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)
    
    def _create_parameters_tab(self):
        """创建参数配置标签页"""
        self.params_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.params_frame, text="参数配置")
        
        # 参数类型选择
        type_frame = ttk.Frame(self.params_frame)
        type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(type_frame, text="配置方式:").pack(side=tk.LEFT)
        self.param_mode_var = tk.StringVar(value="form")
        ttk.Radiobutton(type_frame, text="表单模式", variable=self.param_mode_var, 
                       value="form", command=self._on_param_mode_changed).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Radiobutton(type_frame, text="JSON模式", variable=self.param_mode_var,
                       value="json", command=self._on_param_mode_changed).pack(side=tk.LEFT, padx=5)
        
        # 表单模式框架
        self.form_frame = ttk.LabelFrame(self.params_frame, text="参数表单")
        self.form_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # JSON模式框架
        self.json_frame = ttk.LabelFrame(self.params_frame, text="JSON配置")
        
        # 创建JSON编辑器
        json_edit_frame = ttk.Frame(self.json_frame)
        json_edit_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.params_text = tk.Text(json_edit_frame, wrap=tk.WORD)
        json_scrollbar = ttk.Scrollbar(json_edit_frame, orient=tk.VERTICAL, command=self.params_text.yview)
        self.params_text.configure(yscrollcommand=json_scrollbar.set)
        
        self.params_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        json_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # JSON操作按钮
        json_buttons = ttk.Frame(self.json_frame)
        json_buttons.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(json_buttons, text="验证JSON", command=self._validate_json).pack(side=tk.LEFT, padx=2)
        ttk.Button(json_buttons, text="格式化", command=self._format_json).pack(side=tk.LEFT, padx=2)
        ttk.Button(json_buttons, text="从模板加载", command=self._load_template).pack(side=tk.LEFT, padx=2)
        
        # 默认显示表单模式
        self._on_param_mode_changed()
        
        # 创建特定任务类型的表单
        self._create_task_specific_forms()
    
    def _create_task_specific_forms(self):
        """创建特定任务类型的表单"""
        # 清空表单框架
        for widget in self.form_frame.winfo_children():
            widget.destroy()
        
        task_type = self.task_type_var.get()
        
        if task_type == "click":
            self._create_click_task_form()
        elif task_type == "wait":
            self._create_wait_task_form()
        elif task_type == "ocr":
            self._create_ocr_task_form()
        elif task_type == "swipe":
            self._create_swipe_task_form()
        elif task_type == "key":
            self._create_key_task_form()
        else:
            # 通用表单
            ttk.Label(self.form_frame, text="请使用JSON模式配置此类型任务的参数").pack(pady=20)
    
    def _create_click_task_form(self):
        """创建点击任务表单"""
        # 位置配置
        pos_frame = ttk.LabelFrame(self.form_frame, text="点击位置")
        pos_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 位置类型
        ttk.Label(pos_frame, text="位置类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.pos_type_var = tk.StringVar(value="absolute")
        pos_type_combo = ttk.Combobox(pos_frame, textvariable=self.pos_type_var,
                                    values=["absolute", "relative", "auto"],
                                    state="readonly")
        pos_type_combo.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        pos_type_combo.bind('<<ComboboxSelected>>', self._on_pos_type_changed)
        
        # 绝对坐标
        self.abs_frame = ttk.Frame(pos_frame)
        ttk.Label(self.abs_frame, text="X坐标:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.click_x_var = tk.IntVar(value=100)
        ttk.Spinbox(self.abs_frame, from_=0, to=9999, textvariable=self.click_x_var, width=8).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(self.abs_frame, text="Y坐标:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.click_y_var = tk.IntVar(value=100)
        ttk.Spinbox(self.abs_frame, from_=0, to=9999, textvariable=self.click_y_var, width=8).grid(row=0, column=3, padx=5, pady=2)
        
        # 相对坐标
        self.rel_frame = ttk.Frame(pos_frame)
        ttk.Label(self.rel_frame, text="X百分比:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.click_x_percent_var = tk.DoubleVar(value=0.5)
        ttk.Spinbox(self.rel_frame, from_=0.0, to=1.0, increment=0.01, textvariable=self.click_x_percent_var, width=8).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(self.rel_frame, text="Y百分比:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.click_y_percent_var = tk.DoubleVar(value=0.5)
        ttk.Spinbox(self.rel_frame, from_=0.0, to=1.0, increment=0.01, textvariable=self.click_y_percent_var, width=8).grid(row=0, column=3, padx=5, pady=2)
        
        # 自动定位
        self.auto_frame = ttk.Frame(pos_frame)
        ttk.Label(self.auto_frame, text="目标文字:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.target_text_var = tk.StringVar()
        ttk.Entry(self.auto_frame, textvariable=self.target_text_var, width=20).grid(row=0, column=1, padx=5, pady=2)
        
        self._on_pos_type_changed()
        
        # 点击选项
        click_frame = ttk.LabelFrame(self.form_frame, text="点击选项")
        click_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 点击类型
        ttk.Label(click_frame, text="点击类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.click_type_var = tk.StringVar(value="left")
        ttk.Combobox(click_frame, textvariable=self.click_type_var,
                    values=["left", "right", "middle"], state="readonly").grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 双击
        self.double_click_var = tk.BooleanVar()
        ttk.Checkbutton(click_frame, text="双击", variable=self.double_click_var).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 延迟设置
        ttk.Label(click_frame, text="点击前延迟:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.click_delay_before_var = tk.DoubleVar(value=0.5)
        ttk.Spinbox(click_frame, from_=0.0, to=10.0, increment=0.1, textvariable=self.click_delay_before_var, width=8).grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(click_frame, text="点击后延迟:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.click_delay_after_var = tk.DoubleVar(value=0.5)
        ttk.Spinbox(click_frame, from_=0.0, to=10.0, increment=0.1, textvariable=self.click_delay_after_var, width=8).grid(row=1, column=3, padx=5, pady=5)
    
    def _create_wait_task_form(self):
        """创建等待任务表单"""
        # 等待类型
        wait_frame = ttk.LabelFrame(self.form_frame, text="等待配置")
        wait_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(wait_frame, text="等待类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.wait_type_var = tk.StringVar(value="time")
        wait_type_combo = ttk.Combobox(wait_frame, textvariable=self.wait_type_var,
                                     values=["time", "text", "image"], state="readonly")
        wait_type_combo.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        wait_type_combo.bind('<<ComboboxSelected>>', self._on_wait_type_changed)
        
        # 时间等待
        self.time_wait_frame = ttk.Frame(wait_frame)
        ttk.Label(self.time_wait_frame, text="等待时间(秒):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.wait_duration_var = tk.DoubleVar(value=1.0)
        ttk.Spinbox(self.time_wait_frame, from_=0.1, to=300.0, increment=0.1, textvariable=self.wait_duration_var, width=10).grid(row=0, column=1, padx=5, pady=5)
        
        # 文字等待
        self.text_wait_frame = ttk.Frame(wait_frame)
        ttk.Label(self.text_wait_frame, text="目标文字:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.wait_text_var = tk.StringVar()
        ttk.Entry(self.text_wait_frame, textvariable=self.wait_text_var, width=20).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.text_wait_frame, text="最大等待时间:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_wait_time_var = tk.DoubleVar(value=30.0)
        ttk.Spinbox(self.text_wait_frame, from_=1.0, to=300.0, increment=1.0, textvariable=self.max_wait_time_var, width=10).grid(row=1, column=1, padx=5, pady=5)
        
        # 图像等待
        self.image_wait_frame = ttk.Frame(wait_frame)
        ttk.Label(self.image_wait_frame, text="目标图像:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.wait_image_var = tk.StringVar()
        ttk.Entry(self.image_wait_frame, textvariable=self.wait_image_var, width=20).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(self.image_wait_frame, text="选择", command=self._select_wait_image).grid(row=0, column=2, padx=5, pady=5)
        
        self._on_wait_type_changed()
        
        wait_frame.grid_columnconfigure(1, weight=1)

    def _create_ocr_task_form(self):
        """创建OCR任务表单"""
        # OCR区域配置
        ocr_frame = ttk.LabelFrame(self.form_frame, text="OCR配置")
        ocr_frame.pack(fill=tk.X, padx=5, pady=5)

        # 识别区域
        ttk.Label(ocr_frame, text="识别区域:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        region_frame = ttk.Frame(ocr_frame)
        region_frame.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        ttk.Label(region_frame, text="X:").grid(row=0, column=0, padx=2)
        self.ocr_x_var = tk.IntVar(value=0)
        ttk.Spinbox(region_frame, from_=0, to=9999, textvariable=self.ocr_x_var, width=6).grid(row=0, column=1, padx=2)

        ttk.Label(region_frame, text="Y:").grid(row=0, column=2, padx=2)
        self.ocr_y_var = tk.IntVar(value=0)
        ttk.Spinbox(region_frame, from_=0, to=9999, textvariable=self.ocr_y_var, width=6).grid(row=0, column=3, padx=2)

        ttk.Label(region_frame, text="宽:").grid(row=0, column=4, padx=2)
        self.ocr_width_var = tk.IntVar(value=100)
        ttk.Spinbox(region_frame, from_=1, to=9999, textvariable=self.ocr_width_var, width=6).grid(row=0, column=5, padx=2)

        ttk.Label(region_frame, text="高:").grid(row=0, column=6, padx=2)
        self.ocr_height_var = tk.IntVar(value=50)
        ttk.Spinbox(region_frame, from_=1, to=9999, textvariable=self.ocr_height_var, width=6).grid(row=0, column=7, padx=2)

        # 目标文字
        ttk.Label(ocr_frame, text="目标文字:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.ocr_target_var = tk.StringVar()
        ttk.Entry(ocr_frame, textvariable=self.ocr_target_var).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)

        # 置信度
        ttk.Label(ocr_frame, text="置信度:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.ocr_confidence_var = tk.DoubleVar(value=0.8)
        ttk.Spinbox(ocr_frame, from_=0.1, to=1.0, increment=0.1, textvariable=self.ocr_confidence_var, width=8).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        ocr_frame.grid_columnconfigure(1, weight=1)

    def _create_swipe_task_form(self):
        """创建滑动任务表单"""
        # 滑动配置
        swipe_frame = ttk.LabelFrame(self.form_frame, text="滑动配置")
        swipe_frame.pack(fill=tk.X, padx=5, pady=5)

        # 起始位置
        ttk.Label(swipe_frame, text="起始位置:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        start_frame = ttk.Frame(swipe_frame)
        start_frame.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        ttk.Label(start_frame, text="X:").grid(row=0, column=0, padx=2)
        self.swipe_start_x_var = tk.IntVar(value=100)
        ttk.Spinbox(start_frame, from_=0, to=9999, textvariable=self.swipe_start_x_var, width=8).grid(row=0, column=1, padx=2)

        ttk.Label(start_frame, text="Y:").grid(row=0, column=2, padx=2)
        self.swipe_start_y_var = tk.IntVar(value=100)
        ttk.Spinbox(start_frame, from_=0, to=9999, textvariable=self.swipe_start_y_var, width=8).grid(row=0, column=3, padx=2)

        # 结束位置
        ttk.Label(swipe_frame, text="结束位置:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        end_frame = ttk.Frame(swipe_frame)
        end_frame.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)

        ttk.Label(end_frame, text="X:").grid(row=0, column=0, padx=2)
        self.swipe_end_x_var = tk.IntVar(value=200)
        ttk.Spinbox(end_frame, from_=0, to=9999, textvariable=self.swipe_end_x_var, width=8).grid(row=0, column=1, padx=2)

        ttk.Label(end_frame, text="Y:").grid(row=0, column=2, padx=2)
        self.swipe_end_y_var = tk.IntVar(value=100)
        ttk.Spinbox(end_frame, from_=0, to=9999, textvariable=self.swipe_end_y_var, width=8).grid(row=0, column=3, padx=2)

        # 滑动时间
        ttk.Label(swipe_frame, text="滑动时间(秒):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.swipe_duration_var = tk.DoubleVar(value=1.0)
        ttk.Spinbox(swipe_frame, from_=0.1, to=10.0, increment=0.1, textvariable=self.swipe_duration_var, width=8).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        swipe_frame.grid_columnconfigure(1, weight=1)

    def _create_key_task_form(self):
        """创建按键任务表单"""
        # 按键配置
        key_frame = ttk.LabelFrame(self.form_frame, text="按键配置")
        key_frame.pack(fill=tk.X, padx=5, pady=5)

        # 按键类型
        ttk.Label(key_frame, text="按键类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.key_type_var = tk.StringVar(value="single")
        ttk.Combobox(key_frame, textvariable=self.key_type_var,
                    values=["single", "combination", "text"], state="readonly").grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 按键值
        ttk.Label(key_frame, text="按键值:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.key_value_var = tk.StringVar()
        ttk.Entry(key_frame, textvariable=self.key_value_var).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)

        # 按键时间
        ttk.Label(key_frame, text="按键时间(秒):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.key_duration_var = tk.DoubleVar(value=0.1)
        ttk.Spinbox(key_frame, from_=0.01, to=5.0, increment=0.01, textvariable=self.key_duration_var, width=8).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        key_frame.grid_columnconfigure(1, weight=1)

    def _create_triggers_tab(self):
        """创建触发器标签页"""
        self.triggers_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.triggers_frame, text="触发器")

        # 触发器列表
        triggers_list_frame = ttk.LabelFrame(self.triggers_frame, text="触发器列表")
        triggers_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview
        self.triggers_tree = ttk.Treeview(triggers_list_frame, columns=("type", "condition"), show="tree headings")
        self.triggers_tree.heading("#0", text="触发器名称")
        self.triggers_tree.heading("type", text="类型")
        self.triggers_tree.heading("condition", text="条件")

        self.triggers_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 触发器操作按钮
        triggers_buttons = ttk.Frame(self.triggers_frame)
        triggers_buttons.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(triggers_buttons, text="添加触发器", command=self._add_trigger).pack(side=tk.LEFT, padx=2)
        ttk.Button(triggers_buttons, text="编辑触发器", command=self._edit_trigger).pack(side=tk.LEFT, padx=2)
        ttk.Button(triggers_buttons, text="删除触发器", command=self._delete_trigger).pack(side=tk.LEFT, padx=2)

    def _create_validation_tab(self):
        """创建验证标签页"""
        self.validation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.validation_frame, text="验证")

        # 验证配置
        validation_config_frame = ttk.LabelFrame(self.validation_frame, text="验证配置")
        validation_config_frame.pack(fill=tk.X, padx=5, pady=5)

        # 期望结果
        ttk.Label(validation_config_frame, text="期望结果:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.expected_result_var = tk.StringVar(value="success")
        ttk.Combobox(validation_config_frame, textvariable=self.expected_result_var,
                    values=["success", "failure", "custom"], state="readonly").grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 验证超时
        ttk.Label(validation_config_frame, text="验证超时(秒):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.validation_timeout_var = tk.DoubleVar(value=5.0)
        ttk.Spinbox(validation_config_frame, from_=0.1, to=60.0, increment=0.1, textvariable=self.validation_timeout_var, width=8).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # 验证结果显示
        validation_result_frame = ttk.LabelFrame(self.validation_frame, text="验证结果")
        validation_result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.validation_text = tk.Text(validation_result_frame, height=10, wrap=tk.WORD, state=tk.DISABLED)
        validation_scrollbar = ttk.Scrollbar(validation_result_frame, orient=tk.VERTICAL, command=self.validation_text.yview)
        self.validation_text.configure(yscrollcommand=validation_scrollbar.set)

        self.validation_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        validation_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

        # 验证按钮
        validation_buttons = ttk.Frame(self.validation_frame)
        validation_buttons.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(validation_buttons, text="验证配置", command=self._validate_config).pack(side=tk.LEFT, padx=2)
        ttk.Button(validation_buttons, text="测试执行", command=self._test_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(validation_buttons, text="清空结果", command=self._clear_validation).pack(side=tk.RIGHT, padx=2)

    def _create_buttons(self, parent):
        """创建按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="确定", command=self._on_ok).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="应用", command=self._on_apply).pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(button_frame, text="重置", command=self._on_reset).pack(side=tk.LEFT)

    def _on_task_type_changed(self, event=None):
        """任务类型改变事件"""
        self._create_task_specific_forms()
        self._update_json_from_form()

    def _on_param_mode_changed(self):
        """参数模式改变事件"""
        if self.param_mode_var.get() == "form":
            self.json_frame.pack_forget()
            self.form_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            self._update_json_from_form()
        else:
            self.form_frame.pack_forget()
            self.json_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            self._update_form_from_json()

    def _on_pos_type_changed(self, event=None):
        """位置类型改变事件"""
        # 隐藏所有位置框架
        self.abs_frame.grid_forget()
        self.rel_frame.grid_forget()
        self.auto_frame.grid_forget()

        # 显示对应的框架
        pos_type = self.pos_type_var.get()
        if pos_type == "absolute":
            self.abs_frame.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        elif pos_type == "relative":
            self.rel_frame.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        elif pos_type == "auto":
            self.auto_frame.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)

    def _on_wait_type_changed(self, event=None):
        """等待类型改变事件"""
        # 隐藏所有等待框架
        self.time_wait_frame.grid_forget()
        self.text_wait_frame.grid_forget()
        self.image_wait_frame.grid_forget()

        # 显示对应的框架
        wait_type = self.wait_type_var.get()
        if wait_type == "time":
            self.time_wait_frame.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        elif wait_type == "text":
            self.text_wait_frame.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        elif wait_type == "image":
            self.image_wait_frame.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)

    def _select_wait_image(self):
        """选择等待图像"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.png *.jpg *.jpeg *.bmp *.gif"), ("所有文件", "*.*")]
        )
        if file_path:
            self.wait_image_var.set(file_path)

    def _validate_json(self):
        """验证JSON格式"""
        try:
            json_text = self.params_text.get(1.0, tk.END).strip()
            if json_text:
                json.loads(json_text)
            messagebox.showinfo("验证结果", "JSON格式正确")
        except json.JSONDecodeError as e:
            messagebox.showerror("验证失败", f"JSON格式错误:\n{str(e)}")

    def _format_json(self):
        """格式化JSON"""
        try:
            json_text = self.params_text.get(1.0, tk.END).strip()
            if json_text:
                parsed = json.loads(json_text)
                formatted = json.dumps(parsed, ensure_ascii=False, indent=2)
                self.params_text.delete(1.0, tk.END)
                self.params_text.insert(tk.END, formatted)
        except json.JSONDecodeError as e:
            messagebox.showerror("格式化失败", f"JSON格式错误:\n{str(e)}")

    def _load_template(self):
        """从模板加载参数"""
        if not self.config_manager:
            messagebox.showwarning("警告", "配置管理器未初始化")
            return

        templates = self.config_manager.get_available_templates()
        if not templates:
            messagebox.showinfo("提示", "没有可用的模板")
            return

        # 创建模板选择对话框
        template_dialog = tk.Toplevel(self.dialog)
        template_dialog.title("选择模板")
        template_dialog.geometry("400x300")
        template_dialog.transient(self.dialog)
        template_dialog.grab_set()

        # 模板列表
        template_listbox = tk.Listbox(template_dialog)
        template_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for template in templates:
            template_listbox.insert(tk.END, f"{template['name']} ({template['category']})")

        # 按钮
        button_frame = ttk.Frame(template_dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def load_selected():
            selection = template_listbox.curselection()
            if selection:
                template = templates[selection[0]]
                try:
                    config = self.config_manager.create_config_from_template(template['template_id'])
                    params_json = json.dumps(config.get('parameters', {}), ensure_ascii=False, indent=2)
                    self.params_text.delete(1.0, tk.END)
                    self.params_text.insert(tk.END, params_json)
                    template_dialog.destroy()
                except Exception as e:
                    messagebox.showerror("错误", f"加载模板失败:\n{str(e)}")

        ttk.Button(button_frame, text="加载", command=load_selected).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=template_dialog.destroy).pack(side=tk.RIGHT)

    def _add_trigger(self):
        """添加触发器"""
        messagebox.showinfo("提示", "添加触发器功能开发中")

    def _edit_trigger(self):
        """编辑触发器"""
        messagebox.showinfo("提示", "编辑触发器功能开发中")

    def _delete_trigger(self):
        """删除触发器"""
        messagebox.showinfo("提示", "删除触发器功能开发中")

    def _validate_config(self):
        """验证配置"""
        try:
            config = self._build_config()

            # 使用配置管理器验证
            if self.config_manager:
                is_valid, errors = self.config_manager.validator.validate_config(config, self.task_type)

                self.validation_text.config(state=tk.NORMAL)
                self.validation_text.delete(1.0, tk.END)

                if is_valid:
                    self.validation_text.insert(tk.END, "✅ 配置验证通过\n\n")
                    self.validation_text.insert(tk.END, "配置详情:\n")
                    self.validation_text.insert(tk.END, json.dumps(config, ensure_ascii=False, indent=2))
                else:
                    self.validation_text.insert(tk.END, "❌ 配置验证失败\n\n")
                    self.validation_text.insert(tk.END, "错误列表:\n")
                    for error in errors:
                        self.validation_text.insert(tk.END, f"- {error}\n")

                self.validation_text.config(state=tk.DISABLED)
            else:
                messagebox.showinfo("验证结果", "配置管理器未初始化，无法验证")

        except Exception as e:
            self.validation_text.config(state=tk.NORMAL)
            self.validation_text.delete(1.0, tk.END)
            self.validation_text.insert(tk.END, f"❌ 验证过程出错:\n{str(e)}")
            self.validation_text.config(state=tk.DISABLED)

    def _test_task(self):
        """测试任务执行"""
        messagebox.showinfo("提示", "测试执行功能开发中")

    def _clear_validation(self):
        """清空验证结果"""
        self.validation_text.config(state=tk.NORMAL)
        self.validation_text.delete(1.0, tk.END)
        self.validation_text.config(state=tk.DISABLED)

    def _update_json_from_form(self):
        """从表单更新JSON"""
        if self.param_mode_var.get() != "json":
            return

        try:
            parameters = self._build_parameters_from_form()
            json_text = json.dumps(parameters, ensure_ascii=False, indent=2)
            self.params_text.delete(1.0, tk.END)
            self.params_text.insert(tk.END, json_text)
        except Exception as e:
            print(f"更新JSON失败: {e}")

    def _update_form_from_json(self):
        """从JSON更新表单"""
        if self.param_mode_var.get() != "form":
            return

        try:
            json_text = self.params_text.get(1.0, tk.END).strip()
            if json_text:
                parameters = json.loads(json_text)
                self._load_parameters_to_form(parameters)
        except Exception as e:
            print(f"从JSON更新表单失败: {e}")

    def _build_parameters_from_form(self) -> Dict[str, Any]:
        """从表单构建参数"""
        parameters = {}
        task_type = self.task_type_var.get()

        if task_type == "click":
            # 位置参数
            position = {"type": self.pos_type_var.get()}

            if position["type"] == "absolute":
                position.update({
                    "x": self.click_x_var.get(),
                    "y": self.click_y_var.get()
                })
            elif position["type"] == "relative":
                position.update({
                    "x_percent": self.click_x_percent_var.get(),
                    "y_percent": self.click_y_percent_var.get()
                })
            elif position["type"] == "auto":
                position["target_text"] = self.target_text_var.get()

            parameters.update({
                "position": position,
                "click_type": self.click_type_var.get(),
                "double_click": self.double_click_var.get(),
                "delay_before": self.click_delay_before_var.get(),
                "delay_after": self.click_delay_after_var.get()
            })

        elif task_type == "wait":
            parameters["wait_type"] = self.wait_type_var.get()

            if parameters["wait_type"] == "time":
                parameters["duration"] = self.wait_duration_var.get()
            elif parameters["wait_type"] == "text":
                parameters.update({
                    "target_text": self.wait_text_var.get(),
                    "max_wait_time": self.max_wait_time_var.get()
                })
            elif parameters["wait_type"] == "image":
                parameters.update({
                    "target_image": self.wait_image_var.get(),
                    "max_wait_time": self.max_wait_time_var.get()
                })

        elif task_type == "ocr":
            parameters.update({
                "region": {
                    "x": self.ocr_x_var.get(),
                    "y": self.ocr_y_var.get(),
                    "width": self.ocr_width_var.get(),
                    "height": self.ocr_height_var.get()
                },
                "target_text": self.ocr_target_var.get(),
                "confidence": self.ocr_confidence_var.get()
            })

        elif task_type == "swipe":
            parameters.update({
                "start_position": {
                    "x": self.swipe_start_x_var.get(),
                    "y": self.swipe_start_y_var.get()
                },
                "end_position": {
                    "x": self.swipe_end_x_var.get(),
                    "y": self.swipe_end_y_var.get()
                },
                "duration": self.swipe_duration_var.get()
            })

        elif task_type == "key":
            parameters.update({
                "key_type": self.key_type_var.get(),
                "key_value": self.key_value_var.get(),
                "duration": self.key_duration_var.get()
            })

        return parameters

    def _load_parameters_to_form(self, parameters: Dict[str, Any]):
        """将参数加载到表单"""
        task_type = self.task_type_var.get()

        try:
            if task_type == "click" and "position" in parameters:
                position = parameters["position"]
                self.pos_type_var.set(position.get("type", "absolute"))

                if position["type"] == "absolute":
                    self.click_x_var.set(position.get("x", 100))
                    self.click_y_var.set(position.get("y", 100))
                elif position["type"] == "relative":
                    self.click_x_percent_var.set(position.get("x_percent", 0.5))
                    self.click_y_percent_var.set(position.get("y_percent", 0.5))
                elif position["type"] == "auto":
                    self.target_text_var.set(position.get("target_text", ""))

                self._on_pos_type_changed()

                self.click_type_var.set(parameters.get("click_type", "left"))
                self.double_click_var.set(parameters.get("double_click", False))
                self.click_delay_before_var.set(parameters.get("delay_before", 0.5))
                self.click_delay_after_var.set(parameters.get("delay_after", 0.5))

            elif task_type == "wait":
                self.wait_type_var.set(parameters.get("wait_type", "time"))

                if parameters["wait_type"] == "time":
                    self.wait_duration_var.set(parameters.get("duration", 1.0))
                elif parameters["wait_type"] == "text":
                    self.wait_text_var.set(parameters.get("target_text", ""))
                    self.max_wait_time_var.set(parameters.get("max_wait_time", 30.0))
                elif parameters["wait_type"] == "image":
                    self.wait_image_var.set(parameters.get("target_image", ""))
                    self.max_wait_time_var.set(parameters.get("max_wait_time", 30.0))

                self._on_wait_type_changed()

            # 其他任务类型的参数加载...

        except Exception as e:
            print(f"加载参数到表单失败: {e}")

    def _build_config(self) -> Dict[str, Any]:
        """构建完整的任务配置"""
        # 获取参数
        if self.param_mode_var.get() == "form":
            parameters = self._build_parameters_from_form()
        else:
            json_text = self.params_text.get(1.0, tk.END).strip()
            parameters = json.loads(json_text) if json_text else {}

        # 构建配置
        config = {
            "id": self.task_id_var.get(),
            "name": self.task_name_var.get(),
            "description": self.task_desc_var.get(),
            "type": self.task_type_var.get(),
            "category": self.category_var.get(),
            "enabled": self.enabled_var.get(),
            "priority": self.priority_var.get(),
            "timeout": self.timeout_var.get(),
            "retry_count": self.retry_count_var.get(),
            "retry_delay": self.retry_delay_var.get(),
            "parameters": parameters,
            "triggers": [],  # 触发器暂时为空
            "validation": {
                "expected_result": self.expected_result_var.get(),
                "timeout": self.validation_timeout_var.get()
            }
        }

        return config

    def _load_config(self):
        """加载现有配置"""
        config = self.task_config

        # 加载基本信息
        self.task_id_var.set(config.get("id", ""))
        self.task_name_var.set(config.get("name", ""))
        self.task_desc_var.set(config.get("description", ""))
        self.task_type_var.set(config.get("type", "basic"))
        self.category_var.set(config.get("category", "basic"))

        # 加载选项
        self.enabled_var.set(config.get("enabled", True))
        self.priority_var.set(config.get("priority", 1))
        self.timeout_var.set(config.get("timeout", 10.0))
        self.retry_count_var.set(config.get("retry_count", 3))
        self.retry_delay_var.set(config.get("retry_delay", 1.0))

        # 加载验证配置
        validation = config.get("validation", {})
        self.expected_result_var.set(validation.get("expected_result", "success"))
        self.validation_timeout_var.set(validation.get("timeout", 5.0))

        # 重新创建表单
        self._create_task_specific_forms()

        # 加载参数
        parameters = config.get("parameters", {})
        if parameters:
            # 加载到JSON编辑器
            json_text = json.dumps(parameters, ensure_ascii=False, indent=2)
            self.params_text.delete(1.0, tk.END)
            self.params_text.insert(tk.END, json_text)

            # 加载到表单
            self._load_parameters_to_form(parameters)

    def _on_ok(self):
        """确定按钮事件"""
        try:
            self.result = self._build_config()
            self.dialog.destroy()
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败:\n{str(e)}")

    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()

    def _on_apply(self):
        """应用按钮事件"""
        try:
            self.result = self._build_config()
            messagebox.showinfo("成功", "配置已应用")
        except Exception as e:
            messagebox.showerror("错误", f"应用配置失败:\n{str(e)}")

    def _on_reset(self):
        """重置按钮事件"""
        if messagebox.askyesno("确认重置", "确定要重置所有配置吗？"):
            # 重新加载原始配置
            if self.task_config:
                self._load_config()
            else:
                # 重置为默认值
                self.task_id_var.set("")
                self.task_name_var.set("")
                self.task_desc_var.set("")
                self.params_text.delete(1.0, tk.END)
                self.params_text.insert(tk.END, "{}")

    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result
