# -*- coding: utf-8 -*-
"""
任务库管理器
管理所有可用的任务配置
"""

import os
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass
import uuid

from .config_manager import ConfigManager


@dataclass
class TaskTemplate:
    """任务模板数据类"""
    id: str
    name: str
    description: str
    category: str
    enabled: bool
    priority: int
    config: Dict[str, Any]
    created_time: str
    modified_time: str


class TaskLibrary:
    """任务库管理器类"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None, 
                 library_dir: str = "config/task_library"):
        """
        初始化任务库管理器
        
        Args:
            config_manager: 配置管理器实例
            library_dir: 任务库目录
        """
        self.config_manager = config_manager or ConfigManager()
        self.library_dir = Path(library_dir)
        self.tasks = {}
        
        # 确保任务库目录存在
        self.library_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载所有任务
        self.load_all_tasks()
    
    def load_all_tasks(self) -> Dict[str, TaskTemplate]:
        """
        加载所有任务模板
        
        Returns:
            任务模板字典
        """
        self.tasks.clear()
        
        # 从任务库目录加载
        for task_file in self.library_dir.glob("*.json"):
            try:
                task_template = self._load_task_from_file(task_file)
                if task_template:
                    self.tasks[task_template.id] = task_template
                    print(f"任务模板已加载: {task_template.id} - {task_template.name}")
            except Exception as e:
                print(f"加载任务模板失败: {task_file} - {e}")
        
        print(f"共加载 {len(self.tasks)} 个任务模板")
        return self.tasks
    
    def _load_task_from_file(self, file_path: Path) -> Optional[TaskTemplate]:
        """
        从文件加载任务模板
        
        Args:
            file_path: 文件路径
            
        Returns:
            任务模板对象
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 验证必需字段
            required_fields = ['id', 'name', 'config']
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            return TaskTemplate(
                id=data['id'],
                name=data['name'],
                description=data.get('description', ''),
                category=data.get('category', 'default'),
                enabled=data.get('enabled', True),
                priority=data.get('priority', 1),
                config=data['config'],
                created_time=data.get('created_time', ''),
                modified_time=data.get('modified_time', '')
            )
            
        except Exception as e:
            print(f"解析任务文件失败: {file_path} - {e}")
            return None
    
    def get_task(self, task_id: str) -> Optional[TaskTemplate]:
        """
        获取指定任务模板
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务模板
        """
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, TaskTemplate]:
        """获取所有任务模板"""
        return self.tasks.copy()
    
    def get_tasks_by_category(self, category: str) -> Dict[str, TaskTemplate]:
        """
        按分类获取任务模板
        
        Args:
            category: 分类名称
            
        Returns:
            任务模板字典
        """
        return {
            task_id: task_template 
            for task_id, task_template in self.tasks.items() 
            if task_template.category == category
        }
    
    def get_enabled_tasks(self) -> Dict[str, TaskTemplate]:
        """获取所有启用的任务模板"""
        return {
            task_id: task_template 
            for task_id, task_template in self.tasks.items() 
            if task_template.enabled
        }
    
    def save_task(self, task_template: TaskTemplate) -> bool:
        """
        保存任务模板
        
        Args:
            task_template: 任务模板
            
        Returns:
            保存是否成功
        """
        try:
            # 更新修改时间
            from datetime import datetime
            task_template.modified_time = datetime.now().isoformat()
            
            # 构建保存数据
            save_data = {
                'id': task_template.id,
                'name': task_template.name,
                'description': task_template.description,
                'category': task_template.category,
                'enabled': task_template.enabled,
                'priority': task_template.priority,
                'config': task_template.config,
                'created_time': task_template.created_time,
                'modified_time': task_template.modified_time
            }
            
            # 保存到文件
            task_file = self.library_dir / f"{task_template.id}.json"
            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            # 更新内存中的任务
            self.tasks[task_template.id] = task_template
            
            print(f"任务模板已保存: {task_template.id}")
            return True
            
        except Exception as e:
            print(f"保存任务模板失败: {task_template.id} - {e}")
            return False
    
    def create_task(self, name: str, description: str = "", 
                   category: str = "default") -> TaskTemplate:
        """
        创建新任务模板
        
        Args:
            name: 任务名称
            description: 任务描述
            category: 任务分类
            
        Returns:
            新任务模板
        """
        from datetime import datetime
        
        task_id = str(uuid.uuid4())[:8]  # 使用短UUID
        current_time = datetime.now().isoformat()
        
        # 创建默认配置
        default_config = {
            "task_info": {
                "name": name,
                "description": description,
                "version": "0_0001",
                "enabled": True,
                "priority": 1
            },
            "triggers": [],
            "actions": [],
            "behaviors": [],  # 新增行为配置
            "flow_control": {
                "execution_order": [],
                "loop_enabled": False,
                "loop_count": 1,
                "continue_on_error": True
            },
            "conditions": {
                "time_range": {
                    "start": "00:00",
                    "end": "23:59"
                },
                "cooldown": 3600,
                "max_executions_per_day": 1
            }
        }
        
        task_template = TaskTemplate(
            id=task_id,
            name=name,
            description=description,
            category=category,
            enabled=True,
            priority=1,
            config=default_config,
            created_time=current_time,
            modified_time=current_time
        )
        
        return task_template
    
    def delete_task(self, task_id: str) -> bool:
        """
        删除任务模板
        
        Args:
            task_id: 任务ID
            
        Returns:
            删除是否成功
        """
        try:
            # 从内存中删除
            if task_id in self.tasks:
                del self.tasks[task_id]
            
            # 删除文件
            task_file = self.library_dir / f"{task_id}.json"
            if task_file.exists():
                task_file.unlink()
            
            print(f"任务模板已删除: {task_id}")
            return True
            
        except Exception as e:
            print(f"删除任务模板失败: {task_id} - {e}")
            return False
    
    def enable_task(self, task_id: str) -> bool:
        """
        启用任务模板
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作是否成功
        """
        return self._set_task_enabled(task_id, True)
    
    def disable_task(self, task_id: str) -> bool:
        """
        禁用任务模板
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作是否成功
        """
        return self._set_task_enabled(task_id, False)
    
    def _set_task_enabled(self, task_id: str, enabled: bool) -> bool:
        """
        设置任务启用状态
        
        Args:
            task_id: 任务ID
            enabled: 是否启用
            
        Returns:
            操作是否成功
        """
        task_template = self.tasks.get(task_id)
        if not task_template:
            print(f"任务模板不存在: {task_id}")
            return False
        
        try:
            task_template.enabled = enabled
            return self.save_task(task_template)
        except Exception as e:
            print(f"设置任务状态失败: {task_id} - {e}")
            return False
    
    def get_categories(self) -> List[str]:
        """获取所有分类"""
        categories = set()
        for task_template in self.tasks.values():
            categories.add(task_template.category)
        return sorted(list(categories))
    
    def get_library_summary(self) -> Dict[str, Any]:
        """
        获取任务库摘要信息
        
        Returns:
            任务库摘要
        """
        total_tasks = len(self.tasks)
        enabled_tasks = len(self.get_enabled_tasks())
        disabled_tasks = total_tasks - enabled_tasks
        categories = self.get_categories()
        
        return {
            "total_tasks": total_tasks,
            "enabled_tasks": enabled_tasks,
            "disabled_tasks": disabled_tasks,
            "categories": categories,
            "task_list": [
                {
                    "id": task_template.id,
                    "name": task_template.name,
                    "category": task_template.category,
                    "enabled": task_template.enabled,
                    "priority": task_template.priority
                }
                for task_template in self.tasks.values()
            ]
        }


if __name__ == "__main__":
    # 测试任务库管理器
    print("测试任务库管理器...")
    
    try:
        task_library = TaskLibrary()
        
        # 获取任务库摘要
        summary = task_library.get_library_summary()
        print(f"任务库摘要: {summary}")
        
        # 创建测试任务
        test_task = task_library.create_task("测试任务", "这是一个测试任务", "test")
        if task_library.save_task(test_task):
            print("测试任务创建成功")
        
        print("任务库管理器测试完成")
        
    except Exception as e:
        print(f"任务库管理器测试失败: {e}")
