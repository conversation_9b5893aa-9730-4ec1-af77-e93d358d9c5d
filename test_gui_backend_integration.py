# -*- coding: utf-8 -*-
"""
GUI与后端集成测试脚本
测试新的任务模块与GUI的集成功能
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_task_integration():
    """测试任务集成功能"""
    print("=" * 60)
    print("测试任务模块与GUI的集成功能")
    print("=" * 60)
    
    try:
        # 导入任务集成模块
        from task.integration import get_integration
        
        print("✅ 任务集成模块导入成功")
        
        # 获取集成实例
        integration = get_integration()
        print("✅ 任务集成实例创建成功")
        
        # 测试统计信息
        stats = integration.get_task_statistics()
        print(f"📊 任务统计信息:")
        print(f"   - 基础任务数: {stats.get('base_tasks_count', 0)}")
        print(f"   - 任务流数: {stats.get('task_flows_count', 0)}")
        print(f"   - 工作流数: {stats.get('workflows_count', 0)}")
        
        # 测试获取可用任务
        tasks = integration.get_available_tasks()
        print(f"📋 可用任务列表 ({len(tasks)} 个):")
        for task in tasks[:5]:  # 显示前5个任务
            print(f"   - {task['name']} ({task['category']})")
        
        # 测试获取任务流
        flows = integration.get_available_flows()
        print(f"🔄 可用任务流 ({len(flows)} 个):")
        for flow in flows:
            print(f"   - {flow['name']} ({flow.get('steps_count', 0)} 步骤)")
        
        # 测试获取工作流
        workflows = integration.get_available_workflows()
        print(f"⚙️ 可用工作流 ({len(workflows)} 个):")
        for workflow in workflows:
            print(f"   - {workflow['name']} ({workflow.get('steps_count', 0)} 步骤)")
        
        print("\n✅ 任务集成功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 任务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_startup():
    """测试GUI启动"""
    print("\n" + "=" * 60)
    print("测试GUI V2启动")
    print("=" * 60)
    
    try:
        # 导入GUI模块
        from src.gui.main_window_v2 import SnowZoneMainWindowV2
        
        print("✅ GUI V2模块导入成功")
        
        # 创建GUI实例（不运行mainloop）
        app = SnowZoneMainWindowV2()
        print("✅ GUI V2实例创建成功")
        
        # 测试界面数据刷新
        if app.task_integration:
            print("🔄 测试界面数据刷新...")
            app._refresh_all()
            print("✅ 界面数据刷新成功")
        else:
            print("⚠️ 任务集成未加载，跳过数据刷新测试")
        
        # 销毁窗口
        app.root.destroy()
        print("✅ GUI V2测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GUI V2测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_task_execution():
    """测试任务执行"""
    print("\n" + "=" * 60)
    print("测试任务执行功能")
    print("=" * 60)
    
    try:
        from task.integration import get_integration
        
        integration = get_integration()
        
        # 获取等待任务进行测试
        tasks = integration.get_available_tasks()
        wait_tasks = [task for task in tasks if '等待' in task['name'] or 'wait' in task['name'].lower()]
        
        if wait_tasks:
            task_id = wait_tasks[0]['id']
            task_name = wait_tasks[0]['name']
            
            print(f"🚀 测试执行任务: {task_name}")
            
            # 执行任务
            result = integration.execute_task(task_id)
            
            if result:
                print(f"✅ 任务执行完成")
                print(f"   - 状态: {result.status.value}")
                print(f"   - 消息: {result.message}")
                if result.duration:
                    print(f"   - 耗时: {result.duration:.2f}秒")
            else:
                print("❌ 任务执行失败")
                
        else:
            print("⚠️ 未找到可测试的等待任务")
        
        print("✅ 任务执行测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 任务执行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_with_backend():
    """测试GUI与后端的完整集成"""
    print("\n" + "=" * 60)
    print("测试GUI与后端的完整集成")
    print("=" * 60)
    
    try:
        from src.gui.main_window_v2 import SnowZoneMainWindowV2
        
        # 创建GUI实例
        app = SnowZoneMainWindowV2()
        
        if app.task_integration:
            print("✅ GUI与任务模块集成成功")
            
            # 测试数据加载
            print("🔄 测试数据加载...")
            app._refresh_tasks()
            app._refresh_flows()
            app._refresh_workflows()
            app._refresh_statistics()
            print("✅ 数据加载成功")
            
            # 测试日志功能
            print("📝 测试日志功能...")
            app._add_log("INFO", "这是一条测试信息")
            app._add_log("WARNING", "这是一条测试警告")
            app._add_log("ERROR", "这是一条测试错误")
            print("✅ 日志功能正常")
            
        else:
            print("⚠️ GUI与任务模块集成失败")
        
        # 销毁窗口
        app.root.destroy()
        print("✅ GUI与后端集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GUI与后端集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始SnowZone任务模块与GUI集成测试")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 1. 测试任务集成
    test_results.append(("任务集成", test_task_integration()))
    
    # 2. 测试GUI启动
    test_results.append(("GUI启动", test_gui_startup()))
    
    # 3. 测试任务执行
    test_results.append(("任务执行", test_task_execution()))
    
    # 4. 测试GUI与后端集成
    test_results.append(("GUI后端集成", test_gui_with_backend()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("🎉 所有测试通过！GUI与后端集成功能正常。")
        print("\n💡 现在可以运行以下命令启动完整的GUI V2:")
        print("   python gui_main_v2.py")
    else:
        print(f"⚠️ 有 {failed} 个测试失败，需要检查相关功能。")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
