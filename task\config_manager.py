# -*- coding: utf-8 -*-
"""
任务配置管理器
提供任务配置的动态管理、验证、模板等功能
"""

import json
import os
import uuid
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime
import jsonschema

from .base_task import TaskConfig


@dataclass
class ConfigTemplate:
    """配置模板"""
    template_id: str
    name: str
    description: str
    category: str
    schema: Dict[str, Any]
    default_values: Dict[str, Any]
    created_time: datetime
    updated_time: datetime


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.schemas = self._load_schemas()
    
    def _load_schemas(self) -> Dict[str, Dict[str, Any]]:
        """加载配置模式"""
        schemas = {
            'base_task': {
                'type': 'object',
                'required': ['id', 'name', 'category'],
                'properties': {
                    'id': {'type': 'string', 'minLength': 1},
                    'name': {'type': 'string', 'minLength': 1},
                    'description': {'type': 'string'},
                    'category': {'type': 'string', 'enum': ['basic', 'daily', 'battle', 'system']},
                    'enabled': {'type': 'boolean'},
                    'priority': {'type': 'integer', 'minimum': 1, 'maximum': 10},
                    'timeout': {'type': 'number', 'minimum': 0.1},
                    'retry_count': {'type': 'integer', 'minimum': 0, 'maximum': 10},
                    'retry_delay': {'type': 'number', 'minimum': 0},
                    'parameters': {'type': 'object'}
                }
            },
            'click_task': {
                'type': 'object',
                'allOf': [{'$ref': '#/definitions/base_task'}],
                'properties': {
                    'parameters': {
                        'type': 'object',
                        'required': ['position'],
                        'properties': {
                            'position': {
                                'type': 'object',
                                'required': ['type'],
                                'properties': {
                                    'type': {'type': 'string', 'enum': ['absolute', 'relative', 'auto']},
                                    'x': {'type': 'integer'},
                                    'y': {'type': 'integer'},
                                    'x_percent': {'type': 'number', 'minimum': 0, 'maximum': 1},
                                    'y_percent': {'type': 'number', 'minimum': 0, 'maximum': 1},
                                    'offset_x': {'type': 'integer'},
                                    'offset_y': {'type': 'integer'}
                                }
                            },
                            'click_type': {'type': 'string', 'enum': ['left', 'right', 'middle']},
                            'double_click': {'type': 'boolean'},
                            'delay_before': {'type': 'number', 'minimum': 0},
                            'delay_after': {'type': 'number', 'minimum': 0},
                            'target_text': {'type': 'string'}
                        }
                    }
                }
            },
            'wait_task': {
                'type': 'object',
                'allOf': [{'$ref': '#/definitions/base_task'}],
                'properties': {
                    'parameters': {
                        'type': 'object',
                        'required': ['wait_type'],
                        'properties': {
                            'wait_type': {'type': 'string', 'enum': ['time', 'text', 'image']},
                            'duration': {'type': 'number', 'minimum': 0},
                            'target_text': {'type': 'string'},
                            'target_image': {'type': 'string'},
                            'max_wait_time': {'type': 'number', 'minimum': 0.1},
                            'check_interval': {'type': 'number', 'minimum': 0.1}
                        }
                    }
                }
            },
            'ocr_task': {
                'type': 'object',
                'allOf': [{'$ref': '#/definitions/base_task'}],
                'properties': {
                    'parameters': {
                        'type': 'object',
                        'properties': {
                            'region': {
                                'type': 'object',
                                'required': ['x', 'y', 'width', 'height'],
                                'properties': {
                                    'x': {'type': 'integer', 'minimum': 0},
                                    'y': {'type': 'integer', 'minimum': 0},
                                    'width': {'type': 'integer', 'minimum': 1},
                                    'height': {'type': 'integer', 'minimum': 1}
                                }
                            },
                            'target_text': {'type': 'string'},
                            'confidence': {'type': 'number', 'minimum': 0, 'maximum': 1},
                            'language': {'type': 'string'},
                            'return_position': {'type': 'boolean'},
                            'preprocessing': {
                                'type': 'object',
                                'properties': {
                                    'enhance_contrast': {'type': 'boolean'},
                                    'scale_factor': {'type': 'number', 'minimum': 0.1},
                                    'grayscale': {'type': 'boolean'}
                                }
                            }
                        }
                    }
                }
            }
        }
        
        # 添加基础定义
        for schema_name, schema in schemas.items():
            if schema_name != 'base_task':
                schema['definitions'] = {'base_task': schemas['base_task']}
        
        return schemas
    
    def validate_config(self, config: Dict[str, Any], task_type: str = 'base_task') -> tuple[bool, List[str]]:
        """
        验证配置
        
        Args:
            config: 配置字典
            task_type: 任务类型
            
        Returns:
            (是否有效, 错误列表)
        """
        errors = []
        
        try:
            # 获取对应的模式
            schema = self.schemas.get(task_type, self.schemas['base_task'])
            
            # 使用jsonschema验证
            jsonschema.validate(config, schema)
            
            # 自定义验证逻辑
            custom_errors = self._custom_validation(config, task_type)
            errors.extend(custom_errors)
            
        except jsonschema.ValidationError as e:
            errors.append(f"配置验证失败: {e.message}")
        except Exception as e:
            errors.append(f"验证过程出错: {str(e)}")
        
        return len(errors) == 0, errors
    
    def _custom_validation(self, config: Dict[str, Any], task_type: str) -> List[str]:
        """自定义验证逻辑"""
        errors = []
        
        # 点击任务的特殊验证
        if task_type == 'click_task':
            params = config.get('parameters', {})
            position = params.get('position', {})
            position_type = position.get('type')
            
            if position_type == 'absolute':
                if 'x' not in position or 'y' not in position:
                    errors.append("绝对坐标类型需要指定x和y坐标")
            elif position_type == 'relative':
                if 'x_percent' not in position or 'y_percent' not in position:
                    errors.append("相对坐标类型需要指定x_percent和y_percent")
            elif position_type == 'auto':
                if not params.get('target_text'):
                    errors.append("自动定位类型需要指定target_text")
        
        # 等待任务的特殊验证
        elif task_type == 'wait_task':
            params = config.get('parameters', {})
            wait_type = params.get('wait_type')
            
            if wait_type == 'time':
                if 'duration' not in params:
                    errors.append("时间等待类型需要指定duration")
            elif wait_type == 'text':
                if not params.get('target_text'):
                    errors.append("文字等待类型需要指定target_text")
            elif wait_type == 'image':
                if not params.get('target_image'):
                    errors.append("图像等待类型需要指定target_image")
        
        return errors


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_root: str = "task"):
        """
        初始化配置管理器
        
        Args:
            config_root: 配置根目录
        """
        self.config_root = Path(config_root)
        self.json_dir = self.config_root / "json"
        self.templates_dir = self.config_root / "templates"
        
        self.validator = ConfigValidator()
        self.templates: Dict[str, ConfigTemplate] = {}
        
        # 确保目录存在
        self._ensure_directories()
        
        # 加载模板
        self._load_templates()
    
    def _ensure_directories(self):
        """确保必要目录存在"""
        directories = [
            self.json_dir / "basic",
            self.json_dir / "daily",
            self.json_dir / "battle", 
            self.json_dir / "system",
            self.templates_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _load_templates(self):
        """加载配置模板"""
        self.templates.clear()
        
        if not self.templates_dir.exists():
            return
        
        for template_file in self.templates_dir.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                
                template = ConfigTemplate(
                    template_id=template_data['template_id'],
                    name=template_data['name'],
                    description=template_data['description'],
                    category=template_data['category'],
                    schema=template_data['schema'],
                    default_values=template_data['default_values'],
                    created_time=datetime.fromisoformat(template_data['created_time']),
                    updated_time=datetime.fromisoformat(template_data['updated_time'])
                )
                
                self.templates[template.template_id] = template
                
            except Exception as e:
                print(f"加载模板失败 {template_file}: {e}")
    
    def create_config_from_template(self, template_id: str, **overrides) -> Dict[str, Any]:
        """
        从模板创建配置
        
        Args:
            template_id: 模板ID
            **overrides: 覆盖的配置值
            
        Returns:
            配置字典
        """
        if template_id not in self.templates:
            raise ValueError(f"模板不存在: {template_id}")
        
        template = self.templates[template_id]
        
        # 从默认值开始
        config = template.default_values.copy()
        
        # 生成唯一ID
        if 'id' not in overrides:
            config['id'] = f"{template.category}_{uuid.uuid4().hex[:8]}"
        
        # 应用覆盖值
        config.update(overrides)
        
        return config
    
    def validate_and_save_config(self, config: Dict[str, Any], 
                                category: str, filename: str = None) -> tuple[bool, List[str], Optional[str]]:
        """
        验证并保存配置
        
        Args:
            config: 配置字典
            category: 配置分类
            filename: 文件名（可选）
            
        Returns:
            (是否成功, 错误列表, 保存路径)
        """
        # 验证配置
        task_type = config.get('type', 'base_task')
        is_valid, errors = self.validator.validate_config(config, task_type)
        
        if not is_valid:
            return False, errors, None
        
        # 生成文件名
        if filename is None:
            task_name = config.get('name', config.get('id', 'unnamed'))
            # 清理文件名中的特殊字符
            safe_name = "".join(c for c in task_name if c.isalnum() or c in (' ', '-', '_')).strip()
            filename = f"{safe_name}.json"
        
        # 保存文件
        try:
            category_dir = self.json_dir / category
            category_dir.mkdir(exist_ok=True)
            
            file_path = category_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            return True, [], str(file_path)
            
        except Exception as e:
            return False, [f"保存配置失败: {str(e)}"], None
    
    def load_config(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        加载配置文件
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            配置字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置失败 {file_path}: {e}")
            return None
    
    def list_configs(self, category: str = None) -> List[Dict[str, Any]]:
        """
        列出配置文件
        
        Args:
            category: 配置分类过滤
            
        Returns:
            配置信息列表
        """
        configs = []
        
        if category:
            categories = [category]
        else:
            categories = ['basic', 'daily', 'battle', 'system']
        
        for cat in categories:
            cat_dir = self.json_dir / cat
            if not cat_dir.exists():
                continue
            
            for config_file in cat_dir.glob("*.json"):
                config = self.load_config(config_file)
                if config:
                    config_info = {
                        'file_path': str(config_file),
                        'category': cat,
                        'id': config.get('id', ''),
                        'name': config.get('name', ''),
                        'type': config.get('type', 'base_task'),
                        'enabled': config.get('enabled', True),
                        'modified_time': datetime.fromtimestamp(config_file.stat().st_mtime)
                    }
                    configs.append(config_info)
        
        return configs
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """获取可用模板列表"""
        return [
            {
                'template_id': template.template_id,
                'name': template.name,
                'description': template.description,
                'category': template.category,
                'created_time': template.created_time.isoformat(),
                'updated_time': template.updated_time.isoformat()
            }
            for template in self.templates.values()
        ]
