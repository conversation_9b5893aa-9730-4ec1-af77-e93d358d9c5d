# -*- coding: utf-8 -*-
"""
任务管理器
统一管理基础任务、任务流和工作流
"""

import os
import json
import importlib.util
from typing import Dict, List, Any, Optional, Type
from pathlib import Path
import uuid

from .base_task import BaseTask, TaskConfig, create_task_config
from .task_flow import TaskFlow
from .workflow import Workflow, ScheduleConfig, ScheduleType


class TaskManager:
    """
    任务管理器类
    负责加载、管理和执行所有类型的任务
    """
    
    def __init__(self, task_root_dir: str = "task"):
        """
        初始化任务管理器
        
        Args:
            task_root_dir: 任务根目录
        """
        self.task_root_dir = Path(task_root_dir)
        self.json_dir = self.task_root_dir / "json"
        self.py_dir = self.task_root_dir / "py"
        
        # 存储各类任务
        self.base_tasks: Dict[str, BaseTask] = {}
        self.task_flows: Dict[str, TaskFlow] = {}
        self.workflows: Dict[str, Workflow] = {}
        
        # 任务类注册表
        self.task_classes: Dict[str, Type[BaseTask]] = {}
        
        self.logger = None
        
        # 确保目录存在
        self._ensure_directories()
        
        # 加载所有任务
        self.load_all_tasks()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.json_dir / "basic",
            self.json_dir / "daily", 
            self.json_dir / "battle",
            self.json_dir / "system",
            self.py_dir / "basic",
            self.py_dir / "daily",
            self.py_dir / "battle", 
            self.py_dir / "system"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def register_task_class(self, task_type: str, task_class: Type[BaseTask]):
        """
        注册任务类
        
        Args:
            task_type: 任务类型
            task_class: 任务类
        """
        self.task_classes[task_type] = task_class
    
    def load_all_tasks(self):
        """加载所有任务"""
        self._load_base_tasks()
        self._load_task_flows()
        self._load_workflows()
    
    def _load_base_tasks(self):
        """加载基础任务"""
        self.base_tasks.clear()
        
        # 遍历所有分类目录
        for category_dir in self.json_dir.iterdir():
            if not category_dir.is_dir():
                continue
                
            category = category_dir.name
            py_category_dir = self.py_dir / category
            
            # 加载该分类下的所有任务
            for json_file in category_dir.glob("*.json"):
                try:
                    task = self._load_single_task(json_file, py_category_dir, category)
                    if task:
                        self.base_tasks[task.task_id] = task
                        if self.logger:
                            self.logger.info(f"已加载基础任务: {task.task_name}")
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"加载任务失败 {json_file}: {e}")
        
        if self.logger:
            self.logger.info(f"共加载 {len(self.base_tasks)} 个基础任务")
    
    def _load_single_task(self, json_file: Path, py_dir: Path, category: str) -> Optional[BaseTask]:
        """
        加载单个任务
        
        Args:
            json_file: JSON配置文件路径
            py_dir: Python文件目录
            category: 任务分类
            
        Returns:
            任务实例
        """
        # 加载JSON配置
        with open(json_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 创建任务配置
        config = TaskConfig(
            id=config_data.get('id', str(uuid.uuid4())),
            name=config_data.get('name', json_file.stem),
            description=config_data.get('description', ''),
            category=category,
            enabled=config_data.get('enabled', True),
            priority=config_data.get('priority', 1),
            timeout=config_data.get('timeout', 30.0),
            retry_count=config_data.get('retry_count', 3),
            retry_delay=config_data.get('retry_delay', 1.0),
            parameters=config_data.get('parameters', {})
        )
        
        # 查找对应的Python文件
        py_file = py_dir / f"{json_file.stem}.py"
        if not py_file.exists():
            # 如果没有对应的Python文件，使用默认任务类
            return self._create_default_task(config, config_data)
        
        # 动态加载Python模块
        try:
            spec = importlib.util.spec_from_file_location(f"task_{json_file.stem}", py_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找任务类
            task_class_name = config_data.get('class_name', 'Task')
            if hasattr(module, task_class_name):
                task_class = getattr(module, task_class_name)
                return task_class(config)
            else:
                if self.logger:
                    self.logger.warning(f"Python文件 {py_file} 中未找到类 {task_class_name}")
                return self._create_default_task(config, config_data)
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"加载Python模块失败 {py_file}: {e}")
            return self._create_default_task(config, config_data)
    
    def _create_default_task(self, config: TaskConfig, config_data: Dict[str, Any]) -> BaseTask:
        """创建默认任务实例"""
        # 这里可以根据任务类型创建不同的默认任务
        task_type = config_data.get('type', 'basic')
        
        if task_type in self.task_classes:
            return self.task_classes[task_type](config)
        else:
            # 创建一个简单的默认任务
            return DefaultTask(config)
    
    def _load_task_flows(self):
        """加载任务流（从配置文件）"""
        self.task_flows.clear()

        flows_dir = self.task_root_dir / "flows"
        if not flows_dir.exists():
            return

        for flow_file in flows_dir.glob("*.json"):
            try:
                with open(flow_file, 'r', encoding='utf-8') as f:
                    flow_data = json.load(f)

                # 创建任务流
                flow = TaskFlow(
                    flow_id=flow_data['flow_id'],
                    name=flow_data['name'],
                    description=flow_data.get('description', '')
                )

                # 设置执行模式
                execution_mode = flow_data.get('execution_mode', 'sequential')
                if execution_mode == 'sequential':
                    from .task_flow import ExecutionMode
                    flow.set_execution_mode(ExecutionMode.SEQUENTIAL)

                # 添加任务步骤
                for step_data in flow_data.get('steps', []):
                    task_id = step_data['task_id']
                    task = self.base_tasks.get(task_id)

                    if task:
                        flow.add_task(
                            task=task,
                            order=step_data.get('order', 0),
                            delay_before=step_data.get('delay_before', 0.0),
                            delay_after=step_data.get('delay_after', 0.0),
                            continue_on_error=step_data.get('continue_on_error', True),
                            parameters=step_data.get('parameters', {})
                        )
                    else:
                        if self.logger:
                            self.logger.warning(f"任务流 {flow.name} 中未找到任务: {task_id}")

                self.task_flows[flow.flow_id] = flow
                if self.logger:
                    self.logger.info(f"已加载任务流: {flow.name}")

            except Exception as e:
                if self.logger:
                    self.logger.error(f"加载任务流失败 {flow_file}: {e}")

        if self.logger:
            self.logger.info(f"共加载 {len(self.task_flows)} 个任务流")

    def _load_workflows(self):
        """加载工作流（从配置文件）"""
        self.workflows.clear()

        workflows_dir = self.task_root_dir / "workflows"
        if not workflows_dir.exists():
            return

        for workflow_file in workflows_dir.glob("*.json"):
            try:
                with open(workflow_file, 'r', encoding='utf-8') as f:
                    workflow_data = json.load(f)

                # 创建工作流
                workflow = Workflow(
                    workflow_id=workflow_data['workflow_id'],
                    name=workflow_data['name'],
                    description=workflow_data.get('description', '')
                )

                # 设置调度配置
                schedule_data = workflow_data.get('schedule', {})
                if schedule_data.get('enabled', False):
                    from .workflow import ScheduleConfig, ScheduleType
                    from datetime import datetime

                    schedule_type_str = schedule_data.get('schedule_type', 'manual')
                    schedule_type = ScheduleType.MANUAL

                    if schedule_type_str == 'once':
                        schedule_type = ScheduleType.ONCE
                    elif schedule_type_str == 'interval':
                        schedule_type = ScheduleType.INTERVAL
                    elif schedule_type_str == 'cron':
                        schedule_type = ScheduleType.CRON

                    schedule_config = ScheduleConfig(
                        schedule_type=schedule_type,
                        start_time=datetime.fromisoformat(schedule_data['start_time']) if schedule_data.get('start_time') else None,
                        end_time=datetime.fromisoformat(schedule_data['end_time']) if schedule_data.get('end_time') else None,
                        interval_seconds=schedule_data.get('interval_seconds'),
                        cron_expression=schedule_data.get('cron_expression'),
                        max_executions=schedule_data.get('max_executions'),
                        enabled=schedule_data.get('enabled', True)
                    )

                    workflow.set_schedule(schedule_config)

                # 添加任务流步骤
                for step_data in workflow_data.get('steps', []):
                    flow_id = step_data['flow_id']
                    flow = self.task_flows.get(flow_id)

                    if flow:
                        workflow.add_flow(
                            flow=flow,
                            order=step_data.get('order', 0),
                            delay_before=step_data.get('delay_before', 0.0),
                            delay_after=step_data.get('delay_after', 0.0),
                            continue_on_error=step_data.get('continue_on_error', True),
                            parameters=step_data.get('parameters', {})
                        )
                    else:
                        if self.logger:
                            self.logger.warning(f"工作流 {workflow.name} 中未找到任务流: {flow_id}")

                self.workflows[workflow.workflow_id] = workflow
                if self.logger:
                    self.logger.info(f"已加载工作流: {workflow.name}")

            except Exception as e:
                if self.logger:
                    self.logger.error(f"加载工作流失败 {workflow_file}: {e}")

        if self.logger:
            self.logger.info(f"共加载 {len(self.workflows)} 个工作流")
    
    def create_task_flow(self, flow_id: str, name: str, description: str = "") -> TaskFlow:
        """
        创建任务流
        
        Args:
            flow_id: 任务流ID
            name: 任务流名称
            description: 任务流描述
            
        Returns:
            任务流实例
        """
        flow = TaskFlow(flow_id, name, description)
        self.task_flows[flow_id] = flow
        return flow
    
    def create_workflow(self, workflow_id: str, name: str, description: str = "") -> Workflow:
        """
        创建工作流
        
        Args:
            workflow_id: 工作流ID
            name: 工作流名称
            description: 工作流描述
            
        Returns:
            工作流实例
        """
        workflow = Workflow(workflow_id, name, description)
        self.workflows[workflow_id] = workflow
        return workflow
    
    def get_task(self, task_id: str) -> Optional[BaseTask]:
        """获取基础任务"""
        return self.base_tasks.get(task_id)
    
    def get_task_flow(self, flow_id: str) -> Optional[TaskFlow]:
        """获取任务流"""
        return self.task_flows.get(flow_id)
    
    def get_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """获取工作流"""
        return self.workflows.get(workflow_id)
    
    def list_tasks(self, category: str = None) -> List[BaseTask]:
        """
        列出基础任务
        
        Args:
            category: 任务分类过滤
            
        Returns:
            任务列表
        """
        if category:
            return [task for task in self.base_tasks.values() 
                   if task.config.category == category]
        return list(self.base_tasks.values())
    
    def list_task_flows(self) -> List[TaskFlow]:
        """列出任务流"""
        return list(self.task_flows.values())
    
    def list_workflows(self) -> List[Workflow]:
        """列出工作流"""
        return list(self.workflows.values())
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'base_tasks_count': len(self.base_tasks),
            'task_flows_count': len(self.task_flows),
            'workflows_count': len(self.workflows),
            'categories': list(set(task.config.category for task in self.base_tasks.values()))
        }


class DefaultTask(BaseTask):
    """默认任务实现"""
    
    def execute(self):
        """默认执行逻辑"""
        from .base_task import TaskResult, TaskStatus
        
        # 这里可以实现一些基本的任务逻辑
        # 比如等待、日志记录等
        
        return TaskResult(
            task_id=self.task_id,
            status=TaskStatus.SUCCESS,
            message="默认任务执行完成"
        )
