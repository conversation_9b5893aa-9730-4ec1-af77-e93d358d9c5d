{"id": "daily_confirm_collection_001", "name": "确认奖励领取", "description": "等待确认奖励已领取", "type": "wait", "class_name": "WaitTask", "enabled": true, "priority": 1, "timeout": 8.0, "retry_count": 1, "retry_delay": 0.5, "parameters": {"wait_type": "text", "target_text": "奖励已领取", "max_wait_time": 5.0, "check_interval": 0.5, "optional": true}, "triggers": [], "validation": {"expected_result": "text_found", "timeout": 5.0}}