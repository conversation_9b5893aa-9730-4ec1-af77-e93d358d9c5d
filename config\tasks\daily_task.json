{"task_info": {"name": "每日任务自动化", "description": "自动完成每日任务流程", "version": "0_0001", "enabled": true, "priority": 1}, "triggers": [{"id": "daily_task_button", "type": "text_recognition", "target_text": "每日任务", "confidence": 0.8, "region": {"x": 0, "y": 0, "width": 1920, "height": 1080}, "preprocessing": {"enhance_contrast": true, "scale_factor": 1.5}}, {"id": "reward_available", "type": "text_recognition", "target_text": "领取奖励", "confidence": 0.7, "region": {"x": 800, "y": 400, "width": 320, "height": 280}}], "actions": [{"id": "click_daily_task", "type": "click", "trigger_id": "daily_task_button", "position": {"type": "auto", "offset_x": 0, "offset_y": 0}, "timing": {"delay_before": 1.0, "delay_after": 2.0}, "retry": {"max_attempts": 3, "retry_delay": 1.0}}, {"id": "wait_for_reward", "type": "wait_for_text", "target_text": "领取奖励", "timeout": 10.0, "check_interval": 0.5}, {"id": "click_reward", "type": "click", "trigger_id": "reward_available", "position": {"type": "auto", "offset_x": 0, "offset_y": 0}, "timing": {"delay_before": 0.5, "delay_after": 1.0}}, {"id": "confirm_collection", "type": "wait_for_text", "target_text": "奖励已领取", "timeout": 5.0, "optional": true}], "flow_control": {"execution_order": ["click_daily_task", "wait_for_reward", "click_reward", "confirm_collection"], "loop_enabled": false, "loop_count": 1, "continue_on_error": true}, "conditions": {"time_range": {"start": "00:00", "end": "23:59"}, "cooldown": 3600, "max_executions_per_day": 1}}