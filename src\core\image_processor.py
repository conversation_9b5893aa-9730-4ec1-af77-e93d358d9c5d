# -*- coding: utf-8 -*-
"""
图像预处理模块
对捕获的图像进行预处理以提高OCR识别准确率
"""

import cv2
import numpy as np
from typing import Optional, Tuple, Dict, Any
from PIL import Image, ImageEnhance


class ImageProcessor:
    """图像预处理器类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化图像处理器
        
        Args:
            config: 预处理配置参数
        """
        self.config = config or self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "enhance_contrast": True,
            "remove_noise": True,
            "scale_factor": 2.0,
            "gaussian_blur": False,
            "threshold_binary": True,
            "sharpen": False,
            "gamma_correction": False,
            "gamma_value": 1.2
        }
    
    def preprocess_image(self, image: np.ndarray, custom_config: Optional[Dict] = None) -> np.ndarray:
        """
        对图像进行完整的预处理流程
        
        Args:
            image: 输入图像 (BGR格式)
            custom_config: 自定义配置，会覆盖默认配置
            
        Returns:
            预处理后的图像
        """
        if image is None:
            raise ValueError("输入图像不能为空")
        
        # 使用自定义配置或默认配置
        config = {**self.config, **(custom_config or {})}
        
        processed_image = image.copy()
        
        # 1. 缩放图像
        if config.get("scale_factor", 1.0) != 1.0:
            processed_image = self.resize_image(processed_image, config["scale_factor"])
        
        # 2. 转换为灰度图像
        if len(processed_image.shape) == 3:
            processed_image = cv2.cvtColor(processed_image, cv2.COLOR_BGR2GRAY)
        
        # 3. 高斯模糊去噪
        if config.get("gaussian_blur", False):
            processed_image = cv2.GaussianBlur(processed_image, (3, 3), 0)
        
        # 4. 去除噪声
        if config.get("remove_noise", True):
            processed_image = self.remove_noise(processed_image)
        
        # 5. 增强对比度
        if config.get("enhance_contrast", True):
            processed_image = self.enhance_contrast(processed_image)
        
        # 6. 伽马校正
        if config.get("gamma_correction", False):
            gamma_value = config.get("gamma_value", 1.2)
            processed_image = self.gamma_correction(processed_image, gamma_value)
        
        # 7. 锐化
        if config.get("sharpen", False):
            processed_image = self.sharpen_image(processed_image)
        
        # 8. 二值化
        if config.get("threshold_binary", True):
            processed_image = self.threshold_binary(processed_image)
        
        return processed_image
    
    def resize_image(self, image: np.ndarray, scale_factor: float) -> np.ndarray:
        """
        缩放图像
        
        Args:
            image: 输入图像
            scale_factor: 缩放因子
            
        Returns:
            缩放后的图像
        """
        if scale_factor == 1.0:
            return image
        
        height, width = image.shape[:2]
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        
        # 使用双三次插值进行缩放
        return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
    
    def enhance_contrast(self, image: np.ndarray, method: str = "clahe") -> np.ndarray:
        """
        增强图像对比度
        
        Args:
            image: 输入图像 (灰度图)
            method: 增强方法 ("clahe", "histogram", "gamma")
            
        Returns:
            对比度增强后的图像
        """
        if method == "clahe":
            # 自适应直方图均衡化
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            return clahe.apply(image)
        
        elif method == "histogram":
            # 直方图均衡化
            return cv2.equalizeHist(image)
        
        elif method == "gamma":
            # 伽马校正
            return self.gamma_correction(image, 1.2)
        
        else:
            # 简单的对比度拉伸
            return cv2.convertScaleAbs(image, alpha=1.2, beta=10)
    
    def remove_noise(self, image: np.ndarray, method: str = "bilateral") -> np.ndarray:
        """
        去除图像噪声
        
        Args:
            image: 输入图像
            method: 去噪方法 ("bilateral", "gaussian", "median", "morphology")
            
        Returns:
            去噪后的图像
        """
        if method == "bilateral":
            # 双边滤波，保持边缘的同时去噪
            return cv2.bilateralFilter(image, 9, 75, 75)
        
        elif method == "gaussian":
            # 高斯滤波
            return cv2.GaussianBlur(image, (5, 5), 0)
        
        elif method == "median":
            # 中值滤波，对椒盐噪声效果好
            return cv2.medianBlur(image, 5)
        
        elif method == "morphology":
            # 形态学操作去噪
            kernel = np.ones((3, 3), np.uint8)
            opened = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)
            return cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel)
        
        else:
            return image
    
    def gamma_correction(self, image: np.ndarray, gamma: float) -> np.ndarray:
        """
        伽马校正
        
        Args:
            image: 输入图像
            gamma: 伽马值
            
        Returns:
            校正后的图像
        """
        # 构建查找表
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
        
        # 应用伽马校正
        return cv2.LUT(image, table)
    
    def sharpen_image(self, image: np.ndarray) -> np.ndarray:
        """
        锐化图像
        
        Args:
            image: 输入图像
            
        Returns:
            锐化后的图像
        """
        # 锐化卷积核
        kernel = np.array([[-1, -1, -1],
                          [-1,  9, -1],
                          [-1, -1, -1]])
        
        return cv2.filter2D(image, -1, kernel)
    
    def threshold_binary(self, image: np.ndarray, method: str = "otsu") -> np.ndarray:
        """
        二值化处理
        
        Args:
            image: 输入图像 (灰度图)
            method: 二值化方法 ("otsu", "adaptive", "manual")
            
        Returns:
            二值化后的图像
        """
        if method == "otsu":
            # Otsu自动阈值
            _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            return binary
        
        elif method == "adaptive":
            # 自适应阈值
            return cv2.adaptiveThreshold(image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv2.THRESH_BINARY, 11, 2)
        
        elif method == "manual":
            # 手动阈值
            _, binary = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)
            return binary
        
        else:
            return image
    
    def preprocess_for_ocr(self, image: np.ndarray, text_color: str = "dark") -> np.ndarray:
        """
        专门为OCR优化的预处理
        
        Args:
            image: 输入图像
            text_color: 文字颜色 ("dark" 或 "light")
            
        Returns:
            OCR优化后的图像
        """
        processed = image.copy()
        
        # 转换为灰度
        if len(processed.shape) == 3:
            processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
        
        # 放大图像提高识别精度
        processed = self.resize_image(processed, 2.0)
        
        # 去噪
        processed = cv2.bilateralFilter(processed, 9, 75, 75)
        
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        processed = clahe.apply(processed)
        
        # 根据文字颜色调整二值化
        if text_color == "dark":
            # 深色文字，白色背景
            _, processed = cv2.threshold(processed, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        else:
            # 浅色文字，深色背景
            _, processed = cv2.threshold(processed, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        return processed
    
    def save_processed_image(self, image: np.ndarray, filepath: str) -> bool:
        """
        保存处理后的图像
        
        Args:
            image: 图像数组
            filepath: 保存路径
            
        Returns:
            保存是否成功
        """
        try:
            cv2.imwrite(filepath, image)
            return True
        except Exception as e:
            print(f"保存图像失败: {e}")
            return False


if __name__ == "__main__":
    # 测试图像处理功能
    print("测试图像处理功能...")
    
    processor = ImageProcessor()
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (100, 200, 3), dtype=np.uint8)
    
    # 预处理
    processed = processor.preprocess_image(test_image)
    
    print(f"原始图像尺寸: {test_image.shape}")
    print(f"处理后图像尺寸: {processed.shape}")
    print("图像处理测试完成")
