# -*- coding: utf-8 -*-
"""
主窗口
SnowZone OCR自动化工具的主图形界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from .utils.gui_helpers import (
    GUIStyles, StatusBar, ThreadSafeGUI, LogTextWidget,
    show_error, show_info, ask_yes_no, center_window
)
from .components.task_list import TaskListWidget
from .components.task_library_widget import TaskLibraryWidget
from .components.workflow_widget import WorkflowWidget
from .components.window_capture_test import WindowCaptureTestWidget
from .task_editor import show_task_editor

# 导入后端模块
BACKEND_AVAILABLE = True
backend_errors = []

try:
    from src.config.config_manager import ConfigManager
    from src.config.task_loader import TaskLoader
    from src.config.task_library import TaskLibrary
    from src.config.task_workflow import WorkflowManager
except ImportError as e:
    backend_errors.append(f"配置模块: {e}")
    ConfigManager = None
    TaskLoader = None
    TaskLibrary = None
    WorkflowManager = None

try:
    from src.core.window_capture import WindowCapture
    from src.core.image_processor import ImageProcessor
except ImportError as e:
    backend_errors.append(f"图像处理模块: {e}")
    WindowCapture = None
    ImageProcessor = None

try:
    from src.core.ocr_engine import OCREngine
except ImportError as e:
    backend_errors.append(f"OCR模块: {e}")
    OCREngine = None

try:
    from src.core.action_executor import ActionExecutor
except ImportError as e:
    backend_errors.append(f"动作执行模块: {e}")
    ActionExecutor = None

try:
    from src.core.task_scheduler import TaskScheduler
except ImportError as e:
    backend_errors.append(f"任务调度模块: {e}")
    TaskScheduler = None

try:
    from src.utils.logger import get_logger
    from src.utils.version_manager import VersionManager
    from src.utils.exception_handler import ExceptionHandler
except ImportError as e:
    backend_errors.append(f"工具模块: {e}")
    get_logger = None
    VersionManager = None
    ExceptionHandler = None

if backend_errors:
    BACKEND_AVAILABLE = False
    print("后端模块部分导入失败:")
    for error in backend_errors:
        print(f"  - {error}")


class SnowZoneMainWindow:
    """SnowZone主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()
        self.root.title("SnowZone OCR自动化工具")
        self.root.geometry("1200x800")
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 初始化后端组件
        self._init_backend()
        
        # 创建界面
        self._create_menu()
        self._create_toolbar()
        self._create_main_area()
        self._create_status_bar()
        
        # 设置布局
        self._setup_layout()
        
        # 绑定事件
        self._bind_events()
        
        # 线程安全GUI操作
        self.thread_safe_gui = ThreadSafeGUI(self.root)
        
        # 初始化界面数据
        self._init_ui_data()
    
    def _init_backend(self):
        """初始化后端组件"""
        # 初始化所有组件为None
        self.config_manager = None
        self.task_loader = None
        self.task_library = None
        self.workflow_manager = None
        self.window_capture = None
        self.image_processor = None
        self.ocr_engine = None
        self.action_executor = None
        self.task_scheduler = None
        self.logger = None
        self.version_manager = None
        self.exception_handler = None

        # 尝试初始化配置管理器
        if ConfigManager:
            try:
                self.config_manager = ConfigManager()
                print("配置管理器初始化成功")
            except Exception as e:
                print(f"配置管理器初始化失败: {e}")

        # 尝试初始化任务加载器
        if TaskLoader and self.config_manager:
            try:
                self.task_loader = TaskLoader(self.config_manager)
                print("任务加载器初始化成功")
            except Exception as e:
                print(f"任务加载器初始化失败: {e}")

        # 尝试初始化任务库
        if TaskLibrary:
            try:
                self.task_library = TaskLibrary(self.config_manager)
                print("任务库初始化成功")
            except Exception as e:
                print(f"任务库初始化失败: {e}")

        # 尝试初始化工作流管理器
        if WorkflowManager and self.task_library:
            try:
                self.workflow_manager = WorkflowManager(self.task_library)
                print("工作流管理器初始化成功")
            except Exception as e:
                print(f"工作流管理器初始化失败: {e}")

        # 尝试初始化窗口捕获器
        if WindowCapture:
            try:
                self.window_capture = WindowCapture()
                print("窗口捕获器初始化成功")
            except Exception as e:
                print(f"窗口捕获器初始化失败: {e}")

        # 尝试初始化图像处理器
        if ImageProcessor:
            try:
                self.image_processor = ImageProcessor()
                print("图像处理器初始化成功")
            except Exception as e:
                print(f"图像处理器初始化失败: {e}")

        # 尝试初始化OCR引擎
        if OCREngine:
            try:
                self.ocr_engine = OCREngine()
                print("OCR引擎初始化成功")
            except Exception as e:
                print(f"OCR引擎初始化失败: {e}")

        # 尝试初始化动作执行器
        if ActionExecutor:
            try:
                self.action_executor = ActionExecutor()
                print("动作执行器初始化成功")
            except Exception as e:
                print(f"动作执行器初始化失败: {e}")

        # 尝试初始化任务调度器
        if TaskScheduler:
            try:
                self.task_scheduler = TaskScheduler()
                print("任务调度器初始化成功")
            except Exception as e:
                print(f"任务调度器初始化失败: {e}")

        # 尝试初始化工具组件
        if get_logger:
            try:
                self.logger = get_logger()
                print("日志器初始化成功")
            except Exception as e:
                print(f"日志器初始化失败: {e}")

        if VersionManager:
            try:
                self.version_manager = VersionManager()
                print("版本管理器初始化成功")
            except Exception as e:
                print(f"版本管理器初始化失败: {e}")

        if ExceptionHandler and self.logger:
            try:
                self.exception_handler = ExceptionHandler(self.logger)
                print("异常处理器初始化成功")
            except Exception as e:
                print(f"异常处理器初始化失败: {e}")

        print("后端组件初始化完成")
    
    def _create_menu(self):
        """创建菜单栏"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # 文件菜单
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建任务", command=self._new_task, accelerator="Ctrl+N")
        file_menu.add_command(label="打开任务", command=self._open_task, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="保存配置", command=self._save_config, accelerator="Ctrl+S")
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._quit_app, accelerator="Ctrl+Q")
        
        # 编辑菜单
        edit_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="编辑任务", command=self._edit_task, accelerator="F2")
        edit_menu.add_command(label="删除任务", command=self._delete_task, accelerator="Delete")
        edit_menu.add_separator()
        edit_menu.add_command(label="启用任务", command=self._enable_task)
        edit_menu.add_command(label="禁用任务", command=self._disable_task)
        
        # 工具菜单
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="窗口捕获测试", command=self._show_window_capture_test)
        tools_menu.add_command(label="OCR测试", command=self._show_ocr_test)
        tools_menu.add_separator()
        tools_menu.add_command(label="启动调度器", command=self._start_scheduler)
        tools_menu.add_command(label="停止调度器", command=self._stop_scheduler)
        tools_menu.add_separator()
        tools_menu.add_command(label="刷新任务列表", command=self._refresh_tasks)
        tools_menu.add_command(label="清空日志", command=self._clear_log)
        
        # 帮助菜单
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _create_toolbar(self):
        """创建工具栏"""
        self.toolbar = ttk.Frame(self.root)
        
        # 任务操作按钮
        ttk.Button(self.toolbar, text="新建任务", 
                  command=self._new_task).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(self.toolbar, text="编辑任务", 
                  command=self._edit_task).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(self.toolbar, text="删除任务", 
                  command=self._delete_task).pack(side=tk.LEFT, padx=(0, 5))
        
        # 分隔符
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=5
        )
        
        # 调度器控制按钮
        self.start_btn = ttk.Button(self.toolbar, text="启动调度器", 
                                   command=self._start_scheduler)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(self.toolbar, text="停止调度器", 
                                  command=self._stop_scheduler, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 分隔符
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=5
        )
        
        # 测试按钮
        ttk.Button(self.toolbar, text="窗口捕获测试", 
                  command=self._show_window_capture_test).pack(side=tk.LEFT, padx=(0, 5))
        
        # 刷新按钮
        ttk.Button(self.toolbar, text="刷新", 
                  command=self._refresh_tasks).pack(side=tk.LEFT, padx=(0, 5))
    
    def _create_main_area(self):
        """创建主工作区"""
        # 创建PanedWindow用于分割界面
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        
        # 左侧面板 - 任务管理
        self.left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_frame, weight=1)

        # 创建左侧笔记本控件
        self.left_notebook = ttk.Notebook(self.left_frame)

        # 任务库面板
        self.task_library_frame = ttk.Frame(self.left_notebook)
        self.left_notebook.add(self.task_library_frame, text="任务库")

        self.task_library_widget = TaskLibraryWidget(
            self.task_library_frame,
            self.task_library
        )
        self.task_library_widget.pack(fill=tk.BOTH, expand=True)

        # 设置任务库回调
        self.task_library_widget.set_callbacks(
            on_task_selected=self._on_library_task_selected,
            on_task_edited=self._on_library_task_edited,
            on_task_deleted=self._on_library_task_deleted,
            on_task_toggled=self._on_library_task_toggled,
            on_add_to_workflow=self._on_add_task_to_workflow
        )

        # 工作流面板
        self.workflow_frame = ttk.Frame(self.left_notebook)
        self.left_notebook.add(self.workflow_frame, text="工作流")

        self.workflow_widget = WorkflowWidget(
            self.workflow_frame,
            self.workflow_manager
        )
        self.workflow_widget.pack(fill=tk.BOTH, expand=True)

        # 设置工作流回调
        self.workflow_widget.set_callbacks(
            on_workflow_selected=self._on_workflow_selected,
            on_workflow_edited=self._on_workflow_edited,
            on_workflow_deleted=self._on_workflow_deleted,
            on_workflow_executed=self._on_workflow_executed
        )

        # 传统任务列表面板（保持兼容性）
        self.task_list_frame = ttk.Frame(self.left_notebook)
        self.left_notebook.add(self.task_list_frame, text="传统任务")

        self.task_list = TaskListWidget(self.task_list_frame, self.task_loader)
        self.task_list.pack(fill=tk.BOTH, expand=True)

        # 设置任务列表回调
        self.task_list.set_callbacks(
            on_task_selected=self._on_task_selected,
            on_task_edited=self._on_task_edited,
            on_task_deleted=self._on_task_deleted,
            on_task_toggled=self._on_task_toggled
        )

        self.left_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 右侧面板 - 详情和日志
        self.right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_frame, weight=1)
        
        # 创建笔记本控件用于切换不同的面板
        self.right_notebook = ttk.Notebook(self.right_frame)
        
        # 日志面板
        self.log_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(self.log_frame, text="日志")
        
        # 日志文本控件
        self.log_text = LogTextWidget(
            self.log_frame,
            height=20,
            font=GUIStyles.FONTS['code']
        )
        
        # 日志滚动条
        log_scroll = ttk.Scrollbar(self.log_frame, orient=tk.VERTICAL, 
                                  command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scroll.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 窗口捕获测试面板
        self.capture_test_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(self.capture_test_frame, text="窗口捕获测试")
        
        self.window_capture_test = WindowCaptureTestWidget(
            self.capture_test_frame, 
            self.window_capture
        )
        self.window_capture_test.pack(fill=tk.BOTH, expand=True)
        
        self.right_notebook.pack(fill=tk.BOTH, expand=True)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = StatusBar(self.root)
        
        # 设置版本信息
        if self.version_manager:
            version = self.version_manager.get_current_version()
            self.status_bar.set_version(version)
    
    def _setup_layout(self):
        """设置布局"""
        self.toolbar.pack(fill=tk.X, padx=5, pady=5)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
    
    def _bind_events(self):
        """绑定事件"""
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_closing)
        
        # 键盘快捷键
        self.root.bind('<Control-n>', lambda e: self._new_task())
        self.root.bind('<Control-o>', lambda e: self._open_task())
        self.root.bind('<Control-s>', lambda e: self._save_config())
        self.root.bind('<Control-q>', lambda e: self._quit_app())
        self.root.bind('<F2>', lambda e: self._edit_task())
        self.root.bind('<Delete>', lambda e: self._delete_task())
    
    def _init_ui_data(self):
        """初始化界面数据"""
        # 刷新任务列表
        self._refresh_tasks()
        
        # 添加欢迎日志
        self._add_log("INFO", "SnowZone OCR自动化工具已启动")
        
        if not BACKEND_AVAILABLE:
            self._add_log("WARNING", "后端模块未完全加载，部分功能可能不可用")
        
        # 更新状态
        self.status_bar.set_status("就绪")

    def _add_log(self, level: str, message: str):
        """添加日志"""
        self.log_text.append_log(level, message)

        # 同时输出到后端日志
        if self.logger:
            if level == "INFO":
                self.logger.info(message)
            elif level == "WARNING":
                self.logger.warning(message)
            elif level == "ERROR":
                self.logger.error(message)
            elif level == "DEBUG":
                self.logger.debug(message)

    # 菜单和工具栏事件处理
    def _new_task(self):
        """新建任务"""
        try:
            result = show_task_editor(self.root, self.task_loader, None)
            if result == "save":
                self._refresh_tasks()
                self._add_log("INFO", "新任务已创建")
        except Exception as e:
            self._add_log("ERROR", f"创建任务失败: {e}")
            show_error(self.root, "错误", f"创建任务失败: {e}")

    def _open_task(self):
        """打开任务"""
        # 这里可以实现从文件打开任务的功能
        show_info(self.root, "提示", "此功能尚未实现")

    def _edit_task(self):
        """编辑任务"""
        selected_task_id = self.task_list.get_selected_task_id()
        if not selected_task_id:
            show_error(self.root, "错误", "请先选择一个任务")
            return

        try:
            result = show_task_editor(self.root, self.task_loader, selected_task_id)
            if result == "save":
                self._refresh_tasks()
                self._add_log("INFO", f"任务已更新: {selected_task_id}")
        except Exception as e:
            self._add_log("ERROR", f"编辑任务失败: {e}")
            show_error(self.root, "错误", f"编辑任务失败: {e}")

    def _delete_task(self):
        """删除任务"""
        selected_task_id = self.task_list.get_selected_task_id()
        if not selected_task_id:
            show_error(self.root, "错误", "请先选择一个任务")
            return

        if self.task_loader:
            task_info = self.task_loader.get_task(selected_task_id)
            if task_info:
                if ask_yes_no(
                    self.root,
                    "确认删除",
                    f"确定要删除任务 '{task_info.name}' 吗？\n此操作不可撤销。"
                ):
                    try:
                        if self.task_loader.delete_task(selected_task_id):
                            self._refresh_tasks()
                            self._add_log("INFO", f"任务已删除: {selected_task_id}")
                        else:
                            show_error(self.root, "错误", "删除任务失败")
                    except Exception as e:
                        self._add_log("ERROR", f"删除任务失败: {e}")
                        show_error(self.root, "错误", f"删除任务失败: {e}")

    def _enable_task(self):
        """启用任务"""
        selected_task_id = self.task_list.get_selected_task_id()
        if not selected_task_id:
            show_error(self.root, "错误", "请先选择一个任务")
            return

        if self.task_loader:
            try:
                if self.task_loader.enable_task(selected_task_id):
                    self._refresh_tasks()
                    self._add_log("INFO", f"任务已启用: {selected_task_id}")
                else:
                    show_error(self.root, "错误", "启用任务失败")
            except Exception as e:
                self._add_log("ERROR", f"启用任务失败: {e}")
                show_error(self.root, "错误", f"启用任务失败: {e}")

    def _disable_task(self):
        """禁用任务"""
        selected_task_id = self.task_list.get_selected_task_id()
        if not selected_task_id:
            show_error(self.root, "错误", "请先选择一个任务")
            return

        if self.task_loader:
            try:
                if self.task_loader.disable_task(selected_task_id):
                    self._refresh_tasks()
                    self._add_log("INFO", f"任务已禁用: {selected_task_id}")
                else:
                    show_error(self.root, "错误", "禁用任务失败")
            except Exception as e:
                self._add_log("ERROR", f"禁用任务失败: {e}")
                show_error(self.root, "错误", f"禁用任务失败: {e}")

    def _save_config(self):
        """保存配置"""
        try:
            # 这里可以实现保存当前配置的功能
            self._add_log("INFO", "配置已保存")
            show_info(self.root, "成功", "配置已保存")
        except Exception as e:
            self._add_log("ERROR", f"保存配置失败: {e}")
            show_error(self.root, "错误", f"保存配置失败: {e}")

    def _start_scheduler(self):
        """启动调度器"""
        if not self.task_scheduler:
            show_error(self.root, "错误", "任务调度器未初始化")
            return

        try:
            # 加载任务到调度器
            if self.task_loader:
                enabled_tasks = self.task_loader.get_enabled_tasks()
                for task_id, task_info in enabled_tasks.items():
                    self.task_scheduler.add_task(task_id, task_info.config)

            # 启动调度器
            if self.task_scheduler.start_scheduler():
                self.start_btn.config(state=tk.DISABLED)
                self.stop_btn.config(state=tk.NORMAL)
                self.status_bar.set_status("调度器运行中")
                self._add_log("INFO", "任务调度器已启动")
            else:
                show_error(self.root, "错误", "启动调度器失败")

        except Exception as e:
            self._add_log("ERROR", f"启动调度器失败: {e}")
            show_error(self.root, "错误", f"启动调度器失败: {e}")

    def _stop_scheduler(self):
        """停止调度器"""
        if not self.task_scheduler:
            return

        try:
            self.task_scheduler.stop_scheduler()
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.status_bar.set_status("就绪")
            self._add_log("INFO", "任务调度器已停止")

        except Exception as e:
            self._add_log("ERROR", f"停止调度器失败: {e}")
            show_error(self.root, "错误", f"停止调度器失败: {e}")

    def _refresh_tasks(self):
        """刷新任务列表"""
        try:
            if self.task_loader:
                self.task_loader.reload_tasks()
            self.task_list.refresh_tasks()
            self._add_log("DEBUG", "任务列表已刷新")
        except Exception as e:
            self._add_log("ERROR", f"刷新任务列表失败: {e}")

    def _clear_log(self):
        """清空日志"""
        self.log_text.clear_log()
        self._add_log("INFO", "日志已清空")

    def _show_window_capture_test(self):
        """显示窗口捕获测试"""
        # 切换到窗口捕获测试标签页
        self.right_notebook.select(1)  # 索引1是窗口捕获测试页面

    def _show_ocr_test(self):
        """显示OCR测试"""
        show_info(self.root, "提示", "OCR测试功能尚未实现")

    def _show_help(self):
        """显示帮助"""
        help_text = """SnowZone OCR自动化工具使用说明

基本操作：
1. 新建任务：点击"新建任务"按钮或使用Ctrl+N
2. 编辑任务：选择任务后点击"编辑任务"或双击任务
3. 删除任务：选择任务后点击"删除任务"或按Delete键
4. 启动调度器：点击"启动调度器"开始自动执行任务

窗口捕获测试：
1. 在"窗口捕获测试"标签页中测试窗口捕获功能
2. 输入游戏窗口标题，点击"查找窗口"
3. 找到窗口后可以捕获截图进行预览

任务配置：
- 基本信息：设置任务名称、描述、优先级等
- 触发器：配置OCR文字识别触发条件
- 动作：设置要执行的操作序列
- 执行条件：配置执行时间、冷却时间等

注意事项：
- 确保游戏窗口可见且未被遮挡
- OCR识别需要安装Tesseract OCR
- 建议先在测试环境中验证任务配置
"""

        # 创建帮助窗口
        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("600x500")
        help_window.transient(self.root)

        # 帮助文本
        help_text_widget = tk.Text(
            help_window,
            wrap=tk.WORD,
            font=GUIStyles.FONTS['default']
        )
        help_text_widget.insert('1.0', help_text)
        help_text_widget.config(state=tk.DISABLED)

        # 滚动条
        help_scroll = ttk.Scrollbar(help_window, orient=tk.VERTICAL,
                                   command=help_text_widget.yview)
        help_text_widget.configure(yscrollcommand=help_scroll.set)

        help_text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        help_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        center_window(help_window, 600, 500)

    def _show_about(self):
        """显示关于对话框"""
        about_text = f"""SnowZone OCR自动化工具

版本: {self.version_manager.get_current_version() if self.version_manager else '未知'}
开发团队: SnowZone开发团队

这是一个基于OCR技术的游戏自动化工具，
专为《尘白禁区》游戏设计。

技术栈：
- Python 3.8+
- Tkinter GUI
- Tesseract OCR
- OpenCV
- PyAutoGUI

使用声明：
本工具仅供学习和研究目的使用，
请遵守游戏服务条款和相关法律法规。
"""

        messagebox.showinfo("关于", about_text, parent=self.root)

    # 任务库回调事件
    def _on_library_task_selected(self, task_id: str):
        """任务库任务选择事件"""
        if self.task_library:
            task_template = self.task_library.get_task(task_id)
            if task_template:
                self.status_bar.set_status(f"已选择任务模板: {task_template.name}")

    def _on_library_task_edited(self, task_id: str):
        """任务库任务编辑事件"""
        try:
            # 这里可以实现任务模板编辑器
            # 暂时使用传统的任务编辑器
            result = show_task_editor(self.root, self.task_loader, task_id)
            if result == "save":
                self.task_library_widget.refresh_library()
                if task_id:
                    self._add_log("INFO", f"任务模板已更新: {task_id}")
                else:
                    self._add_log("INFO", "新任务模板已创建")
        except Exception as e:
            self._add_log("ERROR", f"编辑任务模板失败: {e}")
            show_error(self.root, "错误", f"编辑任务模板失败: {e}")

    def _on_library_task_deleted(self, task_id: str):
        """任务库任务删除事件"""
        try:
            if self.task_library and self.task_library.delete_task(task_id):
                self.task_library_widget.refresh_library()
                self._add_log("INFO", f"任务模板已删除: {task_id}")
            else:
                show_error(self.root, "错误", "删除任务模板失败")
        except Exception as e:
            self._add_log("ERROR", f"删除任务模板失败: {e}")
            show_error(self.root, "错误", f"删除任务模板失败: {e}")

    def _on_library_task_toggled(self, task_id: str):
        """任务库任务启用/禁用切换事件"""
        if not self.task_library:
            return

        try:
            task_template = self.task_library.get_task(task_id)
            if task_template:
                if task_template.enabled:
                    success = self.task_library.disable_task(task_id)
                    action = "禁用"
                else:
                    success = self.task_library.enable_task(task_id)
                    action = "启用"

                if success:
                    self.task_library_widget.refresh_library()
                    self._add_log("INFO", f"任务模板已{action}: {task_id}")
                else:
                    show_error(self.root, "错误", f"{action}任务模板失败")
        except Exception as e:
            self._add_log("ERROR", f"切换任务模板状态失败: {e}")
            show_error(self.root, "错误", f"切换任务模板状态失败: {e}")

    def _on_add_task_to_workflow(self, task_id: str):
        """添加任务到工作流事件"""
        try:
            # 切换到工作流标签页
            self.left_notebook.select(1)  # 工作流是第二个标签页

            # 添加任务到当前选中的工作流
            self.workflow_widget.add_task_to_workflow(task_id)

        except Exception as e:
            self._add_log("ERROR", f"添加任务到工作流失败: {e}")
            show_error(self.root, "错误", f"添加任务到工作流失败: {e}")

    # 工作流回调事件
    def _on_workflow_selected(self, workflow_id: str):
        """工作流选择事件"""
        if self.workflow_manager:
            workflow = self.workflow_manager.get_workflow(workflow_id)
            if workflow:
                self.status_bar.set_status(f"已选择工作流: {workflow.name}")

    def _on_workflow_edited(self, workflow_id: str):
        """工作流编辑事件"""
        try:
            # 这里可以实现工作流编辑器
            show_info(self.root, "提示", "工作流编辑器功能尚未实现")
        except Exception as e:
            self._add_log("ERROR", f"编辑工作流失败: {e}")
            show_error(self.root, "错误", f"编辑工作流失败: {e}")

    def _on_workflow_deleted(self, workflow_id: str):
        """工作流删除事件"""
        try:
            if self.workflow_manager and self.workflow_manager.delete_workflow(workflow_id):
                self.workflow_widget.refresh_workflows()
                self._add_log("INFO", f"工作流已删除: {workflow_id}")
            else:
                show_error(self.root, "错误", "删除工作流失败")
        except Exception as e:
            self._add_log("ERROR", f"删除工作流失败: {e}")
            show_error(self.root, "错误", f"删除工作流失败: {e}")

    def _on_workflow_executed(self, workflow_id: str):
        """工作流执行事件"""
        try:
            if self.workflow_manager:
                workflow = self.workflow_manager.get_workflow(workflow_id)
                if workflow:
                    self._add_log("INFO", f"开始执行工作流: {workflow.name}")

                    # 这里可以实现工作流执行逻辑
                    show_info(self.root, "提示", f"工作流执行功能尚未实现\n工作流: {workflow.name}")

                else:
                    show_error(self.root, "错误", "工作流不存在")
        except Exception as e:
            self._add_log("ERROR", f"执行工作流失败: {e}")
            show_error(self.root, "错误", f"执行工作流失败: {e}")

    def _quit_app(self):
        """退出应用程序"""
        if ask_yes_no(self.root, "确认退出", "确定要退出SnowZone OCR自动化工具吗？"):
            self._on_window_closing()

    # 任务列表回调事件
    def _on_task_selected(self, task_id: str):
        """任务选择事件"""
        if self.task_loader:
            task_info = self.task_loader.get_task(task_id)
            if task_info:
                self.status_bar.set_status(f"已选择任务: {task_info.name}")

    def _on_task_edited(self, task_id: str):
        """任务编辑事件"""
        try:
            result = show_task_editor(self.root, self.task_loader, task_id)
            if result == "save":
                self._refresh_tasks()
                if task_id:
                    self._add_log("INFO", f"任务已更新: {task_id}")
                else:
                    self._add_log("INFO", "新任务已创建")
        except Exception as e:
            self._add_log("ERROR", f"编辑任务失败: {e}")
            show_error(self.root, "错误", f"编辑任务失败: {e}")

    def _on_task_deleted(self, task_id: str):
        """任务删除事件"""
        try:
            if self.task_loader and self.task_loader.delete_task(task_id):
                self._refresh_tasks()
                self._add_log("INFO", f"任务已删除: {task_id}")
            else:
                show_error(self.root, "错误", "删除任务失败")
        except Exception as e:
            self._add_log("ERROR", f"删除任务失败: {e}")
            show_error(self.root, "错误", f"删除任务失败: {e}")

    def _on_task_toggled(self, task_id: str):
        """任务启用/禁用切换事件"""
        if not self.task_loader:
            return

        try:
            task_info = self.task_loader.get_task(task_id)
            if task_info:
                if task_info.enabled:
                    success = self.task_loader.disable_task(task_id)
                    action = "禁用"
                else:
                    success = self.task_loader.enable_task(task_id)
                    action = "启用"

                if success:
                    self._refresh_tasks()
                    self._add_log("INFO", f"任务已{action}: {task_id}")
                else:
                    show_error(self.root, "错误", f"{action}任务失败")
        except Exception as e:
            self._add_log("ERROR", f"切换任务状态失败: {e}")
            show_error(self.root, "错误", f"切换任务状态失败: {e}")

    def _on_window_closing(self):
        """窗口关闭事件"""
        try:
            # 停止调度器
            if self.task_scheduler:
                self.task_scheduler.stop_scheduler()

            # 停止配置管理器
            if self.config_manager:
                self.config_manager.stop()

            self._add_log("INFO", "SnowZone OCR自动化工具已退出")

        except Exception as e:
            print(f"关闭程序时出错: {e}")
        finally:
            self.root.destroy()

    def run(self):
        """运行主窗口"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self._on_window_closing()
        except Exception as e:
            self._add_log("ERROR", f"程序运行异常: {e}")
            show_error(self.root, "错误", f"程序运行异常: {e}")


def main():
    """主函数"""
    try:
        app = SnowZoneMainWindow()
        app.run()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
