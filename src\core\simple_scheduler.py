# -*- coding: utf-8 -*-
"""
简化任务调度器
不依赖opencv等外部库的基础调度器，用于测试和演示
"""

import time
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class SimpleTaskStatus(Enum):
    """简单任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class SimpleTaskExecution:
    """简单任务执行记录"""
    task_id: str
    start_time: datetime
    end_time: Optional[datetime]
    status: SimpleTaskStatus
    message: str


class SimpleTaskScheduler:
    """简化任务调度器类"""
    
    def __init__(self, logger=None):
        """
        初始化简化调度器
        
        Args:
            logger: 日志器实例
        """
        self.logger = logger
        self.tasks = {}
        self.executions = {}
        self.running = False
        self.scheduler_thread = None
    
    def add_task(self, task_id: str, task_config: Dict[str, Any]) -> bool:
        """
        添加任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            
        Returns:
            添加是否成功
        """
        try:
            self.tasks[task_id] = task_config
            self.executions[task_id] = []
            
            if self.logger:
                self.logger.info(f"任务已添加: {task_id}")
            
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"添加任务失败: {e}")
            return False
    
    def remove_task(self, task_id: str) -> bool:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            移除是否成功
        """
        if task_id in self.tasks:
            del self.tasks[task_id]
            if task_id in self.executions:
                del self.executions[task_id]
            
            if self.logger:
                self.logger.info(f"任务已移除: {task_id}")
            return True
        return False
    
    def start_scheduler(self) -> bool:
        """
        启动调度器
        
        Returns:
            启动是否成功
        """
        if self.running:
            if self.logger:
                self.logger.warning("调度器已在运行")
            return False
        
        try:
            self.running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            
            if self.logger:
                self.logger.info("简化任务调度器已启动")
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"启动调度器失败: {e}")
            return False
    
    def stop_scheduler(self):
        """停止调度器"""
        if not self.running:
            return
        
        self.running = False
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5.0)
        
        if self.logger:
            self.logger.info("简化任务调度器已停止")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        while self.running:
            try:
                # 简化的任务检查逻辑
                for task_id, task_config in self.tasks.items():
                    if self._should_execute_task(task_id, task_config):
                        self._execute_simple_task(task_id, task_config)
                
                time.sleep(5.0)  # 每5秒检查一次
                
            except Exception as e:
                if self.logger:
                    self.logger.error(f"调度器循环异常: {e}")
                time.sleep(5.0)
    
    def _should_execute_task(self, task_id: str, task_config: Dict[str, Any]) -> bool:
        """
        检查任务是否应该执行（简化版本）
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            
        Returns:
            是否应该执行
        """
        # 检查任务是否启用
        task_info = task_config.get("task_info", {})
        if not task_info.get("enabled", True):
            return False
        
        # 简化的执行条件：每次都不执行（演示模式）
        return False
    
    def _execute_simple_task(self, task_id: str, task_config: Dict[str, Any]):
        """
        执行简化任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
        """
        execution = SimpleTaskExecution(
            task_id=task_id,
            start_time=datetime.now(),
            end_time=None,
            status=SimpleTaskStatus.RUNNING,
            message="开始执行"
        )
        
        self.executions.setdefault(task_id, []).append(execution)
        
        if self.logger:
            self.logger.info(f"开始执行简化任务: {task_id}")
        
        try:
            # 模拟任务执行
            time.sleep(1.0)
            
            execution.end_time = datetime.now()
            execution.status = SimpleTaskStatus.COMPLETED
            execution.message = "执行完成"
            
            if self.logger:
                self.logger.info(f"简化任务执行成功: {task_id}")
            
        except Exception as e:
            execution.end_time = datetime.now()
            execution.status = SimpleTaskStatus.FAILED
            execution.message = f"执行失败: {e}"
            
            if self.logger:
                self.logger.error(f"简化任务执行失败: {task_id} - {e}")
    
    def get_task_status(self, task_id: str) -> Optional[List[SimpleTaskExecution]]:
        """
        获取任务执行状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            执行记录列表
        """
        return self.executions.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务配置"""
        return self.tasks.copy()
    
    def get_scheduler_stats(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        total_tasks = len(self.tasks)
        total_executions = sum(len(execs) for execs in self.executions.values())
        
        return {
            "running": self.running,
            "total_tasks": total_tasks,
            "total_executions": total_executions,
            "tasks": list(self.tasks.keys())
        }


if __name__ == "__main__":
    # 测试简化调度器
    print("测试简化任务调度器...")
    
    scheduler = SimpleTaskScheduler()
    
    # 添加测试任务
    test_task = {
        "task_info": {
            "name": "测试任务",
            "enabled": True
        },
        "triggers": [],
        "actions": []
    }
    
    scheduler.add_task("test_task", test_task)
    
    # 启动调度器
    scheduler.start_scheduler()
    
    # 运行一段时间
    time.sleep(2.0)
    
    # 停止调度器
    scheduler.stop_scheduler()
    
    # 显示统计信息
    stats = scheduler.get_scheduler_stats()
    print(f"调度器统计: {stats}")
    
    print("简化调度器测试完成")
