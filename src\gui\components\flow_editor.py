# -*- coding: utf-8 -*-
"""
任务流编辑器组件
提供完整的任务流可视化编辑功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from task.task_flow import TaskFlow, FlowStep, ExecutionMode
    from task.base_task import BaseTask
    from task.integration import get_integration
    TASK_MODULE_AVAILABLE = True
except ImportError:
    TASK_MODULE_AVAILABLE = False

from .flow_visualizer import FlowVisualizerWidget


class FlowStepConfigDialog:
    """任务流步骤配置对话框"""
    
    def __init__(self, parent, step: FlowStep = None, available_tasks: List[BaseTask] = None):
        """
        初始化对话框
        
        Args:
            parent: 父窗口
            step: 要编辑的步骤（None表示新建）
            available_tasks: 可用任务列表
        """
        self.parent = parent
        self.step = step
        self.available_tasks = available_tasks or []
        self.result = None
        
        self._create_dialog()
    
    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("步骤配置" if self.step else "新建步骤")
        self.dialog.geometry("500x600")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 基本信息
        self._create_basic_info_section(main_frame)
        
        # 执行配置
        self._create_execution_config_section(main_frame)
        
        # 高级配置
        self._create_advanced_config_section(main_frame)
        
        # 按钮
        self._create_buttons(main_frame)
        
        # 加载现有数据
        if self.step:
            self._load_step_data()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _create_basic_info_section(self, parent):
        """创建基本信息区域"""
        basic_frame = ttk.LabelFrame(parent, text="基本信息")
        basic_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 任务选择
        ttk.Label(basic_frame, text="选择任务:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.task_var = tk.StringVar()
        self.task_combo = ttk.Combobox(basic_frame, textvariable=self.task_var, state="readonly")
        self.task_combo['values'] = [f"{task.task_name} ({task.task_id})" for task in self.available_tasks]
        self.task_combo.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # 执行顺序
        ttk.Label(basic_frame, text="执行顺序:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.order_var = tk.IntVar(value=1)
        self.order_spin = ttk.Spinbox(basic_frame, from_=1, to=100, textvariable=self.order_var, width=10)
        self.order_spin.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 启用状态
        self.enabled_var = tk.BooleanVar(value=True)
        self.enabled_check = ttk.Checkbutton(basic_frame, text="启用此步骤", variable=self.enabled_var)
        self.enabled_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        basic_frame.grid_columnconfigure(1, weight=1)
    
    def _create_execution_config_section(self, parent):
        """创建执行配置区域"""
        exec_frame = ttk.LabelFrame(parent, text="执行配置")
        exec_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 执行前延迟
        ttk.Label(exec_frame, text="执行前延迟(秒):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.delay_before_var = tk.DoubleVar(value=0.0)
        self.delay_before_spin = ttk.Spinbox(exec_frame, from_=0.0, to=60.0, increment=0.1, 
                                           textvariable=self.delay_before_var, width=10)
        self.delay_before_spin.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 执行后延迟
        ttk.Label(exec_frame, text="执行后延迟(秒):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.delay_after_var = tk.DoubleVar(value=0.0)
        self.delay_after_spin = ttk.Spinbox(exec_frame, from_=0.0, to=60.0, increment=0.1,
                                          textvariable=self.delay_after_var, width=10)
        self.delay_after_spin.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 出错时继续
        self.continue_on_error_var = tk.BooleanVar(value=True)
        self.continue_check = ttk.Checkbutton(exec_frame, text="出错时继续执行", 
                                            variable=self.continue_on_error_var)
        self.continue_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
    
    def _create_advanced_config_section(self, parent):
        """创建高级配置区域"""
        advanced_frame = ttk.LabelFrame(parent, text="高级配置")
        advanced_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 参数配置
        ttk.Label(advanced_frame, text="参数配置 (JSON格式):").pack(anchor=tk.W, padx=5, pady=(5, 0))
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(advanced_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.params_text = tk.Text(text_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.params_text.yview)
        self.params_text.configure(yscrollcommand=scrollbar.set)
        
        self.params_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 默认参数
        self.params_text.insert(tk.END, "{}")
    
    def _create_buttons(self, parent):
        """创建按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="确定", command=self._on_ok).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="验证参数", command=self._validate_params).pack(side=tk.LEFT)
    
    def _load_step_data(self):
        """加载步骤数据"""
        if not self.step:
            return
        
        # 查找对应的任务
        for i, task in enumerate(self.available_tasks):
            if task.task_id == self.step.task.task_id:
                self.task_combo.current(i)
                break
        
        self.order_var.set(self.step.order)
        self.enabled_var.set(self.step.enabled)
        self.delay_before_var.set(self.step.delay_before)
        self.delay_after_var.set(self.step.delay_after)
        self.continue_on_error_var.set(self.step.continue_on_error)
        
        # 加载参数
        if self.step.parameters:
            self.params_text.delete(1.0, tk.END)
            self.params_text.insert(tk.END, json.dumps(self.step.parameters, ensure_ascii=False, indent=2))
    
    def _validate_params(self):
        """验证参数"""
        try:
            params_text = self.params_text.get(1.0, tk.END).strip()
            if params_text:
                json.loads(params_text)
            messagebox.showinfo("验证结果", "参数格式正确")
        except json.JSONDecodeError as e:
            messagebox.showerror("验证失败", f"参数格式错误:\n{str(e)}")
    
    def _on_ok(self):
        """确定按钮事件"""
        try:
            # 验证输入
            if not self.task_var.get():
                messagebox.showerror("错误", "请选择任务")
                return
            
            # 获取选中的任务
            task_index = self.task_combo.current()
            if task_index < 0:
                messagebox.showerror("错误", "请选择有效的任务")
                return
            
            selected_task = self.available_tasks[task_index]
            
            # 解析参数
            params_text = self.params_text.get(1.0, tk.END).strip()
            parameters = {}
            if params_text:
                try:
                    parameters = json.loads(params_text)
                except json.JSONDecodeError as e:
                    messagebox.showerror("错误", f"参数格式错误:\n{str(e)}")
                    return
            
            # 创建结果
            self.result = {
                'task': selected_task,
                'order': self.order_var.get(),
                'enabled': self.enabled_var.get(),
                'delay_before': self.delay_before_var.get(),
                'delay_after': self.delay_after_var.get(),
                'continue_on_error': self.continue_on_error_var.get(),
                'parameters': parameters
            }
            
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败:\n{str(e)}")
    
    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()
    
    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


class FlowEditorWidget(ttk.Frame):
    """任务流编辑器组件"""
    
    def __init__(self, parent, **kwargs):
        """初始化编辑器"""
        super().__init__(parent, **kwargs)
        
        self.current_flow: Optional[TaskFlow] = None
        self.available_tasks: List[BaseTask] = []
        self.task_integration = None
        self.modified = False
        
        # 回调函数
        self.on_flow_changed: Optional[Callable[[TaskFlow], None]] = None
        self.on_modified_changed: Optional[Callable[[bool], None]] = None
        
        self._create_widgets()
        self._setup_layout()
        
        # 初始化任务集成
        self._init_task_integration()
    
    def _init_task_integration(self):
        """初始化任务集成"""
        if TASK_MODULE_AVAILABLE:
            try:
                self.task_integration = get_integration()
                self._refresh_available_tasks()
            except Exception as e:
                print(f"任务集成初始化失败: {e}")
    
    def _create_widgets(self):
        """创建子组件"""
        # 工具栏
        self.toolbar = ttk.Frame(self)
        
        # 文件操作
        ttk.Button(self.toolbar, text="新建", command=self._new_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="打开", command=self._open_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="保存", command=self._save_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="另存为", command=self._save_flow_as).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # 编辑操作
        ttk.Button(self.toolbar, text="添加步骤", command=self._add_step).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="编辑步骤", command=self._edit_step).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="删除步骤", command=self._delete_step).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # 执行操作
        ttk.Button(self.toolbar, text="验证", command=self._validate_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="测试执行", command=self._test_flow).pack(side=tk.LEFT, padx=2)
        
        # 执行模式选择
        ttk.Label(self.toolbar, text="执行模式:").pack(side=tk.RIGHT, padx=(10, 5))
        self.execution_mode_var = tk.StringVar(value="sequential")
        self.execution_mode_combo = ttk.Combobox(self.toolbar, textvariable=self.execution_mode_var,
                                               values=["sequential", "parallel", "conditional"],
                                               state="readonly", width=12)
        self.execution_mode_combo.pack(side=tk.RIGHT, padx=2)
        self.execution_mode_combo.bind('<<ComboboxSelected>>', self._on_execution_mode_changed)
        
        # 主编辑区域
        self.main_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        
        # 左侧步骤列表
        self._create_steps_panel()
        
        # 右侧可视化区域
        self._create_visualizer_panel()
        
        self.main_paned.add(self.steps_frame, weight=1)
        self.main_paned.add(self.visualizer_frame, weight=2)
    
    def _create_steps_panel(self):
        """创建步骤面板"""
        self.steps_frame = ttk.LabelFrame(self.main_paned, text="任务步骤")
        
        # 步骤列表
        list_frame = ttk.Frame(self.steps_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Treeview
        self.steps_tree = ttk.Treeview(list_frame, columns=("order", "delay", "status"), show="tree headings")
        self.steps_tree.heading("#0", text="任务名称")
        self.steps_tree.heading("order", text="顺序")
        self.steps_tree.heading("delay", text="延迟")
        self.steps_tree.heading("status", text="状态")
        
        self.steps_tree.column("#0", width=200)
        self.steps_tree.column("order", width=50)
        self.steps_tree.column("delay", width=80)
        self.steps_tree.column("status", width=60)
        
        # 滚动条
        steps_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.steps_tree.yview)
        self.steps_tree.configure(yscrollcommand=steps_scrollbar.set)
        
        self.steps_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        steps_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.steps_tree.bind('<Double-1>', lambda e: self._edit_step())
        self.steps_tree.bind('<Button-3>', self._show_step_context_menu)
        
        # 右键菜单
        self.step_context_menu = tk.Menu(self, tearoff=0)
        self.step_context_menu.add_command(label="编辑", command=self._edit_step)
        self.step_context_menu.add_command(label="删除", command=self._delete_step)
        self.step_context_menu.add_separator()
        self.step_context_menu.add_command(label="上移", command=self._move_step_up)
        self.step_context_menu.add_command(label="下移", command=self._move_step_down)
        self.step_context_menu.add_separator()
        self.step_context_menu.add_command(label="启用/禁用", command=self._toggle_step)
    
    def _create_visualizer_panel(self):
        """创建可视化面板"""
        self.visualizer_frame = ttk.LabelFrame(self.main_paned, text="流程图")
        
        # 创建可视化组件
        self.visualizer = FlowVisualizerWidget(self.visualizer_frame)
        self.visualizer.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _setup_layout(self):
        """设置布局"""
        self.toolbar.pack(fill=tk.X, padx=5, pady=5)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
    
    def _refresh_available_tasks(self):
        """刷新可用任务列表"""
        if self.task_integration:
            try:
                task_data = self.task_integration.get_available_tasks()
                self.available_tasks = []
                
                # 这里需要重新构建任务对象
                # 实际实现中需要从TaskManager获取任务实例
                for task_info in task_data:
                    # 暂时创建占位符任务
                    pass
                    
            except Exception as e:
                print(f"刷新可用任务失败: {e}")
    
    def _new_flow(self):
        """新建任务流"""
        if self.modified:
            if not messagebox.askyesno("确认", "当前任务流已修改，是否放弃修改？"):
                return
        
        flow_id = simpledialog.askstring("新建任务流", "请输入任务流ID:")
        if not flow_id:
            return
        
        flow_name = simpledialog.askstring("新建任务流", "请输入任务流名称:")
        if not flow_name:
            return
        
        description = simpledialog.askstring("新建任务流", "请输入任务流描述:", initialvalue="")
        
        # 创建新任务流
        if TASK_MODULE_AVAILABLE:
            self.current_flow = TaskFlow(flow_id, flow_name, description or "")
        else:
            self.current_flow = None
        
        self._refresh_ui()
        self._set_modified(False)
    
    def _open_flow(self):
        """打开任务流"""
        messagebox.showinfo("提示", "打开任务流功能开发中")
    
    def _save_flow(self):
        """保存任务流"""
        if not self.current_flow:
            messagebox.showwarning("警告", "没有可保存的任务流")
            return
        
        messagebox.showinfo("提示", "保存任务流功能开发中")
        self._set_modified(False)
    
    def _save_flow_as(self):
        """另存为任务流"""
        messagebox.showinfo("提示", "另存为功能开发中")
    
    def _add_step(self):
        """添加步骤"""
        if not self.current_flow:
            messagebox.showwarning("警告", "请先创建或打开任务流")
            return
        
        dialog = FlowStepConfigDialog(self, available_tasks=self.available_tasks)
        result = dialog.show()
        
        if result:
            # 添加步骤到任务流
            if TASK_MODULE_AVAILABLE and self.current_flow:
                self.current_flow.add_task(
                    task=result['task'],
                    order=result['order'],
                    delay_before=result['delay_before'],
                    delay_after=result['delay_after'],
                    continue_on_error=result['continue_on_error'],
                    parameters=result['parameters']
                )
            
            self._refresh_ui()
            self._set_modified(True)
    
    def _edit_step(self):
        """编辑步骤"""
        selected = self.steps_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择要编辑的步骤")
            return
        
        messagebox.showinfo("提示", "编辑步骤功能开发中")
    
    def _delete_step(self):
        """删除步骤"""
        selected = self.steps_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择要删除的步骤")
            return
        
        if messagebox.askyesno("确认删除", "确定要删除选中的步骤吗？"):
            # 从任务流中删除步骤
            step_id = selected[0]
            if TASK_MODULE_AVAILABLE and self.current_flow:
                self.current_flow.remove_task(step_id)
            
            self._refresh_ui()
            self._set_modified(True)
    
    def _move_step_up(self):
        """上移步骤"""
        messagebox.showinfo("提示", "移动步骤功能开发中")
    
    def _move_step_down(self):
        """下移步骤"""
        messagebox.showinfo("提示", "移动步骤功能开发中")
    
    def _toggle_step(self):
        """切换步骤启用状态"""
        messagebox.showinfo("提示", "切换步骤状态功能开发中")
    
    def _validate_flow(self):
        """验证任务流"""
        if not self.current_flow:
            messagebox.showwarning("警告", "没有可验证的任务流")
            return
        
        if TASK_MODULE_AVAILABLE:
            is_valid, errors = self.current_flow.validate()
            if is_valid:
                messagebox.showinfo("验证结果", "任务流配置正确")
            else:
                error_msg = "任务流配置错误:\n" + "\n".join(errors)
                messagebox.showerror("验证失败", error_msg)
        else:
            messagebox.showinfo("验证结果", "任务模块未加载，无法验证")
    
    def _test_flow(self):
        """测试执行任务流"""
        if not self.current_flow:
            messagebox.showwarning("警告", "没有可测试的任务流")
            return
        
        messagebox.showinfo("提示", "测试执行功能开发中")
    
    def _on_execution_mode_changed(self, event):
        """执行模式改变事件"""
        if not self.current_flow:
            return
        
        mode_str = self.execution_mode_var.get()
        if TASK_MODULE_AVAILABLE:
            if mode_str == "parallel":
                self.current_flow.set_execution_mode(ExecutionMode.PARALLEL)
            elif mode_str == "conditional":
                self.current_flow.set_execution_mode(ExecutionMode.CONDITIONAL)
            else:
                self.current_flow.set_execution_mode(ExecutionMode.SEQUENTIAL)
        
        self._set_modified(True)
    
    def _show_step_context_menu(self, event):
        """显示步骤右键菜单"""
        item = self.steps_tree.identify_row(event.y)
        if item:
            self.steps_tree.selection_set(item)
            try:
                self.step_context_menu.post(event.x_root, event.y_root)
            finally:
                self.step_context_menu.grab_release()
    
    def _refresh_ui(self):
        """刷新界面"""
        # 清空步骤列表
        for item in self.steps_tree.get_children():
            self.steps_tree.delete(item)
        
        # 重新加载步骤
        if self.current_flow and TASK_MODULE_AVAILABLE:
            for step in self.current_flow.steps:
                status = "✅" if step.enabled else "❌"
                delay_info = f"{step.delay_before}+{step.delay_after}"
                
                self.steps_tree.insert("", "end", iid=step.id,
                                     text=step.task.task_name,
                                     values=(step.order, delay_info, status))
            
            # 更新可视化
            self.visualizer.set_task_flow(self.current_flow)
            
            # 更新执行模式
            mode_map = {
                ExecutionMode.SEQUENTIAL: "sequential",
                ExecutionMode.PARALLEL: "parallel", 
                ExecutionMode.CONDITIONAL: "conditional"
            }
            self.execution_mode_var.set(mode_map.get(self.current_flow.execution_mode, "sequential"))
    
    def _set_modified(self, modified: bool):
        """设置修改状态"""
        self.modified = modified
        if self.on_modified_changed:
            self.on_modified_changed(modified)
    
    def set_task_flow(self, task_flow: TaskFlow):
        """设置当前编辑的任务流"""
        self.current_flow = task_flow
        self._refresh_ui()
        self._set_modified(False)
        
        if self.on_flow_changed:
            self.on_flow_changed(task_flow)
    
    def get_task_flow(self) -> Optional[TaskFlow]:
        """获取当前任务流"""
        return self.current_flow
