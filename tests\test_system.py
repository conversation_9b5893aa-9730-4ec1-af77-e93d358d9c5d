# -*- coding: utf-8 -*-
"""
系统测试脚本
用于测试SnowZone OCR自动化工具的各个组件
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from src.utils.logger import get_logger
        from src.utils.version_manager import VersionManager
        from src.utils.exception_handler import ExceptionHandler
        from src.config.config_manager import ConfigManager
        from src.config.task_loader import TaskLoader
        print("✓ 工具模块导入成功")
    except Exception as e:
        print(f"✗ 工具模块导入失败: {e}")
        return False
    
    try:
        from src.core.window_capture import WindowCapture
        from src.core.image_processor import ImageProcessor
        from src.core.ocr_engine import OCREngine
        from src.core.action_executor import ActionExecutor
        from src.core.task_scheduler import TaskScheduler
        print("✓ 核心模块导入成功")
    except Exception as e:
        print(f"✗ 核心模块导入失败: {e}")
        return False
    
    return True


def test_version_manager():
    """测试版本管理器"""
    print("\n测试版本管理器...")
    
    try:
        from src.utils.version_manager import VersionManager
        
        vm = VersionManager()
        current_version = vm.get_current_version()
        print(f"✓ 当前版本: {current_version}")
        
        history = vm.get_version_history()
        print(f"✓ 版本历史记录: {len(history)} 条")
        
        return True
    except Exception as e:
        print(f"✗ 版本管理器测试失败: {e}")
        return False


def test_logger():
    """测试日志系统"""
    print("\n测试日志系统...")
    
    try:
        from src.utils.logger import get_logger
        
        logger = get_logger()
        logger.info("日志系统测试消息")
        logger.debug("调试消息")
        logger.warning("警告消息")
        
        print("✓ 日志系统工作正常")
        return True
    except Exception as e:
        print(f"✗ 日志系统测试失败: {e}")
        return False


def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        from src.config.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试获取主配置
        main_config = config_manager.get_config("main")
        if main_config:
            print(f"✓ 主配置加载成功，版本: {main_config.get('version')}")
        else:
            print("⚠ 主配置未找到")
        
        # 测试获取任务配置
        task_configs = config_manager.get_task_configs()
        print(f"✓ 任务配置: {len(task_configs)} 个")
        
        config_manager.stop()
        return True
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False


def test_task_loader():
    """测试任务加载器"""
    print("\n测试任务加载器...")
    
    try:
        from src.config.task_loader import TaskLoader
        
        task_loader = TaskLoader()
        
        # 获取任务摘要
        summary = task_loader.get_task_summary()
        print(f"✓ 任务摘要: 总计 {summary['total_tasks']} 个任务")
        print(f"  - 启用: {summary['enabled_tasks']} 个")
        print(f"  - 禁用: {summary['disabled_tasks']} 个")
        
        # 列出任务
        for task in summary['task_list']:
            status = "启用" if task['enabled'] else "禁用"
            print(f"  - {task['name']} ({task['id']}) [{status}]")
        
        return True
    except Exception as e:
        print(f"✗ 任务加载器测试失败: {e}")
        return False


def test_core_modules():
    """测试核心模块"""
    print("\n测试核心模块...")
    
    # 测试图像处理器
    try:
        from src.core.image_processor import ImageProcessor
        import numpy as np
        
        processor = ImageProcessor()
        test_image = np.random.randint(0, 255, (100, 200, 3), dtype=np.uint8)
        processed = processor.preprocess_image(test_image)
        print(f"✓ 图像处理器: {test_image.shape} -> {processed.shape}")
    except Exception as e:
        print(f"✗ 图像处理器测试失败: {e}")
        return False
    
    # 测试窗口捕获器（不需要实际窗口）
    try:
        from src.core.window_capture import WindowCapture
        
        capture = WindowCapture()
        windows = capture._enumerate_windows()
        print(f"✓ 窗口捕获器: 找到 {len(windows)} 个窗口")
    except Exception as e:
        print(f"✗ 窗口捕获器测试失败: {e}")
        return False
    
    # 测试OCR引擎（需要tesseract）
    try:
        from src.core.ocr_engine import OCREngine
        
        ocr = OCREngine()
        languages = ocr.get_supported_languages()
        print(f"✓ OCR引擎: 支持 {len(languages)} 种语言")
    except Exception as e:
        print(f"⚠ OCR引擎测试失败: {e}")
        print("  请确保已安装Tesseract OCR")
    
    # 测试动作执行器
    try:
        from src.core.action_executor import ActionExecutor
        
        executor = ActionExecutor()
        result = executor.wait_for_delay(0.1)
        print(f"✓ 动作执行器: 等待测试 {result.success}")
    except Exception as e:
        print(f"⚠ 动作执行器测试失败: {e}")
        print("  请确保已安装pyautogui和pywin32")
    
    return True


def test_exception_handler():
    """测试异常处理器"""
    print("\n测试异常处理器...")
    
    try:
        from src.utils.exception_handler import ExceptionHandler, OCRException
        
        handler = ExceptionHandler()
        
        # 测试异常处理
        try:
            raise OCRException("测试OCR异常")
        except Exception as e:
            handler.handle_exception(e)
        
        stats = handler.get_error_stats()
        print(f"✓ 异常处理器: 处理了 {stats['total_errors']} 个错误")
        
        return True
    except Exception as e:
        print(f"✗ 异常处理器测试失败: {e}")
        return False


def test_task_scheduler():
    """测试任务调度器"""
    print("\n测试任务调度器...")
    
    try:
        from src.core.task_scheduler import TaskScheduler
        
        scheduler = TaskScheduler()
        
        # 创建测试任务
        test_task = {
            "task_info": {
                "name": "测试任务",
                "enabled": True
            },
            "triggers": [
                {
                    "type": "text_recognition",
                    "target_text": "测试",
                    "confidence": 0.8
                }
            ],
            "actions": [
                {
                    "type": "wait",
                    "delay": 0.1
                }
            ]
        }
        
        success = scheduler.add_task("test_task", test_task)
        print(f"✓ 任务调度器: 添加测试任务 {success}")
        
        tasks = scheduler.get_all_tasks()
        print(f"✓ 任务调度器: 管理 {len(tasks)} 个任务")
        
        return True
    except Exception as e:
        print(f"✗ 任务调度器测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("SnowZone OCR自动化工具 - 系统测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("版本管理器", test_version_manager),
        ("日志系统", test_logger),
        ("配置管理器", test_config_manager),
        ("任务加载器", test_task_loader),
        ("核心模块", test_core_modules),
        ("异常处理器", test_exception_handler),
        ("任务调度器", test_task_scheduler)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关组件")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
