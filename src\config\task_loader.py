# -*- coding: utf-8 -*-
"""
任务加载器
负责加载和管理任务配置文件
"""

import os
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass

from .config_manager import ConfigManager


@dataclass
class TaskInfo:
    """任务信息数据类"""
    id: str
    name: str
    description: str
    enabled: bool
    priority: int
    config: Dict[str, Any]


class TaskLoader:
    """任务加载器类"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None, 
                 tasks_dir: str = "config/tasks"):
        """
        初始化任务加载器
        
        Args:
            config_manager: 配置管理器实例
            tasks_dir: 任务配置目录
        """
        self.config_manager = config_manager or ConfigManager()
        self.tasks_dir = Path(tasks_dir)
        self.tasks = {}
        
        # 确保任务目录存在
        self.tasks_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载所有任务
        self.load_all_tasks()
    
    def load_all_tasks(self) -> Dict[str, TaskInfo]:
        """
        加载所有任务配置
        
        Returns:
            任务信息字典
        """
        self.tasks.clear()
        
        # 从配置管理器获取任务配置
        task_configs = self.config_manager.get_task_configs()
        
        for task_id, config in task_configs.items():
            try:
                task_info = self._create_task_info(task_id, config)
                self.tasks[task_id] = task_info
                print(f"任务已加载: {task_id} - {task_info.name}")
            except Exception as e:
                print(f"加载任务失败: {task_id} - {e}")
        
        print(f"共加载 {len(self.tasks)} 个任务")
        return self.tasks
    
    def _create_task_info(self, task_id: str, config: Dict[str, Any]) -> TaskInfo:
        """
        创建任务信息对象
        
        Args:
            task_id: 任务ID
            config: 任务配置
            
        Returns:
            任务信息对象
        """
        task_info_config = config.get("task_info", {})
        
        return TaskInfo(
            id=task_id,
            name=task_info_config.get("name", task_id),
            description=task_info_config.get("description", ""),
            enabled=task_info_config.get("enabled", True),
            priority=task_info_config.get("priority", 1),
            config=config
        )
    
    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        """
        获取指定任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务信息
        """
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, TaskInfo]:
        """获取所有任务"""
        return self.tasks.copy()
    
    def get_enabled_tasks(self) -> Dict[str, TaskInfo]:
        """获取所有启用的任务"""
        return {
            task_id: task_info 
            for task_id, task_info in self.tasks.items() 
            if task_info.enabled
        }
    
    def get_tasks_by_priority(self, ascending: bool = True) -> List[TaskInfo]:
        """
        按优先级获取任务列表
        
        Args:
            ascending: 是否升序排列
            
        Returns:
            按优先级排序的任务列表
        """
        tasks = list(self.tasks.values())
        return sorted(tasks, key=lambda x: x.priority, reverse=not ascending)
    
    def create_task_template(self, task_id: str, task_name: str) -> Dict[str, Any]:
        """
        创建任务配置模板
        
        Args:
            task_id: 任务ID
            task_name: 任务名称
            
        Returns:
            任务配置模板
        """
        template = {
            "task_info": {
                "name": task_name,
                "description": f"{task_name}的自动化任务",
                "version": "0_0001",
                "enabled": True,
                "priority": 1
            },
            "triggers": [
                {
                    "id": f"{task_id}_trigger",
                    "type": "text_recognition",
                    "target_text": "目标文字",
                    "confidence": 0.8,
                    "region": {
                        "x": 0,
                        "y": 0,
                        "width": 1920,
                        "height": 1080
                    },
                    "preprocessing": {
                        "enhance_contrast": True,
                        "scale_factor": 2.0
                    }
                }
            ],
            "actions": [
                {
                    "id": f"{task_id}_click",
                    "type": "click",
                    "trigger_id": f"{task_id}_trigger",
                    "position": {
                        "type": "auto",
                        "offset_x": 0,
                        "offset_y": 0
                    },
                    "timing": {
                        "delay_before": 1.0,
                        "delay_after": 2.0
                    },
                    "retry": {
                        "max_attempts": 3,
                        "retry_delay": 1.0
                    }
                }
            ],
            "flow_control": {
                "execution_order": [f"{task_id}_click"],
                "loop_enabled": False,
                "loop_count": 1,
                "continue_on_error": True
            },
            "conditions": {
                "time_range": {
                    "start": "00:00",
                    "end": "23:59"
                },
                "cooldown": 3600,
                "max_executions_per_day": 1
            }
        }
        
        return template
    
    def save_task(self, task_id: str, config: Dict[str, Any]) -> bool:
        """
        保存任务配置
        
        Args:
            task_id: 任务ID
            config: 任务配置
            
        Returns:
            保存是否成功
        """
        try:
            # 验证配置
            self._validate_task_config(config)
            
            # 保存到文件
            task_file = self.tasks_dir / f"{task_id}.json"
            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            # 更新内存中的任务
            task_info = self._create_task_info(task_id, config)
            self.tasks[task_id] = task_info
            
            print(f"任务已保存: {task_id}")
            return True
            
        except Exception as e:
            print(f"保存任务失败: {task_id} - {e}")
            return False
    
    def _validate_task_config(self, config: Dict[str, Any]):
        """
        验证任务配置
        
        Args:
            config: 任务配置
        """
        required_sections = ["task_info", "triggers", "actions"]
        
        for section in required_sections:
            if section not in config:
                raise ValueError(f"缺少必需的配置节: {section}")
        
        # 验证任务信息
        task_info = config["task_info"]
        if "name" not in task_info:
            raise ValueError("任务信息中缺少名称")
        
        # 验证触发器
        triggers = config["triggers"]
        if not isinstance(triggers, list) or len(triggers) == 0:
            raise ValueError("触发器配置无效")
        
        for trigger in triggers:
            if "type" not in trigger:
                raise ValueError("触发器缺少类型")
        
        # 验证动作
        actions = config["actions"]
        if not isinstance(actions, list) or len(actions) == 0:
            raise ValueError("动作配置无效")
        
        for action in actions:
            if "type" not in action:
                raise ValueError("动作缺少类型")
    
    def delete_task(self, task_id: str) -> bool:
        """
        删除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            删除是否成功
        """
        try:
            # 从内存中删除
            if task_id in self.tasks:
                del self.tasks[task_id]
            
            # 删除文件
            task_file = self.tasks_dir / f"{task_id}.json"
            if task_file.exists():
                task_file.unlink()
            
            print(f"任务已删除: {task_id}")
            return True
            
        except Exception as e:
            print(f"删除任务失败: {task_id} - {e}")
            return False
    
    def enable_task(self, task_id: str) -> bool:
        """
        启用任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作是否成功
        """
        return self._set_task_enabled(task_id, True)
    
    def disable_task(self, task_id: str) -> bool:
        """
        禁用任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作是否成功
        """
        return self._set_task_enabled(task_id, False)
    
    def _set_task_enabled(self, task_id: str, enabled: bool) -> bool:
        """
        设置任务启用状态
        
        Args:
            task_id: 任务ID
            enabled: 是否启用
            
        Returns:
            操作是否成功
        """
        task_info = self.tasks.get(task_id)
        if not task_info:
            print(f"任务不存在: {task_id}")
            return False
        
        try:
            # 更新配置
            config = task_info.config.copy()
            config["task_info"]["enabled"] = enabled
            
            # 保存配置
            if self.save_task(task_id, config):
                task_info.enabled = enabled
                status = "启用" if enabled else "禁用"
                print(f"任务已{status}: {task_id}")
                return True
            
            return False
            
        except Exception as e:
            print(f"设置任务状态失败: {task_id} - {e}")
            return False
    
    def get_task_summary(self) -> Dict[str, Any]:
        """
        获取任务摘要信息
        
        Returns:
            任务摘要
        """
        total_tasks = len(self.tasks)
        enabled_tasks = len(self.get_enabled_tasks())
        disabled_tasks = total_tasks - enabled_tasks
        
        return {
            "total_tasks": total_tasks,
            "enabled_tasks": enabled_tasks,
            "disabled_tasks": disabled_tasks,
            "task_list": [
                {
                    "id": task_info.id,
                    "name": task_info.name,
                    "enabled": task_info.enabled,
                    "priority": task_info.priority
                }
                for task_info in self.tasks.values()
            ]
        }
    
    def reload_tasks(self):
        """重新加载所有任务"""
        print("重新加载任务配置...")
        self.load_all_tasks()


if __name__ == "__main__":
    # 测试任务加载器
    print("测试任务加载器...")
    
    try:
        task_loader = TaskLoader()
        
        # 获取任务摘要
        summary = task_loader.get_task_summary()
        print(f"任务摘要: {summary}")
        
        # 创建测试任务
        test_config = task_loader.create_task_template("test_task", "测试任务")
        if task_loader.save_task("test_task", test_config):
            print("测试任务创建成功")
        
        print("任务加载器测试完成")
        
    except Exception as e:
        print(f"任务加载器测试失败: {e}")
