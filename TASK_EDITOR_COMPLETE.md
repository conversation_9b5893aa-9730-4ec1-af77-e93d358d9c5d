# 🎯 SnowZone V2 任务编辑器完整功能

## 📋 功能概述

SnowZone V2 的任务编辑器是一个功能完整的可视化任务配置工具，支持多种任务类型的创建和编辑，提供直观的用户界面和强大的配置验证功能。

## 🏗️ 架构设计

### 核心组件
- **TaskEditorDialog**: 主编辑器对话框
- **ConfigManager**: 配置管理和验证
- **DragDropTaskList**: 支持拖拽的任务列表
- **MainWindowV2**: 集成的主窗口界面

### 设计模式
- **标签页模式**: 分类组织不同配置选项
- **表单/JSON双模式**: 支持可视化表单和JSON直接编辑
- **模板系统**: 预定义配置模板快速创建
- **验证驱动**: 实时配置验证和错误提示

## 🎨 用户界面

### 多标签页设计
1. **基本信息**: 任务ID、名称、描述、类型、分类
2. **参数配置**: 任务特定参数的表单或JSON编辑
3. **触发器**: 任务触发条件配置
4. **验证**: 执行结果验证设置

### 双编辑模式
- **表单模式**: 直观的图形界面，适合新手用户
- **JSON模式**: 高级用户的直接配置编辑

## 🔧 支持的任务类型

### 1. 点击任务 (Click Task)
```json
{
  "type": "click",
  "parameters": {
    "position": {
      "type": "absolute|relative|auto",
      "x": 100, "y": 200,
      "x_percent": 0.5, "y_percent": 0.5,
      "target_text": "确定"
    },
    "click_type": "left|right|middle",
    "double_click": false,
    "delay_before": 0.5,
    "delay_after": 0.5
  }
}
```

**界面特性**:
- ✅ 三种位置定位方式：绝对坐标、相对百分比、文字自动定位
- ✅ 点击类型选择：左键、右键、中键
- ✅ 双击选项
- ✅ 执行前后延迟设置

### 2. 等待任务 (Wait Task)
```json
{
  "type": "wait",
  "parameters": {
    "wait_type": "time|text|image",
    "duration": 2.0,
    "target_text": "加载完成",
    "target_image": "loading_complete.png",
    "max_wait_time": 30.0
  }
}
```

**界面特性**:
- ✅ 三种等待类型：时间等待、文字等待、图像等待
- ✅ 动态界面切换
- ✅ 最大等待时间限制

### 3. OCR任务 (OCR Task)
```json
{
  "type": "ocr",
  "parameters": {
    "region": {
      "x": 0, "y": 0,
      "width": 100, "height": 50
    },
    "target_text": "确定",
    "confidence": 0.8
  }
}
```

**界面特性**:
- ✅ 识别区域坐标设置
- ✅ 目标文字配置
- ✅ 置信度调节

### 4. 滑动任务 (Swipe Task)
```json
{
  "type": "swipe",
  "parameters": {
    "start_position": {"x": 100, "y": 200},
    "end_position": {"x": 300, "y": 200},
    "duration": 1.0
  }
}
```

**界面特性**:
- ✅ 起始和结束位置设置
- ✅ 滑动时间控制

### 5. 按键任务 (Key Task)
```json
{
  "type": "key",
  "parameters": {
    "key_type": "single|combination|text",
    "key_value": "Enter",
    "duration": 0.1
  }
}
```

**界面特性**:
- ✅ 按键类型选择
- ✅ 按键值配置
- ✅ 按键持续时间

## ⚙️ 高级功能

### 配置验证系统
- ✅ **实时验证**: 输入时即时检查配置正确性
- ✅ **类型验证**: 针对不同任务类型的专门验证规则
- ✅ **错误提示**: 详细的错误信息和修复建议
- ✅ **JSON格式验证**: JSON语法和结构验证

### 模板系统
- ✅ **预定义模板**: 常用任务类型的标准模板
- ✅ **快速创建**: 从模板一键创建任务
- ✅ **模板加载**: 在编辑器中直接加载模板
- ✅ **自定义模板**: 支持用户自定义模板

### 触发器配置
- ✅ **条件触发**: 基于条件的任务触发
- ✅ **事件触发**: 响应特定事件
- ✅ **时间触发**: 定时执行设置

### 验证配置
- ✅ **期望结果**: 设置任务执行的期望结果
- ✅ **验证超时**: 结果验证的超时设置
- ✅ **自定义验证**: 用户自定义验证逻辑

## 🔗 集成功能

### 主窗口集成
- ✅ **新建任务**: 点击按钮打开任务编辑器
- ✅ **编辑任务**: 双击任务列表项进行编辑
- ✅ **配置保存**: 自动保存到分类目录
- ✅ **列表刷新**: 编辑后自动更新任务列表

### 拖拽支持
- ✅ **拖拽创建**: 从任务库拖拽到编辑器
- ✅ **拖拽编辑**: 拖拽现有任务进行编辑
- ✅ **可视化反馈**: 拖拽过程的视觉提示

### 配置管理
- ✅ **分类存储**: 按分类自动组织配置文件
- ✅ **版本管理**: 配置文件的版本控制
- ✅ **导入导出**: 配置的导入导出功能
- ✅ **批量操作**: 多个配置的批量处理

## 🎯 使用流程

### 创建新任务
1. 点击主窗口的"新建任务"按钮
2. 在基本信息标签页填写任务信息
3. 选择任务类型，界面自动切换到对应表单
4. 配置任务参数
5. 可选：配置触发器和验证规则
6. 点击"确定"保存任务

### 编辑现有任务
1. 在任务列表中双击要编辑的任务
2. 编辑器自动加载现有配置
3. 修改需要的参数
4. 点击"确定"保存更改

### 使用模板
1. 在参数配置标签页点击"从模板加载"
2. 选择合适的模板
3. 模板参数自动填充到表单
4. 根据需要调整参数

### 验证配置
1. 在验证标签页点击"验证配置"
2. 查看验证结果和错误信息
3. 根据提示修复配置问题

## 🚀 技术特性

### 响应式设计
- ✅ **动态界面**: 根据任务类型动态调整界面
- ✅ **自适应布局**: 窗口大小自适应
- ✅ **实时更新**: 配置更改实时反映

### 错误处理
- ✅ **异常捕获**: 完整的异常处理机制
- ✅ **用户友好**: 清晰的错误提示信息
- ✅ **恢复机制**: 配置错误的自动恢复

### 性能优化
- ✅ **懒加载**: 按需加载界面组件
- ✅ **缓存机制**: 配置和模板缓存
- ✅ **异步处理**: 非阻塞的配置验证

## 📁 文件结构

```
src/gui/components/
├── task_editor.py              # 主任务编辑器
├── flow_editor.py              # 任务流编辑器
├── workflow_editor.py          # 工作流编辑器
└── drag_drop.py               # 拖拽功能支持

task/
├── config_manager.py          # 配置管理器
├── templates/                 # 配置模板目录
│   ├── click_task_template.json
│   ├── wait_task_template.json
│   ├── ocr_task_template.json
│   ├── swipe_task_template.json
│   └── key_task_template.json
└── json/                      # 用户配置存储
    ├── basic/                 # 基础任务
    ├── daily/                 # 日常任务
    ├── battle/                # 战斗任务
    └── system/                # 系统任务
```

## 🎉 完成状态

### ✅ 已完成功能
- [x] 多类型任务编辑器界面
- [x] 表单模式和JSON模式切换
- [x] 完整的配置验证系统
- [x] 模板系统和快速创建
- [x] 触发器和验证配置
- [x] 主窗口完全集成
- [x] 拖拽操作支持
- [x] 配置文件管理
- [x] 错误处理和用户提示

### 🚀 立即可用
任务编辑器现在已经完全集成到SnowZone V2中，用户可以：

1. **启动GUI V2**: `python gui_main_v2.py`
2. **创建任务**: 点击"新建任务"按钮
3. **编辑任务**: 双击任务列表中的任务
4. **拖拽操作**: 从左侧面板拖拽任务到编辑器
5. **使用模板**: 快速创建标准任务

**SnowZone V2 任务编辑器 - 让任务配置变得简单而强大！** 🎯
