# -*- coding: utf-8 -*-
"""
数据持久化模块
提供任务流和工作流的保存、加载、版本管理功能
"""

import json
import os
import shutil
import uuid
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, asdict
import sqlite3
import threading

from .task_flow import TaskFlow, FlowStep, ExecutionMode
from .workflow import Workflow, WorkflowStep, ScheduleConfig, ScheduleType
from .base_task import TaskConfig


@dataclass
class VersionInfo:
    """版本信息"""
    version_id: str
    version_number: int
    created_time: datetime
    created_by: str
    description: str
    file_path: str


@dataclass
class BackupInfo:
    """备份信息"""
    backup_id: str
    backup_time: datetime
    backup_type: str  # manual, auto, scheduled
    file_count: int
    backup_size: int
    backup_path: str


class PersistenceManager:
    """持久化管理器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化持久化管理器
        
        Args:
            data_dir: 数据目录
        """
        self.data_dir = Path(data_dir)
        self.flows_dir = self.data_dir / "flows"
        self.workflows_dir = self.data_dir / "workflows"
        self.versions_dir = self.data_dir / "versions"
        self.backups_dir = self.data_dir / "backups"
        self.db_path = self.data_dir / "persistence.db"
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 确保目录存在
        self._ensure_directories()
        
        # 初始化数据库
        self._init_database()
    
    def _ensure_directories(self):
        """确保必要目录存在"""
        directories = [
            self.data_dir,
            self.flows_dir,
            self.workflows_dir,
            self.versions_dir,
            self.backups_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _init_database(self):
        """初始化数据库"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                # 创建版本表
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS versions (
                        version_id TEXT PRIMARY KEY,
                        entity_type TEXT NOT NULL,
                        entity_id TEXT NOT NULL,
                        version_number INTEGER NOT NULL,
                        created_time TEXT NOT NULL,
                        created_by TEXT NOT NULL,
                        description TEXT,
                        file_path TEXT NOT NULL,
                        UNIQUE(entity_type, entity_id, version_number)
                    )
                ''')
                
                # 创建备份表
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS backups (
                        backup_id TEXT PRIMARY KEY,
                        backup_time TEXT NOT NULL,
                        backup_type TEXT NOT NULL,
                        file_count INTEGER NOT NULL,
                        backup_size INTEGER NOT NULL,
                        backup_path TEXT NOT NULL
                    )
                ''')
                
                # 创建执行历史表
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS execution_history (
                        execution_id TEXT PRIMARY KEY,
                        entity_type TEXT NOT NULL,
                        entity_id TEXT NOT NULL,
                        start_time TEXT NOT NULL,
                        end_time TEXT,
                        status TEXT NOT NULL,
                        result_data TEXT,
                        error_info TEXT
                    )
                ''')
                
                conn.commit()
            finally:
                conn.close()
    
    def save_task_flow(self, task_flow: TaskFlow, version_description: str = "", 
                      created_by: str = "system") -> tuple[bool, str]:
        """
        保存任务流
        
        Args:
            task_flow: 任务流对象
            version_description: 版本描述
            created_by: 创建者
            
        Returns:
            (是否成功, 文件路径或错误信息)
        """
        try:
            with self._lock:
                # 导出任务流配置
                flow_config = self._export_task_flow(task_flow)
                
                # 生成文件路径
                file_name = f"{task_flow.flow_id}.json"
                file_path = self.flows_dir / file_name
                
                # 保存文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(flow_config, f, ensure_ascii=False, indent=2)
                
                # 创建版本记录
                version_info = self._create_version_record(
                    entity_type="task_flow",
                    entity_id=task_flow.flow_id,
                    file_path=str(file_path),
                    description=version_description,
                    created_by=created_by
                )
                
                return True, str(file_path)
                
        except Exception as e:
            return False, f"保存任务流失败: {str(e)}"
    
    def load_task_flow(self, flow_id: str, version: Optional[int] = None) -> Optional[TaskFlow]:
        """
        加载任务流
        
        Args:
            flow_id: 任务流ID
            version: 版本号（可选，默认最新版本）
            
        Returns:
            任务流对象
        """
        try:
            with self._lock:
                # 获取文件路径
                if version is not None:
                    file_path = self._get_version_file_path("task_flow", flow_id, version)
                else:
                    file_path = self.flows_dir / f"{flow_id}.json"
                
                if not file_path or not Path(file_path).exists():
                    return None
                
                # 加载配置
                with open(file_path, 'r', encoding='utf-8') as f:
                    flow_config = json.load(f)
                
                # 重建任务流对象
                return self._import_task_flow(flow_config)
                
        except Exception as e:
            print(f"加载任务流失败: {e}")
            return None
    
    def save_workflow(self, workflow: Workflow, version_description: str = "",
                     created_by: str = "system") -> tuple[bool, str]:
        """
        保存工作流
        
        Args:
            workflow: 工作流对象
            version_description: 版本描述
            created_by: 创建者
            
        Returns:
            (是否成功, 文件路径或错误信息)
        """
        try:
            with self._lock:
                # 导出工作流配置
                workflow_config = workflow.export_config()
                
                # 生成文件路径
                file_name = f"{workflow.workflow_id}.json"
                file_path = self.workflows_dir / file_name
                
                # 保存文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(workflow_config, f, ensure_ascii=False, indent=2)
                
                # 创建版本记录
                version_info = self._create_version_record(
                    entity_type="workflow",
                    entity_id=workflow.workflow_id,
                    file_path=str(file_path),
                    description=version_description,
                    created_by=created_by
                )
                
                return True, str(file_path)
                
        except Exception as e:
            return False, f"保存工作流失败: {str(e)}"
    
    def load_workflow(self, workflow_id: str, version: Optional[int] = None) -> Optional[Workflow]:
        """
        加载工作流
        
        Args:
            workflow_id: 工作流ID
            version: 版本号（可选，默认最新版本）
            
        Returns:
            工作流对象
        """
        try:
            with self._lock:
                # 获取文件路径
                if version is not None:
                    file_path = self._get_version_file_path("workflow", workflow_id, version)
                else:
                    file_path = self.workflows_dir / f"{workflow_id}.json"
                
                if not file_path or not Path(file_path).exists():
                    return None
                
                # 加载配置
                with open(file_path, 'r', encoding='utf-8') as f:
                    workflow_config = json.load(f)
                
                # 重建工作流对象
                return self._import_workflow(workflow_config)
                
        except Exception as e:
            print(f"加载工作流失败: {e}")
            return None
    
    def _export_task_flow(self, task_flow: TaskFlow) -> Dict[str, Any]:
        """导出任务流配置"""
        config = {
            'flow_id': task_flow.flow_id,
            'name': task_flow.name,
            'description': task_flow.description,
            'execution_mode': task_flow.execution_mode.value,
            'continue_on_error': task_flow.continue_on_error,
            'max_parallel_tasks': task_flow.max_parallel_tasks,
            'steps': [],
            'metadata': {
                'created_time': datetime.now().isoformat(),
                'version': "0_0002"
            }
        }
        
        # 导出步骤
        for step in task_flow.steps:
            step_config = {
                'id': step.id,
                'task_id': step.task.task_id,
                'task_name': step.task.task_name,
                'order': step.order,
                'enabled': step.enabled,
                'delay_before': step.delay_before,
                'delay_after': step.delay_after,
                'continue_on_error': step.continue_on_error,
                'parameters': step.parameters
            }
            config['steps'].append(step_config)
        
        return config
    
    def _import_task_flow(self, config: Dict[str, Any]) -> TaskFlow:
        """从配置导入任务流"""
        # 创建任务流
        task_flow = TaskFlow(
            flow_id=config['flow_id'],
            name=config['name'],
            description=config.get('description', '')
        )
        
        # 设置执行模式
        execution_mode_str = config.get('execution_mode', 'sequential')
        if execution_mode_str == 'parallel':
            task_flow.execution_mode = ExecutionMode.PARALLEL
        elif execution_mode_str == 'conditional':
            task_flow.execution_mode = ExecutionMode.CONDITIONAL
        else:
            task_flow.execution_mode = ExecutionMode.SEQUENTIAL
        
        task_flow.continue_on_error = config.get('continue_on_error', True)
        task_flow.max_parallel_tasks = config.get('max_parallel_tasks', 5)
        
        # 注意：这里需要重新加载任务对象
        # 实际实现中需要与TaskManager集成
        
        return task_flow
    
    def _import_workflow(self, config: Dict[str, Any]) -> Workflow:
        """从配置导入工作流"""
        # 创建工作流
        workflow = Workflow(
            workflow_id=config['workflow_id'],
            name=config['name'],
            description=config.get('description', '')
        )
        
        workflow.continue_on_error = config.get('continue_on_error', True)
        
        # 设置调度配置
        schedule_config = config.get('schedule')
        if schedule_config:
            schedule_type = ScheduleType.MANUAL
            if schedule_config['schedule_type'] == 'once':
                schedule_type = ScheduleType.ONCE
            elif schedule_config['schedule_type'] == 'interval':
                schedule_type = ScheduleType.INTERVAL
            elif schedule_config['schedule_type'] == 'cron':
                schedule_type = ScheduleType.CRON
            
            workflow.schedule_config = ScheduleConfig(
                schedule_type=schedule_type,
                enabled=schedule_config.get('enabled', False),
                start_time=datetime.fromisoformat(schedule_config['start_time']) if schedule_config.get('start_time') else None,
                end_time=datetime.fromisoformat(schedule_config['end_time']) if schedule_config.get('end_time') else None,
                interval_seconds=schedule_config.get('interval_seconds'),
                cron_expression=schedule_config.get('cron_expression'),
                max_executions=schedule_config.get('max_executions')
            )
        
        # 注意：这里需要重新加载任务流对象
        # 实际实现中需要与TaskManager集成
        
        return workflow
    
    def _create_version_record(self, entity_type: str, entity_id: str, file_path: str,
                              description: str, created_by: str) -> VersionInfo:
        """创建版本记录"""
        version_id = str(uuid.uuid4())
        created_time = datetime.now()
        
        # 获取下一个版本号
        version_number = self._get_next_version_number(entity_type, entity_id)
        
        # 保存到数据库
        conn = sqlite3.connect(self.db_path)
        try:
            conn.execute('''
                INSERT INTO versions (version_id, entity_type, entity_id, version_number,
                                    created_time, created_by, description, file_path)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (version_id, entity_type, entity_id, version_number,
                  created_time.isoformat(), created_by, description, file_path))
            conn.commit()
        finally:
            conn.close()
        
        # 创建版本文件副本
        version_file_path = self.versions_dir / f"{entity_type}_{entity_id}_v{version_number}.json"
        shutil.copy2(file_path, version_file_path)
        
        return VersionInfo(
            version_id=version_id,
            version_number=version_number,
            created_time=created_time,
            created_by=created_by,
            description=description,
            file_path=str(version_file_path)
        )
    
    def _get_next_version_number(self, entity_type: str, entity_id: str) -> int:
        """获取下一个版本号"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.execute('''
                SELECT MAX(version_number) FROM versions 
                WHERE entity_type = ? AND entity_id = ?
            ''', (entity_type, entity_id))
            
            result = cursor.fetchone()
            max_version = result[0] if result[0] is not None else 0
            return max_version + 1
        finally:
            conn.close()
    
    def _get_version_file_path(self, entity_type: str, entity_id: str, version: int) -> Optional[str]:
        """获取指定版本的文件路径"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.execute('''
                SELECT file_path FROM versions 
                WHERE entity_type = ? AND entity_id = ? AND version_number = ?
            ''', (entity_type, entity_id, version))
            
            result = cursor.fetchone()
            return result[0] if result else None
        finally:
            conn.close()
    
    def get_version_history(self, entity_type: str, entity_id: str) -> List[VersionInfo]:
        """获取版本历史"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.execute('''
                SELECT version_id, version_number, created_time, created_by, description, file_path
                FROM versions 
                WHERE entity_type = ? AND entity_id = ?
                ORDER BY version_number DESC
            ''', (entity_type, entity_id))
            
            versions = []
            for row in cursor.fetchall():
                version_info = VersionInfo(
                    version_id=row[0],
                    version_number=row[1],
                    created_time=datetime.fromisoformat(row[2]),
                    created_by=row[3],
                    description=row[4],
                    file_path=row[5]
                )
                versions.append(version_info)
            
            return versions
        finally:
            conn.close()
    
    def create_backup(self, backup_type: str = "manual") -> tuple[bool, str]:
        """
        创建备份
        
        Args:
            backup_type: 备份类型
            
        Returns:
            (是否成功, 备份路径或错误信息)
        """
        try:
            backup_id = str(uuid.uuid4())
            backup_time = datetime.now()
            backup_dir = self.backups_dir / f"backup_{backup_time.strftime('%Y%m%d_%H%M%S')}_{backup_id[:8]}"
            
            # 创建备份目录
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 复制数据文件
            file_count = 0
            backup_size = 0
            
            for source_dir in [self.flows_dir, self.workflows_dir]:
                if source_dir.exists():
                    target_dir = backup_dir / source_dir.name
                    shutil.copytree(source_dir, target_dir)
                    
                    # 统计文件
                    for file_path in target_dir.rglob('*'):
                        if file_path.is_file():
                            file_count += 1
                            backup_size += file_path.stat().st_size
            
            # 复制数据库
            if self.db_path.exists():
                shutil.copy2(self.db_path, backup_dir / "persistence.db")
                file_count += 1
                backup_size += self.db_path.stat().st_size
            
            # 记录备份信息
            conn = sqlite3.connect(self.db_path)
            try:
                conn.execute('''
                    INSERT INTO backups (backup_id, backup_time, backup_type, 
                                       file_count, backup_size, backup_path)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (backup_id, backup_time.isoformat(), backup_type,
                      file_count, backup_size, str(backup_dir)))
                conn.commit()
            finally:
                conn.close()
            
            return True, str(backup_dir)
            
        except Exception as e:
            return False, f"创建备份失败: {str(e)}"
    
    def restore_from_backup(self, backup_path: str) -> tuple[bool, str]:
        """
        从备份恢复
        
        Args:
            backup_path: 备份路径
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            backup_dir = Path(backup_path)
            if not backup_dir.exists():
                return False, "备份路径不存在"
            
            # 创建当前数据的备份
            current_backup_success, current_backup_path = self.create_backup("auto_before_restore")
            if not current_backup_success:
                return False, f"创建当前数据备份失败: {current_backup_path}"
            
            # 恢复数据文件
            for backup_subdir in backup_dir.iterdir():
                if backup_subdir.is_dir() and backup_subdir.name in ['flows', 'workflows']:
                    target_dir = self.data_dir / backup_subdir.name
                    
                    # 删除现有目录
                    if target_dir.exists():
                        shutil.rmtree(target_dir)
                    
                    # 复制备份数据
                    shutil.copytree(backup_subdir, target_dir)
            
            # 恢复数据库
            backup_db = backup_dir / "persistence.db"
            if backup_db.exists():
                shutil.copy2(backup_db, self.db_path)
            
            return True, ""
            
        except Exception as e:
            return False, f"从备份恢复失败: {str(e)}"
    
    def list_backups(self) -> List[BackupInfo]:
        """列出所有备份"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.execute('''
                SELECT backup_id, backup_time, backup_type, file_count, backup_size, backup_path
                FROM backups 
                ORDER BY backup_time DESC
            ''')
            
            backups = []
            for row in cursor.fetchall():
                backup_info = BackupInfo(
                    backup_id=row[0],
                    backup_time=datetime.fromisoformat(row[1]),
                    backup_type=row[2],
                    file_count=row[3],
                    backup_size=row[4],
                    backup_path=row[5]
                )
                backups.append(backup_info)
            
            return backups
        finally:
            conn.close()
    
    def cleanup_old_versions(self, keep_versions: int = 10):
        """清理旧版本"""
        # 实现版本清理逻辑
        pass
    
    def cleanup_old_backups(self, keep_backups: int = 5):
        """清理旧备份"""
        # 实现备份清理逻辑
        pass
