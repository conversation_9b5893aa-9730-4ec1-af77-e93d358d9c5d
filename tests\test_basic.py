# -*- coding: utf-8 -*-
"""
基础系统测试脚本
测试不依赖外部库的核心功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """测试基础模块导入"""
    print("测试基础模块导入...")
    
    try:
        from src.utils.logger import get_logger
        from src.utils.version_manager import VersionManager
        from src.utils.exception_handler import ExceptionHandler
        print("✓ 工具模块导入成功")
    except Exception as e:
        print(f"✗ 工具模块导入失败: {e}")
        return False
    
    try:
        from src.config.config_manager import ConfigManager
        from src.config.task_loader import TaskLoader
        print("✓ 配置模块导入成功")
    except Exception as e:
        print(f"✗ 配置模块导入失败: {e}")
        return False
    
    return True


def test_version_manager():
    """测试版本管理器"""
    print("\n测试版本管理器...")
    
    try:
        from src.utils.version_manager import VersionManager
        
        vm = VersionManager()
        current_version = vm.get_current_version()
        print(f"✓ 当前版本: {current_version}")
        
        history = vm.get_version_history()
        print(f"✓ 版本历史记录: {len(history)} 条")
        
        # 测试版本信息显示
        print("版本信息:")
        for record in history:
            print(f"  - {record['version']}: {record['description']}")
        
        return True
    except Exception as e:
        print(f"✗ 版本管理器测试失败: {e}")
        return False


def test_logger():
    """测试日志系统"""
    print("\n测试日志系统...")
    
    try:
        from src.utils.logger import get_logger
        
        logger = get_logger()
        logger.info("日志系统测试消息")
        logger.debug("调试消息")
        logger.warning("警告消息")
        
        print("✓ 日志系统工作正常")
        return True
    except Exception as e:
        print(f"✗ 日志系统测试失败: {e}")
        return False


def test_config_system():
    """测试配置系统"""
    print("\n测试配置系统...")
    
    try:
        from src.config.config_manager import ConfigManager
        from src.config.task_loader import TaskLoader
        
        # 测试配置管理器
        config_manager = ConfigManager()
        
        main_config = config_manager.get_config("main")
        if main_config:
            print(f"✓ 主配置加载成功，版本: {main_config.get('version')}")
        else:
            print("⚠ 主配置未找到")
        
        task_configs = config_manager.get_task_configs()
        print(f"✓ 任务配置: {len(task_configs)} 个")
        
        # 测试任务加载器
        task_loader = TaskLoader(config_manager)
        summary = task_loader.get_task_summary()
        print(f"✓ 任务摘要: 总计 {summary['total_tasks']} 个任务")
        print(f"  - 启用: {summary['enabled_tasks']} 个")
        print(f"  - 禁用: {summary['disabled_tasks']} 个")
        
        # 列出任务详情
        for task in summary['task_list']:
            status = "启用" if task['enabled'] else "禁用"
            print(f"  - {task['name']} ({task['id']}) [{status}] 优先级:{task['priority']}")
        
        config_manager.stop()
        return True
    except Exception as e:
        print(f"✗ 配置系统测试失败: {e}")
        return False


def test_exception_handler():
    """测试异常处理器"""
    print("\n测试异常处理器...")
    
    try:
        from src.utils.exception_handler import (
            ExceptionHandler, OCRException, WindowCaptureException,
            ActionExecutionException, ConfigurationException
        )
        
        handler = ExceptionHandler()
        
        # 测试不同类型的异常
        test_exceptions = [
            OCRException("测试OCR异常", {"confidence": 0.3}),
            WindowCaptureException("测试窗口捕获异常"),
            ActionExecutionException("测试动作执行异常"),
            ConfigurationException("测试配置异常")
        ]
        
        for exc in test_exceptions:
            try:
                raise exc
            except Exception as e:
                handler.handle_exception(e)
        
        stats = handler.get_error_stats()
        print(f"✓ 异常处理器: 处理了 {stats['total_errors']} 个错误")
        print(f"  - 按类型统计: {stats['errors_by_type']}")
        print(f"  - 按级别统计: {stats['errors_by_level']}")
        
        return True
    except Exception as e:
        print(f"✗ 异常处理器测试失败: {e}")
        return False


def test_main_program():
    """测试主程序"""
    print("\n测试主程序...")
    
    try:
        # 测试命令行参数解析
        import main
        
        # 模拟命令行参数
        import sys
        original_argv = sys.argv.copy()
        
        # 测试版本显示
        sys.argv = ['main.py', '--version']
        try:
            main.parse_arguments()
            print("✓ 版本参数解析成功")
        except SystemExit:
            pass  # argparse会调用sys.exit
        
        # 测试任务列表
        sys.argv = ['main.py', '--list-tasks']
        try:
            args = main.parse_arguments()
            if main.handle_task_management(args):
                print("✓ 任务管理功能正常")
        except Exception as e:
            print(f"⚠ 任务管理测试异常: {e}")
        
        # 恢复原始参数
        sys.argv = original_argv
        
        return True
    except Exception as e:
        print(f"✗ 主程序测试失败: {e}")
        return False


def test_project_structure():
    """测试项目结构"""
    print("\n测试项目结构...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "README.md",
        "项目结构与实现流程.md",
        "config/default_config.json",
        "config/tasks/daily_task.json",
        "src/__init__.py",
        "src/core/__init__.py",
        "src/config/__init__.py",
        "src/utils/__init__.py",
        "logs/0_0001.log"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ 缺少文件: {missing_files}")
        return False
    else:
        print(f"✓ 项目结构完整，检查了 {len(required_files)} 个文件")
        return True


def run_basic_tests():
    """运行基础测试"""
    print("SnowZone OCR自动化工具 - 基础系统测试")
    print("=" * 50)
    
    tests = [
        ("项目结构", test_project_structure),
        ("基础模块导入", test_basic_imports),
        ("版本管理器", test_version_manager),
        ("日志系统", test_logger),
        ("配置系统", test_config_system),
        ("异常处理器", test_exception_handler),
        ("主程序", test_main_program)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"基础测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础测试通过！核心系统运行正常")
        print("\n📋 项目状态:")
        print("  ✓ 项目架构完整")
        print("  ✓ 核心模块可用")
        print("  ✓ 配置系统正常")
        print("  ✓ 日志系统工作")
        print("  ✓ 异常处理完善")
        print("  ✓ 版本管理就绪")
        
        print("\n🚀 下一步建议:")
        print("  1. 安装完整依赖: pip install -r requirements.txt")
        print("  2. 安装Tesseract OCR用于文字识别")
        print("  3. 运行完整测试: python test_system.py")
        print("  4. 启动主程序: python main.py")
        
        return True
    else:
        print("⚠ 部分基础测试失败，请检查相关组件")
        return False


if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)
