# -*- coding: utf-8 -*-
"""
OCR识别引擎
基于Tesseract的文字识别功能
"""

import os
import re
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("警告: pytesseract模块未安装，OCR功能将不可用")


@dataclass
class OCRResult:
    """OCR识别结果数据类"""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x, y, width, height)
    words: List[Dict[str, Any]]


class OCREngine:
    """OCR识别引擎类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化OCR引擎
        
        Args:
            config: OCR配置参数
        """
        if not TESSERACT_AVAILABLE:
            raise ImportError("需要安装pytesseract包: pip install pytesseract")
        
        self.config = config or self._get_default_config()
        self._setup_tesseract()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "language": "chi_sim",
            "confidence_threshold": 0.7,
            "psm": 6,  # Page Segmentation Mode
            "oem": 3,  # OCR Engine Mode
            "whitelist": "",
            "blacklist": "",
            "tesseract_path": None  # 如果需要指定Tesseract路径
        }
    
    def _setup_tesseract(self):
        """设置Tesseract配置"""
        # 如果指定了Tesseract路径
        tesseract_path = self.config.get("tesseract_path")
        if tesseract_path and os.path.exists(tesseract_path):
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # 测试Tesseract是否可用
        try:
            pytesseract.get_tesseract_version()
            print(f"Tesseract版本: {pytesseract.get_tesseract_version()}")
        except Exception as e:
            print(f"Tesseract初始化失败: {e}")
            raise
    
    def _build_tesseract_config(self, custom_config: Optional[Dict] = None) -> str:
        """
        构建Tesseract配置字符串
        
        Args:
            custom_config: 自定义配置
            
        Returns:
            配置字符串
        """
        config = {**self.config, **(custom_config or {})}
        
        config_parts = []
        
        # Page Segmentation Mode
        psm = config.get("psm", 6)
        config_parts.append(f"--psm {psm}")
        
        # OCR Engine Mode
        oem = config.get("oem", 3)
        config_parts.append(f"--oem {oem}")
        
        # 字符白名单
        whitelist = config.get("whitelist", "")
        if whitelist:
            config_parts.append(f"-c tessedit_char_whitelist={whitelist}")
        
        # 字符黑名单
        blacklist = config.get("blacklist", "")
        if blacklist:
            config_parts.append(f"-c tessedit_char_blacklist={blacklist}")
        
        return " ".join(config_parts)
    
    def recognize_text(self, image: np.ndarray, language: Optional[str] = None, 
                      custom_config: Optional[Dict] = None) -> OCRResult:
        """
        识别图像中的文字
        
        Args:
            image: 输入图像
            language: 识别语言
            custom_config: 自定义配置
            
        Returns:
            OCR识别结果
        """
        if image is None:
            raise ValueError("输入图像不能为空")
        
        # 使用指定语言或默认语言
        lang = language or self.config.get("language", "chi_sim")
        
        # 构建配置
        tesseract_config = self._build_tesseract_config(custom_config)
        
        try:
            # 获取详细识别结果
            data = pytesseract.image_to_data(image, lang=lang, config=tesseract_config, 
                                           output_type=pytesseract.Output.DICT)
            
            # 提取文字和置信度
            text_parts = []
            words = []
            total_confidence = 0
            valid_words = 0
            
            for i in range(len(data['text'])):
                word = data['text'][i].strip()
                confidence = float(data['conf'][i])
                
                if word and confidence > 0:
                    text_parts.append(word)
                    words.append({
                        'text': word,
                        'confidence': confidence,
                        'bbox': (data['left'][i], data['top'][i], 
                                data['width'][i], data['height'][i])
                    })
                    total_confidence += confidence
                    valid_words += 1
            
            # 计算整体置信度
            overall_confidence = total_confidence / valid_words if valid_words > 0 else 0
            
            # 合并文字
            full_text = ' '.join(text_parts)
            
            # 计算整体边界框
            if words:
                min_x = min(word['bbox'][0] for word in words)
                min_y = min(word['bbox'][1] for word in words)
                max_x = max(word['bbox'][0] + word['bbox'][2] for word in words)
                max_y = max(word['bbox'][1] + word['bbox'][3] for word in words)
                bbox = (min_x, min_y, max_x - min_x, max_y - min_y)
            else:
                bbox = (0, 0, 0, 0)
            
            return OCRResult(
                text=full_text,
                confidence=overall_confidence / 100.0,  # 转换为0-1范围
                bbox=bbox,
                words=words
            )
            
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return OCRResult(text="", confidence=0.0, bbox=(0, 0, 0, 0), words=[])
    
    def find_text_position(self, image: np.ndarray, target_text: str, 
                          similarity_threshold: float = 0.8) -> List[Tuple[int, int, int, int]]:
        """
        查找指定文字在图像中的位置
        
        Args:
            image: 输入图像
            target_text: 目标文字
            similarity_threshold: 相似度阈值
            
        Returns:
            匹配位置列表 [(x, y, width, height), ...]
        """
        result = self.recognize_text(image)
        positions = []
        
        # 清理目标文字
        target_clean = self._clean_text(target_text)
        
        for word in result.words:
            word_clean = self._clean_text(word['text'])
            
            # 计算文字相似度
            similarity = self._calculate_similarity(target_clean, word_clean)
            
            if similarity >= similarity_threshold:
                positions.append(word['bbox'])
        
        return positions
    
    def _clean_text(self, text: str) -> str:
        """
        清理文字，去除空格和特殊字符
        
        Args:
            text: 原始文字
            
        Returns:
            清理后的文字
        """
        # 去除空格和换行符
        cleaned = re.sub(r'\s+', '', text)
        # 去除标点符号
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', cleaned)
        return cleaned.lower()
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文字的相似度
        
        Args:
            text1: 文字1
            text2: 文字2
            
        Returns:
            相似度 (0-1)
        """
        if not text1 or not text2:
            return 0.0
        
        # 简单的包含关系检查
        if text1 in text2 or text2 in text1:
            return 1.0
        
        # 计算编辑距离相似度
        return self._levenshtein_similarity(text1, text2)
    
    def _levenshtein_similarity(self, s1: str, s2: str) -> float:
        """
        计算编辑距离相似度
        
        Args:
            s1: 字符串1
            s2: 字符串2
            
        Returns:
            相似度 (0-1)
        """
        if len(s1) == 0:
            return 0.0 if len(s2) > 0 else 1.0
        if len(s2) == 0:
            return 0.0
        
        # 动态规划计算编辑距离
        matrix = [[0] * (len(s2) + 1) for _ in range(len(s1) + 1)]
        
        for i in range(len(s1) + 1):
            matrix[i][0] = i
        for j in range(len(s2) + 1):
            matrix[0][j] = j
        
        for i in range(1, len(s1) + 1):
            for j in range(1, len(s2) + 1):
                if s1[i-1] == s2[j-1]:
                    cost = 0
                else:
                    cost = 1
                
                matrix[i][j] = min(
                    matrix[i-1][j] + 1,      # 删除
                    matrix[i][j-1] + 1,      # 插入
                    matrix[i-1][j-1] + cost  # 替换
                )
        
        distance = matrix[len(s1)][len(s2)]
        max_len = max(len(s1), len(s2))
        
        return 1.0 - (distance / max_len)
    
    def batch_recognize(self, images: List[np.ndarray]) -> List[OCRResult]:
        """
        批量识别多个图像
        
        Args:
            images: 图像列表
            
        Returns:
            识别结果列表
        """
        results = []
        for image in images:
            result = self.recognize_text(image)
            results.append(result)
        return results
    
    def is_text_present(self, image: np.ndarray, target_text: str, 
                       confidence_threshold: Optional[float] = None) -> bool:
        """
        检查图像中是否存在指定文字
        
        Args:
            image: 输入图像
            target_text: 目标文字
            confidence_threshold: 置信度阈值
            
        Returns:
            是否存在目标文字
        """
        threshold = confidence_threshold or self.config.get("confidence_threshold", 0.7)
        
        result = self.recognize_text(image)
        
        if result.confidence < threshold:
            return False
        
        # 检查是否包含目标文字
        target_clean = self._clean_text(target_text)
        text_clean = self._clean_text(result.text)
        
        return target_clean in text_clean
    
    def get_supported_languages(self) -> List[str]:
        """
        获取支持的语言列表
        
        Returns:
            语言代码列表
        """
        try:
            return pytesseract.get_languages()
        except Exception as e:
            print(f"获取语言列表失败: {e}")
            return ["eng", "chi_sim"]


if __name__ == "__main__":
    # 测试OCR功能
    print("测试OCR识别功能...")
    
    try:
        ocr = OCREngine()
        print("支持的语言:", ocr.get_supported_languages())
        
        # 创建测试图像（包含文字）
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "Test Text", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # 识别文字
        result = ocr.recognize_text(test_image, language="eng")
        print(f"识别结果: '{result.text}' (置信度: {result.confidence:.2f})")
        
    except Exception as e:
        print(f"OCR测试失败: {e}")
        print("请确保已安装Tesseract OCR和pytesseract包")
