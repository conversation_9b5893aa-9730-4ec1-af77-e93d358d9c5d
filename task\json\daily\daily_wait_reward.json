{"id": "daily_wait_reward_001", "name": "等待奖励出现", "description": "等待领取奖励按钮出现", "type": "wait", "class_name": "WaitTask", "enabled": true, "priority": 1, "timeout": 15.0, "retry_count": 2, "retry_delay": 1.0, "parameters": {"wait_type": "text", "target_text": "领取奖励", "max_wait_time": 10.0, "check_interval": 0.5, "region": {"x": 800, "y": 400, "width": 320, "height": 280}}, "triggers": [], "validation": {"expected_result": "text_found", "timeout": 10.0}}