# OCR自动化点击工具 - 《尘白禁区》专用
## 项目架构设计文档

### 版本信息
- **项目版本**: 0_0001
- **创建日期**: 2025-07-10
- **目标游戏**: 尘白禁区 (game.exe)

---

## 1. 项目概述

### 1.1 项目目标
开发基于光学字符识别(OCR)技术的自动化点击工具，专门针对《尘白禁区》游戏窗口进行文字识别和自动化操作。

### 1.2 核心功能
- **实时窗口监控**: 持续监控游戏窗口状态
- **OCR文字识别**: 识别游戏界面中的特定文字内容
- **自动化执行**: 根据识别结果执行预定义的操作序列
- **配置化管理**: 通过JSON文件灵活配置任务流程
- **版本控制**: 完整的版本管理和日志记录系统

---

## 2. 技术架构

### 2.1 技术栈选择
```
编程语言: Python 3.8+
OCR引擎: Tesseract-OCR + pytesseract
图像处理: OpenCV (cv2)
窗口操作: pywin32
GUI界面: tkinter (可选)
配置管理: json
日志系统: logging
```

### 2.2 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   游戏窗口监控   │───▶│   图像预处理     │───▶│   OCR文字识别   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   配置管理器     │◀───│   任务调度器     │───▶│   动作执行器     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   版本管理       │    │   日志记录       │    │   异常处理       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 3. 项目结构

### 3.1 目录结构
```
SnowZone/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明文档
├── 项目结构与实现流程.md    # 本文档
├── config/                # 配置文件目录
│   ├── default_config.json # 默认配置文件
│   ├── tasks/             # 任务配置目录
│   │   ├── daily_task.json
│   │   └── battle_task.json
│   └── ocr_settings.json  # OCR设置配置
├── src/                   # 源代码目录
│   ├── __init__.py
│   ├── core/              # 核心功能模块
│   │   ├── __init__.py
│   │   ├── window_capture.py    # 窗口捕获
│   │   ├── image_processor.py   # 图像预处理
│   │   ├── ocr_engine.py       # OCR识别引擎
│   │   └── action_executor.py  # 动作执行器
│   ├── config/            # 配置管理模块
│   │   ├── __init__.py
│   │   ├── config_manager.py   # 配置管理器
│   │   └── task_loader.py      # 任务加载器
│   ├── utils/             # 工具模块
│   │   ├── __init__.py
│   │   ├── logger.py           # 日志工具
│   │   ├── version_manager.py  # 版本管理
│   │   └── exception_handler.py # 异常处理
│   └── gui/               # 图形界面模块(可选)
│       ├── __init__.py
│       └── main_window.py
├── logs/                  # 日志文件目录
│   ├── 0_0001.log
│   ├── 0_0002.log
│   └── current.log
├── tests/                 # 测试文件目录
│   ├── __init__.py
│   ├── test_ocr.py
│   └── test_config.py
└── docs/                  # 文档目录
    ├── API文档.md
    └── 使用说明.md
```

---

## 4. 核心模块设计

### 4.1 窗口捕获模块 (window_capture.py)
**功能**: 定位并捕获游戏窗口画面
```python
class WindowCapture:
    def find_game_window(self, window_title="尘白禁区")
    def capture_window(self, window_handle)
    def get_window_rect(self, window_handle)
```

### 4.2 图像预处理模块 (image_processor.py)
**功能**: 对捕获的图像进行预处理以提高OCR识别率
```python
class ImageProcessor:
    def preprocess_image(self, image)
    def enhance_contrast(self, image)
    def remove_noise(self, image)
    def resize_image(self, image, scale_factor)
```

### 4.3 OCR识别引擎 (ocr_engine.py)
**功能**: 执行文字识别并返回结果
```python
class OCREngine:
    def initialize_tesseract(self)
    def recognize_text(self, image, language='chi_sim')
    def find_text_position(self, image, target_text)
    def get_confidence_score(self, result)
```

### 4.4 动作执行器 (action_executor.py)
**功能**: 执行鼠标点击、键盘输入等操作
```python
class ActionExecutor:
    def click_at_position(self, x, y)
    def send_key_sequence(self, keys)
    def wait_for_delay(self, seconds)
    def execute_task_sequence(self, task_list)
```

---

## 5. 配置文件格式

### 5.1 主配置文件 (default_config.json)
```json
{
    "version": "0_0001",
    "game_settings": {
        "window_title": "尘白禁区",
        "process_name": "game.exe",
        "capture_interval": 1.0
    },
    "ocr_settings": {
        "language": "chi_sim",
        "confidence_threshold": 0.7,
        "preprocessing": {
            "enhance_contrast": true,
            "remove_noise": true,
            "scale_factor": 2.0
        }
    },
    "logging": {
        "level": "INFO",
        "max_file_size": "10MB",
        "backup_count": 5
    }
}
```

### 5.2 任务配置文件示例 (daily_task.json)
```json
{
    "task_name": "每日任务自动化",
    "description": "自动完成每日任务流程",
    "triggers": [
        {
            "type": "text_recognition",
            "target_text": "每日任务",
            "confidence": 0.8,
            "region": [0, 0, 1920, 1080]
        }
    ],
    "actions": [
        {
            "type": "click",
            "position": [960, 540],
            "delay_before": 1.0,
            "delay_after": 2.0
        },
        {
            "type": "wait_for_text",
            "target_text": "领取奖励",
            "timeout": 10.0
        },
        {
            "type": "click",
            "position": "auto",
            "target_text": "领取奖励"
        }
    ]
}
```

---

## 6. 实现流程

### 6.1 主程序执行流程
```
1. 初始化系统
   ├── 加载配置文件
   ├── 初始化日志系统
   ├── 检查依赖环境
   └── 启动版本管理

2. 游戏窗口检测
   ├── 搜索目标窗口
   ├── 获取窗口句柄
   └── 验证窗口状态

3. 主循环执行
   ├── 捕获窗口画面
   ├── 图像预处理
   ├── OCR文字识别
   ├── 匹配任务触发条件
   ├── 执行对应动作序列
   └── 记录执行日志

4. 异常处理与恢复
   ├── 捕获运行时异常
   ├── 记录错误信息
   └── 尝试自动恢复
```

### 6.2 版本管理流程
```
版本号格式: 0_000x
├── 0_0001: 项目初始化和基础架构
├── 0_0002: OCR核心功能实现
├── 0_0003: 配置管理系统
├── 0_0004: 自动化执行引擎
├── 0_0005: GUI界面开发
└── 0_000x: 功能完善和优化
```

---

## 7. 开发计划

### 阶段一: 基础架构 (0_0001-0_0002)
- [x] 项目结构设计
- [ ] 基础目录创建
- [ ] 依赖环境配置
- [ ] 日志系统实现

### 阶段二: 核心功能 (0_0003-0_0005)
- [ ] 窗口捕获功能
- [ ] OCR识别引擎
- [ ] 图像预处理优化
- [ ] 基础动作执行

### 阶段三: 高级功能 (0_0006-0_0008)
- [ ] 配置管理系统
- [ ] 任务流程引擎
- [ ] 异常处理机制
- [ ] 性能优化

### 阶段四: 完善优化 (0_0009-0_0010)
- [ ] GUI界面开发
- [ ] 功能测试完善
- [ ] 文档编写
- [ ] 发布准备

---

## 8. 技术要点

### 8.1 OCR优化策略
- 图像预处理: 对比度增强、噪声去除、尺寸缩放
- 语言模型: 使用中文简体模型提高识别准确率
- 区域识别: 限定识别区域减少干扰
- 置信度过滤: 设置阈值过滤低质量结果

### 8.2 性能优化方案
- 异步处理: 使用多线程处理图像捕获和识别
- 缓存机制: 缓存重复识别结果
- 智能等待: 根据游戏状态动态调整检测频率
- 内存管理: 及时释放图像资源

### 8.3 异常处理机制
- 窗口丢失: 自动重新搜索游戏窗口
- OCR失败: 重试机制和降级策略
- 配置错误: 验证和修复机制
- 系统资源: 监控和限制资源使用

---

## 9. 安全与合规

### 9.1 使用声明
本工具仅供学习和研究目的，使用者需遵守游戏服务条款和相关法律法规。

### 9.2 技术限制
- 仅支持本地窗口操作，不涉及网络通信
- 不修改游戏文件或内存数据
- 基于图像识别，不使用游戏内部API

---

*文档版本: 0_0001*
*最后更新: 2025-07-10*
