# -*- coding: utf-8 -*-
"""
工作流编辑器组件
提供完整的工作流编辑和调度配置功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from task.workflow import Workflow, WorkflowStep, ScheduleConfig, ScheduleType
    from task.task_flow import TaskFlow
    from task.integration import get_integration
    TASK_MODULE_AVAILABLE = True
except ImportError:
    TASK_MODULE_AVAILABLE = False


class ScheduleConfigDialog:
    """调度配置对话框"""

    def __init__(self, parent, schedule_config: ScheduleConfig = None):
        """
        初始化对话框

        Args:
            parent: 父窗口
            schedule_config: 现有调度配置
        """
        self.parent = parent
        self.schedule_config = schedule_config
        self.result = None

        self._create_dialog()

    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("调度配置")
        self.dialog.geometry("500x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 基本设置
        self._create_basic_settings(main_frame)

        # 调度类型设置
        self._create_schedule_type_settings(main_frame)

        # 高级设置
        self._create_advanced_settings(main_frame)

        # 按钮
        self._create_buttons(main_frame)

        # 加载现有配置
        if self.schedule_config:
            self._load_config()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def _create_basic_settings(self, parent):
        """创建基本设置"""
        basic_frame = ttk.LabelFrame(parent, text="基本设置")
        basic_frame.pack(fill=tk.X, pady=(0, 10))

        # 启用调度
        self.enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(basic_frame, text="启用调度", variable=self.enabled_var).pack(anchor=tk.W, padx=5, pady=5)

        # 调度类型
        ttk.Label(basic_frame, text="调度类型:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        self.schedule_type_var = tk.StringVar(value="manual")
        type_frame = ttk.Frame(basic_frame)
        type_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Radiobutton(type_frame, text="手动执行", variable=self.schedule_type_var,
                       value="manual", command=self._on_type_changed).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="单次执行", variable=self.schedule_type_var,
                       value="once", command=self._on_type_changed).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="间隔执行", variable=self.schedule_type_var,
                       value="interval", command=self._on_type_changed).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="Cron表达式", variable=self.schedule_type_var,
                       value="cron", command=self._on_type_changed).pack(side=tk.LEFT)

    def _create_schedule_type_settings(self, parent):
        """创建调度类型设置"""
        self.type_frame = ttk.LabelFrame(parent, text="调度参数")
        self.type_frame.pack(fill=tk.X, pady=(0, 10))

        # 间隔执行设置
        self.interval_frame = ttk.Frame(self.type_frame)
        ttk.Label(self.interval_frame, text="执行间隔(秒):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.interval_var = tk.IntVar(value=3600)
        ttk.Spinbox(self.interval_frame, from_=1, to=86400, textvariable=self.interval_var, width=10).grid(row=0, column=1, padx=5, pady=5)

        # Cron表达式设置
        self.cron_frame = ttk.Frame(self.type_frame)
        ttk.Label(self.cron_frame, text="Cron表达式:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.cron_var = tk.StringVar(value="0 9 * * *")
        ttk.Entry(self.cron_frame, textvariable=self.cron_var, width=20).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(self.cron_frame, text="格式: 分 时 日 月 周", font=("Arial", 8)).grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5)

        # 默认显示间隔设置
        self._on_type_changed()

    def _create_advanced_settings(self, parent):
        """创建高级设置"""
        advanced_frame = ttk.LabelFrame(parent, text="高级设置")
        advanced_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 时间范围
        time_frame = ttk.Frame(advanced_frame)
        time_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(time_frame, text="开始时间:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.start_time_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        ttk.Entry(time_frame, textvariable=self.start_time_var, width=20).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(time_frame, text="结束时间:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.end_time_var = tk.StringVar()
        ttk.Entry(time_frame, textvariable=self.end_time_var, width=20).grid(row=1, column=1, padx=5, pady=2)

        # 执行限制
        limit_frame = ttk.Frame(advanced_frame)
        limit_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(limit_frame, text="最大执行次数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.max_executions_var = tk.StringVar()
        ttk.Entry(limit_frame, textvariable=self.max_executions_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        ttk.Label(limit_frame, text="(留空表示无限制)").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)

    def _create_buttons(self, parent):
        """创建按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="确定", command=self._on_ok).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="测试Cron", command=self._test_cron).pack(side=tk.LEFT)

    def _on_type_changed(self):
        """调度类型改变事件"""
        # 隐藏所有类型特定的框架
        self.interval_frame.pack_forget()
        self.cron_frame.pack_forget()

        # 显示对应的框架
        schedule_type = self.schedule_type_var.get()
        if schedule_type == "interval":
            self.interval_frame.pack(fill=tk.X, padx=5, pady=5)
        elif schedule_type == "cron":
            self.cron_frame.pack(fill=tk.X, padx=5, pady=5)

    def _load_config(self):
        """加载现有配置"""
        config = self.schedule_config

        self.enabled_var.set(config.enabled)

        # 设置调度类型
        type_map = {
            ScheduleType.MANUAL: "manual",
            ScheduleType.ONCE: "once",
            ScheduleType.INTERVAL: "interval",
            ScheduleType.CRON: "cron"
        }
        self.schedule_type_var.set(type_map.get(config.schedule_type, "manual"))

        # 设置参数
        if config.interval_seconds:
            self.interval_var.set(config.interval_seconds)

        if config.cron_expression:
            self.cron_var.set(config.cron_expression)

        if config.start_time:
            self.start_time_var.set(config.start_time.strftime("%Y-%m-%d %H:%M:%S"))

        if config.end_time:
            self.end_time_var.set(config.end_time.strftime("%Y-%m-%d %H:%M:%S"))

        if config.max_executions:
            self.max_executions_var.set(str(config.max_executions))

        self._on_type_changed()

    def _test_cron(self):
        """测试Cron表达式"""
        cron_expr = self.cron_var.get().strip()
        if not cron_expr:
            messagebox.showwarning("警告", "请输入Cron表达式")
            return

        try:
            # 简单验证Cron表达式格式
            parts = cron_expr.split()
            if len(parts) != 5:
                raise ValueError("Cron表达式应包含5个部分：分 时 日 月 周")

            messagebox.showinfo("测试结果", f"Cron表达式格式正确:\n{cron_expr}")
        except Exception as e:
            messagebox.showerror("测试失败", f"Cron表达式格式错误:\n{str(e)}")

    def _on_ok(self):
        """确定按钮事件"""
        try:
            # 解析调度类型
            type_map = {
                "manual": ScheduleType.MANUAL,
                "once": ScheduleType.ONCE,
                "interval": ScheduleType.INTERVAL,
                "cron": ScheduleType.CRON
            }
            schedule_type = type_map[self.schedule_type_var.get()]

            # 解析时间
            start_time = None
            if self.start_time_var.get().strip():
                start_time = datetime.strptime(self.start_time_var.get().strip(), "%Y-%m-%d %H:%M:%S")

            end_time = None
            if self.end_time_var.get().strip():
                end_time = datetime.strptime(self.end_time_var.get().strip(), "%Y-%m-%d %H:%M:%S")

            # 解析最大执行次数
            max_executions = None
            if self.max_executions_var.get().strip():
                max_executions = int(self.max_executions_var.get().strip())

            # 验证参数
            if schedule_type == ScheduleType.INTERVAL:
                if self.interval_var.get() <= 0:
                    messagebox.showerror("错误", "执行间隔必须大于0")
                    return
            elif schedule_type == ScheduleType.CRON:
                if not self.cron_var.get().strip():
                    messagebox.showerror("错误", "请输入Cron表达式")
                    return

            # 创建配置
            self.result = ScheduleConfig(
                schedule_type=schedule_type,
                enabled=self.enabled_var.get(),
                start_time=start_time,
                end_time=end_time,
                interval_seconds=self.interval_var.get() if schedule_type == ScheduleType.INTERVAL else None,
                cron_expression=self.cron_var.get().strip() if schedule_type == ScheduleType.CRON else None,
                max_executions=max_executions
            )

            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("错误", f"参数格式错误:\n{str(e)}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败:\n{str(e)}")

    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()

    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


class WorkflowStepConfigDialog:
    """工作流步骤配置对话框"""

    def __init__(self, parent, step: WorkflowStep = None, available_flows: List[TaskFlow] = None):
        """
        初始化对话框

        Args:
            parent: 父窗口
            step: 要编辑的步骤（None表示新建）
            available_flows: 可用任务流列表
        """
        self.parent = parent
        self.step = step
        self.available_flows = available_flows or []
        self.result = None

        self._create_dialog()

    def _create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("工作流步骤配置" if self.step else "新建工作流步骤")
        self.dialog.geometry("450x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 基本信息
        self._create_basic_info_section(main_frame)

        # 执行配置
        self._create_execution_config_section(main_frame)

        # 按钮
        self._create_buttons(main_frame)

        # 加载现有数据
        if self.step:
            self._load_step_data()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def _create_basic_info_section(self, parent):
        """创建基本信息区域"""
        basic_frame = ttk.LabelFrame(parent, text="基本信息")
        basic_frame.pack(fill=tk.X, pady=(0, 10))

        # 任务流选择
        ttk.Label(basic_frame, text="选择任务流:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.flow_var = tk.StringVar()
        self.flow_combo = ttk.Combobox(basic_frame, textvariable=self.flow_var, state="readonly")
        self.flow_combo['values'] = [f"{flow.name} ({flow.flow_id})" for flow in self.available_flows]
        self.flow_combo.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        # 执行顺序
        ttk.Label(basic_frame, text="执行顺序:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.order_var = tk.IntVar(value=1)
        self.order_spin = ttk.Spinbox(basic_frame, from_=1, to=100, textvariable=self.order_var, width=10)
        self.order_spin.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # 启用状态
        self.enabled_var = tk.BooleanVar(value=True)
        self.enabled_check = ttk.Checkbutton(basic_frame, text="启用此步骤", variable=self.enabled_var)
        self.enabled_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        basic_frame.grid_columnconfigure(1, weight=1)

    def _create_execution_config_section(self, parent):
        """创建执行配置区域"""
        exec_frame = ttk.LabelFrame(parent, text="执行配置")
        exec_frame.pack(fill=tk.X, pady=(0, 10))

        # 执行前延迟
        ttk.Label(exec_frame, text="执行前延迟(秒):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.delay_before_var = tk.DoubleVar(value=0.0)
        self.delay_before_spin = ttk.Spinbox(exec_frame, from_=0.0, to=300.0, increment=1.0,
                                           textvariable=self.delay_before_var, width=10)
        self.delay_before_spin.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 执行后延迟
        ttk.Label(exec_frame, text="执行后延迟(秒):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.delay_after_var = tk.DoubleVar(value=0.0)
        self.delay_after_spin = ttk.Spinbox(exec_frame, from_=0.0, to=300.0, increment=1.0,
                                          textvariable=self.delay_after_var, width=10)
        self.delay_after_spin.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # 出错时继续
        self.continue_on_error_var = tk.BooleanVar(value=True)
        self.continue_check = ttk.Checkbutton(exec_frame, text="出错时继续执行",
                                            variable=self.continue_on_error_var)
        self.continue_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

    def _create_buttons(self, parent):
        """创建按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="确定", command=self._on_ok).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.RIGHT)

    def _load_step_data(self):
        """加载步骤数据"""
        if not self.step:
            return

        # 查找对应的任务流
        for i, flow in enumerate(self.available_flows):
            if flow.flow_id == self.step.flow.flow_id:
                self.flow_combo.current(i)
                break

        self.order_var.set(self.step.order)
        self.enabled_var.set(self.step.enabled)
        self.delay_before_var.set(self.step.delay_before)
        self.delay_after_var.set(self.step.delay_after)
        self.continue_on_error_var.set(self.step.continue_on_error)

    def _on_ok(self):
        """确定按钮事件"""
        try:
            # 验证输入
            if not self.flow_var.get():
                messagebox.showerror("错误", "请选择任务流")
                return

            # 获取选中的任务流
            flow_index = self.flow_combo.current()
            if flow_index < 0:
                messagebox.showerror("错误", "请选择有效的任务流")
                return

            selected_flow = self.available_flows[flow_index]

            # 创建结果
            self.result = {
                'flow': selected_flow,
                'order': self.order_var.get(),
                'enabled': self.enabled_var.get(),
                'delay_before': self.delay_before_var.get(),
                'delay_after': self.delay_after_var.get(),
                'continue_on_error': self.continue_on_error_var.get()
            }

            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败:\n{str(e)}")

    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()

    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


class WorkflowEditorWidget(ttk.Frame):
    """工作流编辑器组件"""

    def __init__(self, parent, **kwargs):
        """初始化编辑器"""
        super().__init__(parent, **kwargs)

        self.current_workflow: Optional[Workflow] = None
        self.available_flows: List[TaskFlow] = []
        self.task_integration = None
        self.modified = False

        # 回调函数
        self.on_workflow_changed: Optional[Callable[[Workflow], None]] = None
        self.on_modified_changed: Optional[Callable[[bool], None]] = None

        self._create_widgets()
        self._setup_layout()

        # 初始化任务集成
        self._init_task_integration()

    def _init_task_integration(self):
        """初始化任务集成"""
        if TASK_MODULE_AVAILABLE:
            try:
                self.task_integration = get_integration()
                self._refresh_available_flows()
            except Exception as e:
                print(f"任务集成初始化失败: {e}")

    def _create_widgets(self):
        """创建子组件"""
        # 工具栏
        self.toolbar = ttk.Frame(self)

        # 文件操作
        ttk.Button(self.toolbar, text="新建", command=self._new_workflow).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="打开", command=self._open_workflow).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="保存", command=self._save_workflow).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="另存为", command=self._save_workflow_as).pack(side=tk.LEFT, padx=2)

        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        # 编辑操作
        ttk.Button(self.toolbar, text="添加流程", command=self._add_flow_step).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="编辑步骤", command=self._edit_step).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="删除步骤", command=self._delete_step).pack(side=tk.LEFT, padx=2)

        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        # 调度操作
        ttk.Button(self.toolbar, text="调度配置", command=self._configure_schedule).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="启动调度", command=self._start_schedule).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="停止调度", command=self._stop_schedule).pack(side=tk.LEFT, padx=2)

        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        # 执行操作
        ttk.Button(self.toolbar, text="验证", command=self._validate_workflow).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="立即执行", command=self._execute_workflow).pack(side=tk.LEFT, padx=2)

        # 主编辑区域
        self.main_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)

        # 左侧步骤列表
        self._create_steps_panel()

        # 右侧配置面板
        self._create_config_panel()

        self.main_paned.add(self.steps_frame, weight=1)
        self.main_paned.add(self.config_frame, weight=1)

    def _create_steps_panel(self):
        """创建步骤面板"""
        self.steps_frame = ttk.LabelFrame(self.main_paned, text="工作流步骤")

        # 步骤列表
        list_frame = ttk.Frame(self.steps_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview
        self.steps_tree = ttk.Treeview(list_frame, columns=("order", "delay", "status"), show="tree headings")
        self.steps_tree.heading("#0", text="任务流名称")
        self.steps_tree.heading("order", text="顺序")
        self.steps_tree.heading("delay", text="延迟")
        self.steps_tree.heading("status", text="状态")

        self.steps_tree.column("#0", width=200)
        self.steps_tree.column("order", width=50)
        self.steps_tree.column("delay", width=80)
        self.steps_tree.column("status", width=60)

        # 滚动条
        steps_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.steps_tree.yview)
        self.steps_tree.configure(yscrollcommand=steps_scrollbar.set)

        self.steps_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        steps_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定事件
        self.steps_tree.bind('<Double-1>', lambda e: self._edit_step())
        self.steps_tree.bind('<Button-3>', self._show_step_context_menu)

        # 右键菜单
        self.step_context_menu = tk.Menu(self, tearoff=0)
        self.step_context_menu.add_command(label="编辑", command=self._edit_step)
        self.step_context_menu.add_command(label="删除", command=self._delete_step)
        self.step_context_menu.add_separator()
        self.step_context_menu.add_command(label="上移", command=self._move_step_up)
        self.step_context_menu.add_command(label="下移", command=self._move_step_down)
        self.step_context_menu.add_separator()
        self.step_context_menu.add_command(label="启用/禁用", command=self._toggle_step)

    def _create_config_panel(self):
        """创建配置面板"""
        self.config_frame = ttk.LabelFrame(self.main_paned, text="工作流配置")

        # 创建笔记本控件
        self.config_notebook = ttk.Notebook(self.config_frame)
        self.config_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 基本信息标签页
        self._create_basic_info_tab()

        # 调度配置标签页
        self._create_schedule_tab()

        # 执行统计标签页
        self._create_statistics_tab()

    def _create_basic_info_tab(self):
        """创建基本信息标签页"""
        self.basic_info_frame = ttk.Frame(self.config_notebook)
        self.config_notebook.add(self.basic_info_frame, text="基本信息")

        # 工作流信息
        info_frame = ttk.LabelFrame(self.basic_info_frame, text="工作流信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(info_frame, text="工作流ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.workflow_id_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.workflow_id_var, state="readonly").grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        ttk.Label(info_frame, text="工作流名称:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.workflow_name_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.workflow_name_var).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)

        ttk.Label(info_frame, text="描述:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.workflow_desc_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.workflow_desc_var).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)

        info_frame.grid_columnconfigure(1, weight=1)

        # 执行选项
        options_frame = ttk.LabelFrame(self.basic_info_frame, text="执行选项")
        options_frame.pack(fill=tk.X, padx=5, pady=5)

        self.continue_on_error_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="出错时继续执行", variable=self.continue_on_error_var).pack(anchor=tk.W, padx=5, pady=5)

        # 绑定变化事件
        self.workflow_name_var.trace('w', self._on_basic_info_changed)
        self.workflow_desc_var.trace('w', self._on_basic_info_changed)
        self.continue_on_error_var.trace('w', self._on_basic_info_changed)

    def _create_schedule_tab(self):
        """创建调度配置标签页"""
        self.schedule_frame = ttk.Frame(self.config_notebook)
        self.config_notebook.add(self.schedule_frame, text="调度配置")

        # 调度状态
        status_frame = ttk.LabelFrame(self.schedule_frame, text="调度状态")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        self.schedule_status_label = ttk.Label(status_frame, text="未配置调度", font=("Arial", 10, "bold"))
        self.schedule_status_label.pack(pady=5)

        # 调度信息
        info_frame = ttk.LabelFrame(self.schedule_frame, text="调度信息")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建文本框显示调度详情
        self.schedule_info_text = tk.Text(info_frame, height=10, wrap=tk.WORD, state=tk.DISABLED)
        schedule_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.schedule_info_text.yview)
        self.schedule_info_text.configure(yscrollcommand=schedule_scrollbar.set)

        self.schedule_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        schedule_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def _create_statistics_tab(self):
        """创建执行统计标签页"""
        self.statistics_frame = ttk.Frame(self.config_notebook)
        self.config_notebook.add(self.statistics_frame, text="执行统计")

        # 统计信息
        stats_frame = ttk.LabelFrame(self.statistics_frame, text="执行统计")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)

        # 创建统计标签
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(stats_grid, text="总执行次数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.total_executions_label = ttk.Label(stats_grid, text="0", font=("Arial", 10, "bold"))
        self.total_executions_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(stats_grid, text="成功次数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.success_executions_label = ttk.Label(stats_grid, text="0", font=("Arial", 10, "bold"), foreground="green")
        self.success_executions_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(stats_grid, text="失败次数:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.failed_executions_label = ttk.Label(stats_grid, text="0", font=("Arial", 10, "bold"), foreground="red")
        self.failed_executions_label.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(stats_grid, text="上次执行:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.last_execution_label = ttk.Label(stats_grid, text="从未执行")
        self.last_execution_label.grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(stats_grid, text="下次执行:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        self.next_execution_label = ttk.Label(stats_grid, text="未安排")
        self.next_execution_label.grid(row=4, column=1, sticky=tk.W, padx=5, pady=2)

        # 刷新按钮
        ttk.Button(stats_frame, text="刷新统计", command=self._refresh_statistics).pack(pady=5)

    def _setup_layout(self):
        """设置布局"""
        self.toolbar.pack(fill=tk.X, padx=5, pady=5)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

    def _refresh_available_flows(self):
        """刷新可用任务流列表"""
        if self.task_integration:
            try:
                flow_data = self.task_integration.get_available_flows()
                self.available_flows = []

                # 这里需要重新构建任务流对象
                # 实际实现中需要从TaskManager获取任务流实例
                for flow_info in flow_data:
                    # 暂时创建占位符任务流
                    pass

            except Exception as e:
                print(f"刷新可用任务流失败: {e}")

    def _new_workflow(self):
        """新建工作流"""
        if self.modified:
            if not messagebox.askyesno("确认", "当前工作流已修改，是否放弃修改？"):
                return

        workflow_id = simpledialog.askstring("新建工作流", "请输入工作流ID:")
        if not workflow_id:
            return

        workflow_name = simpledialog.askstring("新建工作流", "请输入工作流名称:")
        if not workflow_name:
            return

        description = simpledialog.askstring("新建工作流", "请输入工作流描述:", initialvalue="")

        # 创建新工作流
        if TASK_MODULE_AVAILABLE:
            self.current_workflow = Workflow(workflow_id, workflow_name, description or "")
        else:
            self.current_workflow = None

        self._refresh_ui()
        self._set_modified(False)

    def _open_workflow(self):
        """打开工作流"""
        messagebox.showinfo("提示", "打开工作流功能开发中")

    def _save_workflow(self):
        """保存工作流"""
        if not self.current_workflow:
            messagebox.showwarning("警告", "没有可保存的工作流")
            return

        messagebox.showinfo("提示", "保存工作流功能开发中")
        self._set_modified(False)

    def _save_workflow_as(self):
        """另存为工作流"""
        messagebox.showinfo("提示", "另存为功能开发中")

    def _add_flow_step(self):
        """添加任务流步骤"""
        if not self.current_workflow:
            messagebox.showwarning("警告", "请先创建或打开工作流")
            return

        dialog = WorkflowStepConfigDialog(self, available_flows=self.available_flows)
        result = dialog.show()

        if result:
            # 添加步骤到工作流
            if TASK_MODULE_AVAILABLE and self.current_workflow:
                self.current_workflow.add_flow(
                    flow=result['flow'],
                    order=result['order'],
                    delay_before=result['delay_before'],
                    delay_after=result['delay_after'],
                    continue_on_error=result['continue_on_error']
                )

            self._refresh_ui()
            self._set_modified(True)

    def _edit_step(self):
        """编辑步骤"""
        selected = self.steps_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择要编辑的步骤")
            return

        messagebox.showinfo("提示", "编辑步骤功能开发中")

    def _delete_step(self):
        """删除步骤"""
        selected = self.steps_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择要删除的步骤")
            return

        if messagebox.askyesno("确认删除", "确定要删除选中的步骤吗？"):
            # 从工作流中删除步骤
            step_id = selected[0]
            if TASK_MODULE_AVAILABLE and self.current_workflow:
                # 这里需要实现删除步骤的方法
                pass

            self._refresh_ui()
            self._set_modified(True)

    def _move_step_up(self):
        """上移步骤"""
        messagebox.showinfo("提示", "移动步骤功能开发中")

    def _move_step_down(self):
        """下移步骤"""
        messagebox.showinfo("提示", "移动步骤功能开发中")

    def _toggle_step(self):
        """切换步骤启用状态"""
        messagebox.showinfo("提示", "切换步骤状态功能开发中")

    def _configure_schedule(self):
        """配置调度"""
        if not self.current_workflow:
            messagebox.showwarning("警告", "请先创建或打开工作流")
            return

        current_config = None
        if TASK_MODULE_AVAILABLE and self.current_workflow:
            current_config = self.current_workflow.schedule_config

        dialog = ScheduleConfigDialog(self, current_config)
        result = dialog.show()

        if result:
            if TASK_MODULE_AVAILABLE and self.current_workflow:
                self.current_workflow.set_schedule(result)

            self._refresh_schedule_info()
            self._set_modified(True)

    def _start_schedule(self):
        """启动调度"""
        if not self.current_workflow:
            messagebox.showwarning("警告", "没有可启动的工作流")
            return

        if TASK_MODULE_AVAILABLE and self.current_workflow:
            if self.current_workflow.start_scheduled_execution():
                messagebox.showinfo("成功", "工作流调度已启动")
                self._refresh_schedule_info()
            else:
                messagebox.showerror("失败", "启动工作流调度失败")
        else:
            messagebox.showinfo("提示", "任务模块未加载")

    def _stop_schedule(self):
        """停止调度"""
        if not self.current_workflow:
            messagebox.showwarning("警告", "没有可停止的工作流")
            return

        if TASK_MODULE_AVAILABLE and self.current_workflow:
            self.current_workflow.stop()
            messagebox.showinfo("成功", "工作流调度已停止")
            self._refresh_schedule_info()
        else:
            messagebox.showinfo("提示", "任务模块未加载")

    def _validate_workflow(self):
        """验证工作流"""
        if not self.current_workflow:
            messagebox.showwarning("警告", "没有可验证的工作流")
            return

        if TASK_MODULE_AVAILABLE:
            is_valid, errors = self.current_workflow.validate()
            if is_valid:
                messagebox.showinfo("验证结果", "工作流配置正确")
            else:
                error_msg = "工作流配置错误:\n" + "\n".join(errors)
                messagebox.showerror("验证失败", error_msg)
        else:
            messagebox.showinfo("验证结果", "任务模块未加载，无法验证")

    def _execute_workflow(self):
        """立即执行工作流"""
        if not self.current_workflow:
            messagebox.showwarning("警告", "没有可执行的工作流")
            return

        messagebox.showinfo("提示", "立即执行功能开发中")

    def _show_step_context_menu(self, event):
        """显示步骤右键菜单"""
        item = self.steps_tree.identify_row(event.y)
        if item:
            self.steps_tree.selection_set(item)
            try:
                self.step_context_menu.post(event.x_root, event.y_root)
            finally:
                self.step_context_menu.grab_release()

    def _on_basic_info_changed(self, *args):
        """基本信息改变事件"""
        if self.current_workflow and TASK_MODULE_AVAILABLE:
            self.current_workflow.name = self.workflow_name_var.get()
            self.current_workflow.description = self.workflow_desc_var.get()
            self.current_workflow.continue_on_error = self.continue_on_error_var.get()
            self._set_modified(True)

    def _refresh_ui(self):
        """刷新界面"""
        # 清空步骤列表
        for item in self.steps_tree.get_children():
            self.steps_tree.delete(item)

        # 重新加载步骤
        if self.current_workflow and TASK_MODULE_AVAILABLE:
            for step in self.current_workflow.steps:
                status = "✅" if step.enabled else "❌"
                delay_info = f"{step.delay_before}+{step.delay_after}"

                self.steps_tree.insert("", "end", iid=step.id,
                                     text=step.flow.name,
                                     values=(step.order, delay_info, status))

            # 更新基本信息
            self.workflow_id_var.set(self.current_workflow.workflow_id)
            self.workflow_name_var.set(self.current_workflow.name)
            self.workflow_desc_var.set(self.current_workflow.description)
            self.continue_on_error_var.set(self.current_workflow.continue_on_error)

            # 更新调度信息
            self._refresh_schedule_info()

            # 更新统计信息
            self._refresh_statistics()

    def _refresh_schedule_info(self):
        """刷新调度信息"""
        if not self.current_workflow or not TASK_MODULE_AVAILABLE:
            self.schedule_status_label.config(text="未配置调度")
            self.schedule_info_text.config(state=tk.NORMAL)
            self.schedule_info_text.delete(1.0, tk.END)
            self.schedule_info_text.config(state=tk.DISABLED)
            return

        schedule_config = self.current_workflow.schedule_config

        if not schedule_config or not schedule_config.enabled:
            self.schedule_status_label.config(text="调度未启用", foreground="gray")
            info_text = "当前工作流未配置调度或调度已禁用"
        else:
            # 根据调度状态设置状态文本
            if self.current_workflow.status.value == "running":
                self.schedule_status_label.config(text="调度运行中", foreground="green")
            else:
                self.schedule_status_label.config(text="调度已配置", foreground="blue")

            # 构建调度信息文本
            info_lines = []
            info_lines.append(f"调度类型: {schedule_config.schedule_type.value}")

            if schedule_config.schedule_type.value == "interval":
                info_lines.append(f"执行间隔: {schedule_config.interval_seconds} 秒")
            elif schedule_config.schedule_type.value == "cron":
                info_lines.append(f"Cron表达式: {schedule_config.cron_expression}")

            if schedule_config.start_time:
                info_lines.append(f"开始时间: {schedule_config.start_time.strftime('%Y-%m-%d %H:%M:%S')}")

            if schedule_config.end_time:
                info_lines.append(f"结束时间: {schedule_config.end_time.strftime('%Y-%m-%d %H:%M:%S')}")

            if schedule_config.max_executions:
                info_lines.append(f"最大执行次数: {schedule_config.max_executions}")

            if self.current_workflow.next_execution_time:
                info_lines.append(f"下次执行时间: {self.current_workflow.next_execution_time.strftime('%Y-%m-%d %H:%M:%S')}")

            info_text = "\n".join(info_lines)

        # 更新文本框
        self.schedule_info_text.config(state=tk.NORMAL)
        self.schedule_info_text.delete(1.0, tk.END)
        self.schedule_info_text.insert(tk.END, info_text)
        self.schedule_info_text.config(state=tk.DISABLED)

    def _refresh_statistics(self):
        """刷新统计信息"""
        if not self.current_workflow or not TASK_MODULE_AVAILABLE:
            self.total_executions_label.config(text="0")
            self.success_executions_label.config(text="0")
            self.failed_executions_label.config(text="0")
            self.last_execution_label.config(text="从未执行")
            self.next_execution_label.config(text="未安排")
            return

        # 更新执行统计
        self.total_executions_label.config(text=str(self.current_workflow.execution_count))

        # 这里需要从工作流获取详细统计信息
        # 暂时使用占位符数据
        self.success_executions_label.config(text="0")
        self.failed_executions_label.config(text="0")

        # 更新执行时间
        if self.current_workflow.last_execution_time:
            last_time = self.current_workflow.last_execution_time.strftime('%Y-%m-%d %H:%M:%S')
            self.last_execution_label.config(text=last_time)
        else:
            self.last_execution_label.config(text="从未执行")

        if self.current_workflow.next_execution_time:
            next_time = self.current_workflow.next_execution_time.strftime('%Y-%m-%d %H:%M:%S')
            self.next_execution_label.config(text=next_time)
        else:
            self.next_execution_label.config(text="未安排")

    def _set_modified(self, modified: bool):
        """设置修改状态"""
        self.modified = modified
        if self.on_modified_changed:
            self.on_modified_changed(modified)

    def set_workflow(self, workflow: Workflow):
        """设置当前编辑的工作流"""
        self.current_workflow = workflow
        self._refresh_ui()
        self._set_modified(False)

        if self.on_workflow_changed:
            self.on_workflow_changed(workflow)

    def get_workflow(self) -> Optional[Workflow]:
        """获取当前工作流"""
        return self.current_workflow