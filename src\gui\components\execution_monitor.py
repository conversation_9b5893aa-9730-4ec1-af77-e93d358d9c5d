# -*- coding: utf-8 -*-
"""
执行监控组件
提供实时的任务执行监控和状态显示
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from task.base_task import TaskStatus, TaskResult
    from task.task_flow import TaskFlow, FlowResult
    from task.workflow import Workflow, WorkflowResult
    TASK_MODULE_AVAILABLE = True
except ImportError:
    TASK_MODULE_AVAILABLE = False


@dataclass
class ExecutionEvent:
    """执行事件"""
    timestamp: datetime
    event_type: str  # start, progress, complete, error, warning
    entity_type: str  # task, flow, workflow
    entity_id: str
    entity_name: str
    message: str
    details: Dict[str, Any] = None


class ExecutionMonitorWidget(ttk.Frame):
    """执行监控组件"""
    
    def __init__(self, parent, **kwargs):
        """初始化监控组件"""
        super().__init__(parent, **kwargs)
        
        self.execution_events: List[ExecutionEvent] = []
        self.max_events = 1000  # 最大事件数量
        self.auto_scroll = True
        self.monitoring_active = False
        
        # 回调函数
        self.on_event_callback: Optional[Callable[[ExecutionEvent], None]] = None
        
        self._create_widgets()
        self._setup_layout()
        
        # 启动监控线程
        self._start_monitoring()
    
    def _create_widgets(self):
        """创建子组件"""
        # 工具栏
        self.toolbar = ttk.Frame(self)
        
        # 控制按钮
        self.start_btn = ttk.Button(self.toolbar, text="开始监控", command=self._start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=2)
        
        self.stop_btn = ttk.Button(self.toolbar, text="停止监控", command=self._stop_monitoring)
        self.stop_btn.pack(side=tk.LEFT, padx=2)
        
        self.clear_btn = ttk.Button(self.toolbar, text="清空日志", command=self._clear_events)
        self.clear_btn.pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # 过滤选项
        ttk.Label(self.toolbar, text="过滤:").pack(side=tk.LEFT, padx=(5, 2))
        
        self.filter_var = tk.StringVar(value="all")
        self.filter_combo = ttk.Combobox(self.toolbar, textvariable=self.filter_var,
                                       values=["all", "task", "flow", "workflow", "error"],
                                       state="readonly", width=10)
        self.filter_combo.pack(side=tk.LEFT, padx=2)
        self.filter_combo.bind('<<ComboboxSelected>>', self._on_filter_changed)
        
        # 自动滚动选项
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(self.toolbar, text="自动滚动", variable=self.auto_scroll_var,
                       command=self._on_auto_scroll_changed).pack(side=tk.RIGHT, padx=5)
        
        # 状态显示
        self.status_label = ttk.Label(self.toolbar, text="监控状态: 停止")
        self.status_label.pack(side=tk.RIGHT, padx=10)
        
        # 主显示区域
        self.main_paned = ttk.PanedWindow(self, orient=tk.VERTICAL)
        
        # 事件列表
        self._create_events_panel()
        
        # 详情面板
        self._create_details_panel()
        
        self.main_paned.add(self.events_frame, weight=3)
        self.main_paned.add(self.details_frame, weight=1)
    
    def _create_events_panel(self):
        """创建事件面板"""
        self.events_frame = ttk.LabelFrame(self.main_paned, text="执行事件")
        
        # 创建Treeview
        columns = ('time', 'type', 'entity', 'message')
        self.events_tree = ttk.Treeview(self.events_frame, columns=columns, show='headings')
        
        # 配置列
        self.events_tree.heading('time', text='时间')
        self.events_tree.heading('type', text='类型')
        self.events_tree.heading('entity', text='实体')
        self.events_tree.heading('message', text='消息')
        
        self.events_tree.column('time', width=150)
        self.events_tree.column('type', width=80)
        self.events_tree.column('entity', width=120)
        self.events_tree.column('message', width=300)
        
        # 滚动条
        events_scrollbar_v = ttk.Scrollbar(self.events_frame, orient=tk.VERTICAL, command=self.events_tree.yview)
        events_scrollbar_h = ttk.Scrollbar(self.events_frame, orient=tk.HORIZONTAL, command=self.events_tree.xview)
        self.events_tree.configure(yscrollcommand=events_scrollbar_v.set, xscrollcommand=events_scrollbar_h.set)
        
        # 布局
        self.events_tree.grid(row=0, column=0, sticky='nsew')
        events_scrollbar_v.grid(row=0, column=1, sticky='ns')
        events_scrollbar_h.grid(row=1, column=0, sticky='ew')
        
        self.events_frame.grid_rowconfigure(0, weight=1)
        self.events_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.events_tree.bind('<<TreeviewSelect>>', self._on_event_select)
        self.events_tree.bind('<Double-1>', self._on_event_double_click)
    
    def _create_details_panel(self):
        """创建详情面板"""
        self.details_frame = ttk.LabelFrame(self.main_paned, text="事件详情")
        
        # 创建文本框
        self.details_text = tk.Text(self.details_frame, wrap=tk.WORD, state=tk.DISABLED)
        details_scrollbar = ttk.Scrollbar(self.details_frame, orient=tk.VERTICAL, command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scrollbar.set)
        
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        details_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _setup_layout(self):
        """设置布局"""
        self.toolbar.pack(fill=tk.X, padx=5, pady=5)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
    
    def _start_monitoring(self):
        """开始监控"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.status_label.config(text="监控状态: 运行中", foreground="green")
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
    
    def _stop_monitoring(self):
        """停止监控"""
        if self.monitoring_active:
            self.monitoring_active = False
            self.status_label.config(text="监控状态: 停止", foreground="red")
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 这里应该从任务系统获取执行事件
                # 暂时使用模拟数据
                if TASK_MODULE_AVAILABLE:
                    # 实际实现中应该从任务管理器获取事件
                    pass
                
                time.sleep(0.5)  # 监控间隔
                
            except Exception as e:
                print(f"监控循环错误: {e}")
                time.sleep(1)
    
    def add_event(self, event: ExecutionEvent):
        """添加执行事件"""
        # 添加到事件列表
        self.execution_events.append(event)
        
        # 限制事件数量
        if len(self.execution_events) > self.max_events:
            self.execution_events.pop(0)
        
        # 更新UI（在主线程中执行）
        self.after(0, lambda: self._update_events_display([event]))
        
        # 调用回调函数
        if self.on_event_callback:
            try:
                self.on_event_callback(event)
            except Exception as e:
                print(f"事件回调错误: {e}")
    
    def _update_events_display(self, new_events: List[ExecutionEvent] = None):
        """更新事件显示"""
        if new_events is None:
            # 重新加载所有事件
            self.events_tree.delete(*self.events_tree.get_children())
            events_to_show = self._filter_events(self.execution_events)
        else:
            # 只添加新事件
            events_to_show = self._filter_events(new_events)
        
        for event in events_to_show:
            # 确定事件类型的显示样式
            type_display = self._get_event_type_display(event.event_type)
            entity_display = f"{event.entity_type}: {event.entity_name}"
            time_display = event.timestamp.strftime("%H:%M:%S.%f")[:-3]
            
            # 插入到树形视图
            item_id = self.events_tree.insert('', 'end', values=(
                time_display, type_display, entity_display, event.message
            ))
            
            # 设置颜色
            self._set_event_color(item_id, event.event_type)
        
        # 自动滚动到底部
        if self.auto_scroll_var.get() and self.events_tree.get_children():
            last_item = self.events_tree.get_children()[-1]
            self.events_tree.see(last_item)
    
    def _filter_events(self, events: List[ExecutionEvent]) -> List[ExecutionEvent]:
        """过滤事件"""
        filter_type = self.filter_var.get()
        
        if filter_type == "all":
            return events
        elif filter_type == "error":
            return [e for e in events if e.event_type in ["error", "warning"]]
        else:
            return [e for e in events if e.entity_type == filter_type]
    
    def _get_event_type_display(self, event_type: str) -> str:
        """获取事件类型显示文本"""
        type_map = {
            "start": "🚀 开始",
            "progress": "⏳ 进行",
            "complete": "✅ 完成",
            "error": "❌ 错误",
            "warning": "⚠️ 警告"
        }
        return type_map.get(event_type, event_type)
    
    def _set_event_color(self, item_id: str, event_type: str):
        """设置事件颜色"""
        color_map = {
            "start": "blue",
            "progress": "orange",
            "complete": "green",
            "error": "red",
            "warning": "darkorange"
        }
        
        color = color_map.get(event_type, "black")
        self.events_tree.set(item_id, 'type', self._get_event_type_display(event_type))
        
        # 设置标签颜色
        tag_name = f"event_{event_type}"
        self.events_tree.tag_configure(tag_name, foreground=color)
        self.events_tree.item(item_id, tags=(tag_name,))
    
    def _on_filter_changed(self, event=None):
        """过滤器改变事件"""
        self._update_events_display()
    
    def _on_auto_scroll_changed(self):
        """自动滚动选项改变"""
        self.auto_scroll = self.auto_scroll_var.get()
    
    def _on_event_select(self, event):
        """事件选择事件"""
        selection = self.events_tree.selection()
        if selection:
            item_id = selection[0]
            item_values = self.events_tree.item(item_id, 'values')
            
            # 查找对应的事件
            time_str = item_values[0]
            message = item_values[3]
            
            for exec_event in self.execution_events:
                event_time = exec_event.timestamp.strftime("%H:%M:%S.%f")[:-3]
                if event_time == time_str and exec_event.message == message:
                    self._show_event_details(exec_event)
                    break
    
    def _on_event_double_click(self, event):
        """事件双击事件"""
        # 可以在这里添加更详细的事件查看功能
        pass
    
    def _show_event_details(self, event: ExecutionEvent):
        """显示事件详情"""
        self.details_text.config(state=tk.NORMAL)
        self.details_text.delete(1.0, tk.END)
        
        # 构建详情文本
        details = []
        details.append(f"时间: {event.timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        details.append(f"事件类型: {event.event_type}")
        details.append(f"实体类型: {event.entity_type}")
        details.append(f"实体ID: {event.entity_id}")
        details.append(f"实体名称: {event.entity_name}")
        details.append(f"消息: {event.message}")
        
        if event.details:
            details.append("\n详细信息:")
            for key, value in event.details.items():
                details.append(f"  {key}: {value}")
        
        self.details_text.insert(tk.END, "\n".join(details))
        self.details_text.config(state=tk.DISABLED)
    
    def _clear_events(self):
        """清空事件"""
        if messagebox.askyesno("确认清空", "确定要清空所有执行事件吗？"):
            self.execution_events.clear()
            self.events_tree.delete(*self.events_tree.get_children())
            self.details_text.config(state=tk.NORMAL)
            self.details_text.delete(1.0, tk.END)
            self.details_text.config(state=tk.DISABLED)
    
    def set_event_callback(self, callback: Callable[[ExecutionEvent], None]):
        """设置事件回调函数"""
        self.on_event_callback = callback
    
    def simulate_task_execution(self, task_name: str = "测试任务"):
        """模拟任务执行（用于测试）"""
        def simulate():
            # 开始事件
            start_event = ExecutionEvent(
                timestamp=datetime.now(),
                event_type="start",
                entity_type="task",
                entity_id="test_001",
                entity_name=task_name,
                message=f"开始执行任务: {task_name}"
            )
            self.add_event(start_event)
            
            # 进度事件
            for i in range(3):
                time.sleep(1)
                progress_event = ExecutionEvent(
                    timestamp=datetime.now(),
                    event_type="progress",
                    entity_type="task",
                    entity_id="test_001",
                    entity_name=task_name,
                    message=f"任务执行中... ({i+1}/3)",
                    details={"progress": f"{(i+1)*33}%"}
                )
                self.add_event(progress_event)
            
            # 完成事件
            complete_event = ExecutionEvent(
                timestamp=datetime.now(),
                event_type="complete",
                entity_type="task",
                entity_id="test_001",
                entity_name=task_name,
                message=f"任务执行完成: {task_name}",
                details={"duration": "3.2秒", "result": "成功"}
            )
            self.add_event(complete_event)
        
        # 在后台线程中运行模拟
        threading.Thread(target=simulate, daemon=True).start()


class ExecutionStatusWidget(ttk.Frame):
    """执行状态组件"""
    
    def __init__(self, parent, **kwargs):
        """初始化状态组件"""
        super().__init__(parent, **kwargs)
        
        self.current_executions: Dict[str, Dict[str, Any]] = {}
        
        self._create_widgets()
        self._setup_layout()
    
    def _create_widgets(self):
        """创建子组件"""
        # 状态标签
        self.status_frame = ttk.LabelFrame(self, text="执行状态")
        
        # 总体状态
        status_grid = ttk.Frame(self.status_frame)
        status_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(status_grid, text="运行中任务:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.running_count_label = ttk.Label(status_grid, text="0", font=("Arial", 12, "bold"), foreground="blue")
        self.running_count_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(status_grid, text="等待中任务:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.waiting_count_label = ttk.Label(status_grid, text="0", font=("Arial", 12, "bold"), foreground="orange")
        self.waiting_count_label.grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(status_grid, text="已完成任务:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.completed_count_label = ttk.Label(status_grid, text="0", font=("Arial", 12, "bold"), foreground="green")
        self.completed_count_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(status_grid, text="失败任务:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        self.failed_count_label = ttk.Label(status_grid, text="0", font=("Arial", 12, "bold"), foreground="red")
        self.failed_count_label.grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)
        
        # 当前执行列表
        self.current_frame = ttk.LabelFrame(self, text="当前执行")
        
        # 创建列表
        self.current_tree = ttk.Treeview(self.current_frame, columns=('type', 'status', 'progress'), show='tree headings')
        self.current_tree.heading('#0', text='名称')
        self.current_tree.heading('type', text='类型')
        self.current_tree.heading('status', text='状态')
        self.current_tree.heading('progress', text='进度')
        
        self.current_tree.column('#0', width=150)
        self.current_tree.column('type', width=80)
        self.current_tree.column('status', width=80)
        self.current_tree.column('progress', width=100)
        
        current_scrollbar = ttk.Scrollbar(self.current_frame, orient=tk.VERTICAL, command=self.current_tree.yview)
        self.current_tree.configure(yscrollcommand=current_scrollbar.set)
        
        self.current_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        current_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _setup_layout(self):
        """设置布局"""
        self.status_frame.pack(fill=tk.X, padx=5, pady=5)
        self.current_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
    
    def update_execution_status(self, entity_id: str, entity_name: str, entity_type: str, 
                              status: str, progress: str = ""):
        """更新执行状态"""
        if entity_id in self.current_executions:
            # 更新现有项目
            item_id = self.current_executions[entity_id]['item_id']
            self.current_tree.item(item_id, values=(entity_type, status, progress))
        else:
            # 添加新项目
            item_id = self.current_tree.insert('', 'end', text=entity_name,
                                             values=(entity_type, status, progress))
            self.current_executions[entity_id] = {
                'item_id': item_id,
                'name': entity_name,
                'type': entity_type,
                'status': status
            }
        
        # 更新统计
        self._update_statistics()
    
    def remove_execution(self, entity_id: str):
        """移除执行项目"""
        if entity_id in self.current_executions:
            item_id = self.current_executions[entity_id]['item_id']
            self.current_tree.delete(item_id)
            del self.current_executions[entity_id]
            self._update_statistics()
    
    def _update_statistics(self):
        """更新统计信息"""
        running_count = 0
        waiting_count = 0
        completed_count = 0
        failed_count = 0
        
        for execution in self.current_executions.values():
            status = execution['status'].lower()
            if status in ['running', 'executing']:
                running_count += 1
            elif status in ['waiting', 'pending']:
                waiting_count += 1
            elif status in ['completed', 'success']:
                completed_count += 1
            elif status in ['failed', 'error']:
                failed_count += 1
        
        self.running_count_label.config(text=str(running_count))
        self.waiting_count_label.config(text=str(waiting_count))
        self.completed_count_label.config(text=str(completed_count))
        self.failed_count_label.config(text=str(failed_count))
