# -*- coding: utf-8 -*-
"""
任务流类
管理基础任务的组合和执行流程
"""

import json
import time
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from .base_task import BaseTask, TaskStatus, TaskResult


class FlowStatus(Enum):
    """任务流状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    SUCCESS = "success"      # 执行成功
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消
    PAUSED = "paused"        # 已暂停


class ExecutionMode(Enum):
    """执行模式枚举"""
    SEQUENTIAL = "sequential"    # 顺序执行
    PARALLEL = "parallel"        # 并行执行
    CONDITIONAL = "conditional"  # 条件执行


@dataclass
class FlowStep:
    """任务流步骤"""
    id: str
    task: BaseTask
    order: int
    enabled: bool = True
    delay_before: float = 0.0
    delay_after: float = 0.0
    continue_on_error: bool = True
    condition: Optional[Callable[[], bool]] = None
    parameters: Optional[Dict[str, Any]] = None


@dataclass
class FlowResult:
    """任务流执行结果"""
    flow_id: str
    status: FlowStatus
    message: str
    step_results: List[TaskResult]
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    success_count: int = 0
    failed_count: int = 0


class TaskFlow:
    """
    任务流类
    管理多个基础任务的组合执行
    """
    
    def __init__(self, flow_id: str, name: str, description: str = ""):
        """
        初始化任务流
        
        Args:
            flow_id: 任务流ID
            name: 任务流名称
            description: 任务流描述
        """
        self.flow_id = flow_id
        self.name = name
        self.description = description
        self.steps: List[FlowStep] = []
        self.status = FlowStatus.PENDING
        self.execution_mode = ExecutionMode.SEQUENTIAL
        self.continue_on_error = True
        self.max_parallel_tasks = 5
        self.logger = None
        
        # 执行状态
        self.current_step = 0
        self.start_time = None
        self.end_time = None
        self.step_results = []
        
    def add_task(self, task: BaseTask, order: int = None, 
                 delay_before: float = 0.0, delay_after: float = 0.0,
                 continue_on_error: bool = True, 
                 condition: Optional[Callable[[], bool]] = None,
                 parameters: Optional[Dict[str, Any]] = None) -> str:
        """
        添加任务到流程中
        
        Args:
            task: 基础任务
            order: 执行顺序
            delay_before: 执行前延迟
            delay_after: 执行后延迟
            continue_on_error: 出错时是否继续
            condition: 执行条件函数
            parameters: 任务参数
            
        Returns:
            步骤ID
        """
        if order is None:
            order = len(self.steps)
            
        step_id = f"{self.flow_id}_step_{len(self.steps)}"
        step = FlowStep(
            id=step_id,
            task=task,
            order=order,
            delay_before=delay_before,
            delay_after=delay_after,
            continue_on_error=continue_on_error,
            condition=condition,
            parameters=parameters
        )
        
        self.steps.append(step)
        # 按顺序排序
        self.steps.sort(key=lambda x: x.order)
        
        return step_id
    
    def remove_task(self, step_id: str) -> bool:
        """
        从流程中移除任务
        
        Args:
            step_id: 步骤ID
            
        Returns:
            是否移除成功
        """
        for i, step in enumerate(self.steps):
            if step.id == step_id:
                self.steps.pop(i)
                return True
        return False
    
    def set_execution_mode(self, mode: ExecutionMode):
        """设置执行模式"""
        self.execution_mode = mode
    
    def run(self) -> FlowResult:
        """
        执行任务流
        
        Returns:
            任务流执行结果
        """
        if not self.steps:
            return FlowResult(
                flow_id=self.flow_id,
                status=FlowStatus.FAILED,
                message="任务流为空",
                step_results=[]
            )
        
        self.status = FlowStatus.RUNNING
        self.start_time = datetime.now()
        self.step_results = []
        
        try:
            if self.execution_mode == ExecutionMode.SEQUENTIAL:
                result = self._run_sequential()
            elif self.execution_mode == ExecutionMode.PARALLEL:
                result = self._run_parallel()
            elif self.execution_mode == ExecutionMode.CONDITIONAL:
                result = self._run_conditional()
            else:
                result = self._run_sequential()
                
        except Exception as e:
            result = FlowResult(
                flow_id=self.flow_id,
                status=FlowStatus.FAILED,
                message=f"任务流执行异常: {str(e)}",
                step_results=self.step_results
            )
        
        self.end_time = datetime.now()
        result.start_time = self.start_time
        result.end_time = self.end_time
        result.duration = (self.end_time - self.start_time).total_seconds()
        
        self.status = result.status
        return result
    
    def _run_sequential(self) -> FlowResult:
        """顺序执行任务"""
        success_count = 0
        failed_count = 0
        
        for i, step in enumerate(self.steps):
            if not step.enabled:
                continue
                
            # 检查执行条件
            if step.condition and not step.condition():
                if self.logger:
                    self.logger.info(f"步骤 {step.id} 条件不满足，跳过执行")
                continue
            
            # 执行前延迟
            if step.delay_before > 0:
                time.sleep(step.delay_before)
            
            # 执行任务
            self.current_step = i
            if self.logger:
                self.logger.info(f"开始执行步骤 {step.id}: {step.task.task_name}")
            
            result = step.task.run()
            self.step_results.append(result)
            
            if result.status == TaskStatus.SUCCESS:
                success_count += 1
                if self.logger:
                    self.logger.info(f"步骤 {step.id} 执行成功")
            else:
                failed_count += 1
                if self.logger:
                    self.logger.error(f"步骤 {step.id} 执行失败: {result.message}")
                
                if not step.continue_on_error:
                    return FlowResult(
                        flow_id=self.flow_id,
                        status=FlowStatus.FAILED,
                        message=f"步骤 {step.id} 执行失败，流程终止",
                        step_results=self.step_results,
                        success_count=success_count,
                        failed_count=failed_count
                    )
            
            # 执行后延迟
            if step.delay_after > 0:
                time.sleep(step.delay_after)
        
        # 确定最终状态
        if failed_count == 0:
            status = FlowStatus.SUCCESS
            message = f"任务流执行成功，共执行 {success_count} 个任务"
        elif success_count > 0:
            status = FlowStatus.SUCCESS  # 部分成功也算成功
            message = f"任务流部分成功，成功 {success_count} 个，失败 {failed_count} 个"
        else:
            status = FlowStatus.FAILED
            message = f"任务流执行失败，失败 {failed_count} 个任务"
        
        return FlowResult(
            flow_id=self.flow_id,
            status=status,
            message=message,
            step_results=self.step_results,
            success_count=success_count,
            failed_count=failed_count
        )
    
    def _run_parallel(self) -> FlowResult:
        """并行执行任务"""
        import concurrent.futures
        import threading

        success_count = 0
        failed_count = 0

        # 创建线程池
        max_workers = min(self.max_parallel_tasks, len(self.steps))

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有启用的任务
            future_to_step = {}

            for i, step in enumerate(self.steps):
                if not step.enabled:
                    continue

                # 检查执行条件
                if step.condition and not step.condition():
                    if self.logger:
                        self.logger.info(f"步骤 {step.id} 条件不满足，跳过执行")
                    continue

                # 提交任务到线程池
                future = executor.submit(self._execute_step_parallel, step, i)
                future_to_step[future] = step

            # 等待所有任务完成
            for future in concurrent.futures.as_completed(future_to_step):
                step = future_to_step[future]

                try:
                    result = future.result()
                    self.step_results.append(result)

                    if result.status == TaskStatus.SUCCESS:
                        success_count += 1
                        if self.logger:
                            self.logger.info(f"步骤 {step.id} 并行执行成功")
                    else:
                        failed_count += 1
                        if self.logger:
                            self.logger.error(f"步骤 {step.id} 并行执行失败: {result.message}")

                        if not step.continue_on_error and not self.continue_on_error:
                            # 取消其他任务
                            for f in future_to_step:
                                if not f.done():
                                    f.cancel()
                            break

                except Exception as e:
                    failed_count += 1
                    if self.logger:
                        self.logger.error(f"步骤 {step.id} 并行执行异常: {e}")

        # 确定最终状态
        if failed_count == 0:
            status = FlowStatus.SUCCESS
            message = f"任务流并行执行成功，共执行 {success_count} 个任务"
        elif success_count > 0:
            status = FlowStatus.SUCCESS
            message = f"任务流部分成功，成功 {success_count} 个，失败 {failed_count} 个"
        else:
            status = FlowStatus.FAILED
            message = f"任务流并行执行失败，失败 {failed_count} 个任务"

        return FlowResult(
            flow_id=self.flow_id,
            status=status,
            message=message,
            step_results=self.step_results,
            success_count=success_count,
            failed_count=failed_count
        )

    def _execute_step_parallel(self, step: FlowStep, step_index: int) -> TaskResult:
        """在并行环境中执行单个步骤"""
        import time

        # 执行前延迟
        if step.delay_before > 0:
            time.sleep(step.delay_before)

        # 执行任务
        self.current_step = step_index
        if self.logger:
            self.logger.info(f"开始并行执行步骤 {step.id}: {step.task.task_name}")

        result = step.task.run()

        # 执行后延迟
        if step.delay_after > 0:
            time.sleep(step.delay_after)

        return result

    def _run_conditional(self) -> FlowResult:
        """条件执行任务 - 基于条件分支的执行"""
        success_count = 0
        failed_count = 0
        executed_count = 0

        for i, step in enumerate(self.steps):
            if not step.enabled:
                continue

            # 检查执行条件
            should_execute = True
            if step.condition:
                try:
                    should_execute = step.condition()
                    if self.logger:
                        condition_result = "满足" if should_execute else "不满足"
                        self.logger.info(f"步骤 {step.id} 执行条件{condition_result}")
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"步骤 {step.id} 条件检查失败: {e}")
                    should_execute = False

            if not should_execute:
                continue

            # 执行前延迟
            if step.delay_before > 0:
                time.sleep(step.delay_before)

            # 执行任务
            self.current_step = i
            executed_count += 1

            if self.logger:
                self.logger.info(f"开始条件执行步骤 {step.id}: {step.task.task_name}")

            result = step.task.run()
            self.step_results.append(result)

            if result.status == TaskStatus.SUCCESS:
                success_count += 1
                if self.logger:
                    self.logger.info(f"步骤 {step.id} 条件执行成功")
            else:
                failed_count += 1
                if self.logger:
                    self.logger.error(f"步骤 {step.id} 条件执行失败: {result.message}")

                if not step.continue_on_error:
                    return FlowResult(
                        flow_id=self.flow_id,
                        status=FlowStatus.FAILED,
                        message=f"步骤 {step.id} 执行失败，流程终止",
                        step_results=self.step_results,
                        success_count=success_count,
                        failed_count=failed_count
                    )

            # 执行后延迟
            if step.delay_after > 0:
                time.sleep(step.delay_after)

        # 确定最终状态
        if executed_count == 0:
            status = FlowStatus.SUCCESS
            message = "任务流条件执行完成，无任务满足执行条件"
        elif failed_count == 0:
            status = FlowStatus.SUCCESS
            message = f"任务流条件执行成功，执行 {executed_count} 个任务，成功 {success_count} 个"
        elif success_count > 0:
            status = FlowStatus.SUCCESS
            message = f"任务流部分成功，执行 {executed_count} 个，成功 {success_count} 个，失败 {failed_count} 个"
        else:
            status = FlowStatus.FAILED
            message = f"任务流条件执行失败，执行 {executed_count} 个，失败 {failed_count} 个"

        return FlowResult(
            flow_id=self.flow_id,
            status=status,
            message=message,
            step_results=self.step_results,
            success_count=success_count,
            failed_count=failed_count
        )
    
    def pause(self):
        """暂停任务流"""
        if self.status == FlowStatus.RUNNING:
            self.status = FlowStatus.PAUSED
            if self.logger:
                self.logger.info(f"任务流 {self.name} 已暂停")
            return True
        return False

    def resume(self):
        """恢复任务流"""
        if self.status == FlowStatus.PAUSED:
            self.status = FlowStatus.RUNNING
            if self.logger:
                self.logger.info(f"任务流 {self.name} 已恢复")
            return True
        return False

    def cancel(self):
        """取消任务流"""
        if self.status in [FlowStatus.RUNNING, FlowStatus.PAUSED]:
            self.status = FlowStatus.CANCELLED
            if self.logger:
                self.logger.info(f"任务流 {self.name} 已取消")
            return True
        return False

    def reset(self):
        """重置任务流状态"""
        self.status = FlowStatus.PENDING
        self.current_step = 0
        self.start_time = None
        self.end_time = None
        self.step_results = []
        if self.logger:
            self.logger.info(f"任务流 {self.name} 已重置")

    def clone(self, new_flow_id: str = None, new_name: str = None) -> 'TaskFlow':
        """克隆任务流"""
        import uuid

        if new_flow_id is None:
            new_flow_id = f"{self.flow_id}_clone_{uuid.uuid4().hex[:8]}"

        if new_name is None:
            new_name = f"{self.name} (副本)"

        # 创建新的任务流
        cloned_flow = TaskFlow(new_flow_id, new_name, self.description)
        cloned_flow.execution_mode = self.execution_mode
        cloned_flow.continue_on_error = self.continue_on_error
        cloned_flow.max_parallel_tasks = self.max_parallel_tasks

        # 复制步骤
        for step in self.steps:
            cloned_flow.add_task(
                task=step.task,
                order=step.order,
                delay_before=step.delay_before,
                delay_after=step.delay_after,
                continue_on_error=step.continue_on_error,
                condition=step.condition,
                parameters=step.parameters.copy() if step.parameters else None
            )

        if self.logger:
            self.logger.info(f"任务流 {self.name} 已克隆为 {new_name}")

        return cloned_flow

    def validate(self) -> tuple[bool, list[str]]:
        """验证任务流配置"""
        errors = []

        # 检查基本信息
        if not self.flow_id:
            errors.append("任务流ID不能为空")

        if not self.name:
            errors.append("任务流名称不能为空")

        # 检查步骤
        if not self.steps:
            errors.append("任务流至少需要一个步骤")

        # 检查步骤顺序
        orders = [step.order for step in self.steps if step.enabled]
        if len(orders) != len(set(orders)):
            errors.append("步骤顺序不能重复")

        # 检查任务有效性
        for step in self.steps:
            if not step.task:
                errors.append(f"步骤 {step.id} 缺少任务")
                continue

            # 验证任务配置
            if hasattr(step.task, 'validate_config'):
                if not step.task.validate_config():
                    errors.append(f"步骤 {step.id} 的任务配置无效")

        # 检查并行执行配置
        if self.execution_mode == ExecutionMode.PARALLEL:
            if self.max_parallel_tasks <= 0:
                errors.append("并行执行时最大并行任务数必须大于0")

        return len(errors) == 0, errors

    def get_execution_plan(self) -> dict:
        """获取执行计划"""
        enabled_steps = [step for step in self.steps if step.enabled]

        plan = {
            'flow_id': self.flow_id,
            'flow_name': self.name,
            'execution_mode': self.execution_mode.value,
            'total_steps': len(enabled_steps),
            'estimated_duration': 0,
            'steps': []
        }

        for step in enabled_steps:
            step_info = {
                'step_id': step.id,
                'task_name': step.task.task_name,
                'order': step.order,
                'delay_before': step.delay_before,
                'delay_after': step.delay_after,
                'continue_on_error': step.continue_on_error,
                'has_condition': step.condition is not None
            }

            # 估算执行时间
            estimated_time = step.delay_before + step.delay_after
            if hasattr(step.task.config, 'timeout'):
                estimated_time += step.task.config.timeout

            step_info['estimated_duration'] = estimated_time
            plan['estimated_duration'] += estimated_time
            plan['steps'].append(step_info)

        return plan
    
    def get_progress(self) -> Dict[str, Any]:
        """获取执行进度"""
        total_steps = len([s for s in self.steps if s.enabled])
        completed_steps = len(self.step_results)
        
        return {
            'flow_id': self.flow_id,
            'name': self.name,
            'status': self.status.value,
            'total_steps': total_steps,
            'completed_steps': completed_steps,
            'current_step': self.current_step,
            'progress_percent': (completed_steps / total_steps * 100) if total_steps > 0 else 0
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """将任务流转换为字典格式"""
        return {
            'flow_id': self.flow_id,
            'name': self.name,
            'description': self.description,
            'status': self.status.value,
            'execution_mode': self.execution_mode.value,
            'steps_count': len(self.steps),
            'enabled_steps_count': len([s for s in self.steps if s.enabled])
        }
