# -*- coding: utf-8 -*-
"""
主窗口 V2 - 优化版本
基于新任务模块架构的现代化GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from .utils.gui_helpers import (
    GUIStyles, StatusBar, ThreadSafeGUI, LogTextWidget,
    show_error, show_info, ask_yes_no, center_window
)

# 导入新的任务模块
try:
    from task.integration import get_integration
    TASK_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"新任务模块导入失败: {e}")
    TASK_MODULE_AVAILABLE = False

# 导入现有组件
from .components.task_list import TaskListWidget
from .components.window_capture_test import WindowCaptureTestWidget


class SnowZoneMainWindowV2:
    """SnowZone主窗口V2类 - 优化版本"""
    
    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()
        self.root.title("SnowZone OCR自动化工具 V2")
        self.root.geometry("1400x900")  # 增大默认窗口尺寸
        # self.root.minsize(1024, 768)    # 设置最小尺寸 - 某些tkinter版本不支持
        
        # 初始化任务集成
        self._init_task_integration()
        
        # 创建界面
        self._create_menu()
        self._create_toolbar()
        self._create_main_area()
        self._create_status_bar()
        
        # 设置布局
        self._setup_layout()
        
        # 绑定事件
        self._bind_events()
        
        # 线程安全GUI操作
        self.thread_safe_gui = ThreadSafeGUI(self.root)
        
        # 初始化界面数据
        self._init_ui_data()
        
        # 应用样式
        self._apply_styles()
    
    def _init_task_integration(self):
        """初始化任务集成"""
        if TASK_MODULE_AVAILABLE:
            try:
                self.task_integration = get_integration()
                self.task_manager = self.task_integration.get_task_manager()
                print("新任务模块集成成功")
            except Exception as e:
                print(f"任务模块集成失败: {e}")
                self.task_integration = None
                self.task_manager = None
        else:
            self.task_integration = None
            self.task_manager = None
    
    def _create_menu(self):
        """创建菜单栏"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # 文件菜单
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建任务", command=self._new_task, accelerator="Ctrl+N")
        file_menu.add_command(label="新建任务流", command=self._new_task_flow, accelerator="Ctrl+Shift+N")
        file_menu.add_command(label="新建工作流", command=self._new_workflow, accelerator="Ctrl+Alt+N")
        file_menu.add_separator()
        file_menu.add_command(label="导入配置", command=self._import_config)
        file_menu.add_command(label="导出配置", command=self._export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._quit_app, accelerator="Ctrl+Q")
        
        # 编辑菜单
        edit_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="撤销", command=self._undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="重做", command=self._redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="复制", command=self._copy, accelerator="Ctrl+C")
        edit_menu.add_command(label="粘贴", command=self._paste, accelerator="Ctrl+V")
        edit_menu.add_command(label="删除", command=self._delete, accelerator="Delete")
        
        # 执行菜单
        run_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="执行", menu=run_menu)
        run_menu.add_command(label="执行选中任务", command=self._run_selected_task, accelerator="F5")
        run_menu.add_command(label="执行任务流", command=self._run_task_flow, accelerator="F6")
        run_menu.add_command(label="启动工作流", command=self._start_workflow, accelerator="F7")
        run_menu.add_separator()
        run_menu.add_command(label="停止所有执行", command=self._stop_all_execution, accelerator="Ctrl+F12")
        
        # 视图菜单
        view_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="视图", menu=view_menu)
        view_menu.add_command(label="刷新", command=self._refresh_all, accelerator="F5")
        view_menu.add_separator()
        view_menu.add_command(label="显示/隐藏左侧面板", command=self._toggle_left_panel)
        view_menu.add_command(label="显示/隐藏右侧面板", command=self._toggle_right_panel)
        view_menu.add_separator()
        view_menu.add_command(label="全屏编辑模式", command=self._toggle_fullscreen_edit)
        
        # 工具菜单
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="窗口捕获测试", command=self._show_window_capture_test)
        tools_menu.add_command(label="OCR测试", command=self._show_ocr_test)
        tools_menu.add_command(label="任务统计", command=self._show_task_statistics)
        tools_menu.add_separator()
        tools_menu.add_command(label="设置", command=self._show_settings)
        
        # 帮助菜单
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _create_toolbar(self):
        """创建工具栏"""
        self.toolbar = ttk.Frame(self.root)
        
        # 文件操作按钮
        ttk.Button(self.toolbar, text="新建任务", command=self._new_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="新建流程", command=self._new_task_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="新建工作流", command=self._new_workflow).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # 执行控制按钮
        ttk.Button(self.toolbar, text="▶ 执行", command=self._run_selected_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="⏸ 暂停", command=self._pause_execution).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="⏹ 停止", command=self._stop_execution).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # 视图控制按钮
        ttk.Button(self.toolbar, text="🔄 刷新", command=self._refresh_all).pack(side=tk.LEFT, padx=2)
        
        # 右侧状态显示
        self.status_label = ttk.Label(self.toolbar, text="就绪")
        self.status_label.pack(side=tk.RIGHT, padx=10)
    
    def _create_main_area(self):
        """创建主工作区 - 三面板布局"""
        # 主PanedWindow - 水平分割
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        
        # 左侧面板 - 任务资源管理
        self._create_left_panel()
        
        # 中央面板 - 可视化编辑器
        self._create_center_panel()
        
        # 右侧面板 - 监控与日志
        self._create_right_panel()
        
        # 设置面板权重 - 移除minsize参数以兼容旧版本tkinter
        self.main_paned.add(self.left_panel, weight=1)
        self.main_paned.add(self.center_panel, weight=2)
        self.main_paned.add(self.right_panel, weight=1)
    
    def _create_left_panel(self):
        """创建左侧面板 - 任务资源管理"""
        self.left_panel = ttk.Frame(self.main_paned)
        
        # 创建笔记本控件
        self.left_notebook = ttk.Notebook(self.left_panel)
        
        # 基础任务库标签页
        self._create_base_tasks_tab()
        
        # 任务流库标签页
        self._create_task_flows_tab()
        
        # 工作流库标签页
        self._create_workflows_tab()
        
        self.left_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _create_base_tasks_tab(self):
        """创建基础任务库标签页"""
        self.base_tasks_frame = ttk.Frame(self.left_notebook)
        self.left_notebook.add(self.base_tasks_frame, text="基础任务")
        
        # 搜索框
        search_frame = ttk.Frame(self.base_tasks_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        self.task_search_var = tk.StringVar()
        self.task_search_entry = ttk.Entry(search_frame, textvariable=self.task_search_var)
        self.task_search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        self.task_search_var.trace('w', self._on_task_search_changed)
        
        # 分类过滤
        filter_frame = ttk.Frame(self.base_tasks_frame)
        filter_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Label(filter_frame, text="分类:").pack(side=tk.LEFT)
        self.category_var = tk.StringVar(value="全部")
        self.category_combo = ttk.Combobox(filter_frame, textvariable=self.category_var, 
                                          values=["全部", "basic", "daily", "battle", "system"],
                                          state="readonly", width=10)
        self.category_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.category_combo.bind('<<ComboboxSelected>>', self._on_category_changed)
        
        # 任务树形列表
        tree_frame = ttk.Frame(self.base_tasks_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # 创建Treeview
        self.tasks_tree = ttk.Treeview(tree_frame, columns=("status", "category"), show="tree headings")
        self.tasks_tree.heading("#0", text="任务名称")
        self.tasks_tree.heading("status", text="状态")
        self.tasks_tree.heading("category", text="分类")
        
        self.tasks_tree.column("#0", width=200)
        self.tasks_tree.column("status", width=60)
        self.tasks_tree.column("category", width=80)
        
        # 滚动条
        tasks_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tasks_tree.yview)
        self.tasks_tree.configure(yscrollcommand=tasks_scrollbar.set)
        
        self.tasks_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tasks_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右键菜单
        self.tasks_context_menu = tk.Menu(self.root, tearoff=0)
        self.tasks_context_menu.add_command(label="编辑", command=self._edit_selected_task)
        self.tasks_context_menu.add_command(label="复制", command=self._copy_selected_task)
        self.tasks_context_menu.add_command(label="删除", command=self._delete_selected_task)
        self.tasks_context_menu.add_separator()
        self.tasks_context_menu.add_command(label="添加到流程", command=self._add_task_to_flow)
        self.tasks_context_menu.add_command(label="单独执行", command=self._run_single_task)
        
        self.tasks_tree.bind("<Button-3>", self._show_tasks_context_menu)
        self.tasks_tree.bind("<Double-1>", lambda e: self._edit_selected_task())
        
        # 操作按钮
        buttons_frame = ttk.Frame(self.base_tasks_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="新建", command=self._new_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="编辑", command=self._edit_selected_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="删除", command=self._delete_selected_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="刷新", command=self._refresh_tasks).pack(side=tk.RIGHT, padx=2)

    def _create_task_flows_tab(self):
        """创建任务流库标签页"""
        self.task_flows_frame = ttk.Frame(self.left_notebook)
        self.left_notebook.add(self.task_flows_frame, text="任务流")

        # 任务流列表
        flows_frame = ttk.Frame(self.task_flows_frame)
        flows_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.flows_tree = ttk.Treeview(flows_frame, columns=("steps", "status"), show="tree headings")
        self.flows_tree.heading("#0", text="任务流名称")
        self.flows_tree.heading("steps", text="步骤数")
        self.flows_tree.heading("status", text="状态")

        self.flows_tree.column("#0", width=200)
        self.flows_tree.column("steps", width=60)
        self.flows_tree.column("status", width=80)

        flows_scrollbar = ttk.Scrollbar(flows_frame, orient=tk.VERTICAL, command=self.flows_tree.yview)
        self.flows_tree.configure(yscrollcommand=flows_scrollbar.set)

        self.flows_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        flows_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 右键菜单
        self.flows_context_menu = tk.Menu(self.root, tearoff=0)
        self.flows_context_menu.add_command(label="编辑", command=self._edit_selected_flow)
        self.flows_context_menu.add_command(label="复制", command=self._copy_selected_flow)
        self.flows_context_menu.add_command(label="删除", command=self._delete_selected_flow)
        self.flows_context_menu.add_separator()
        self.flows_context_menu.add_command(label="添加到工作流", command=self._add_flow_to_workflow)
        self.flows_context_menu.add_command(label="执行", command=self._run_selected_flow)

        self.flows_tree.bind("<Button-3>", self._show_flows_context_menu)
        self.flows_tree.bind("<Double-1>", lambda e: self._edit_selected_flow())

        # 操作按钮
        flow_buttons_frame = ttk.Frame(self.task_flows_frame)
        flow_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(flow_buttons_frame, text="新建", command=self._new_task_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(flow_buttons_frame, text="编辑", command=self._edit_selected_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(flow_buttons_frame, text="删除", command=self._delete_selected_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(flow_buttons_frame, text="刷新", command=self._refresh_flows).pack(side=tk.RIGHT, padx=2)

    def _create_workflows_tab(self):
        """创建工作流库标签页"""
        self.workflows_frame = ttk.Frame(self.left_notebook)
        self.left_notebook.add(self.workflows_frame, text="工作流")

        # 工作流列表
        workflows_frame = ttk.Frame(self.workflows_frame)
        workflows_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.workflows_tree = ttk.Treeview(workflows_frame, columns=("flows", "status", "schedule"), show="tree headings")
        self.workflows_tree.heading("#0", text="工作流名称")
        self.workflows_tree.heading("flows", text="流程数")
        self.workflows_tree.heading("status", text="状态")
        self.workflows_tree.heading("schedule", text="调度")

        self.workflows_tree.column("#0", width=180)
        self.workflows_tree.column("flows", width=50)
        self.workflows_tree.column("status", width=60)
        self.workflows_tree.column("schedule", width=60)

        workflows_scrollbar = ttk.Scrollbar(workflows_frame, orient=tk.VERTICAL, command=self.workflows_tree.yview)
        self.workflows_tree.configure(yscrollcommand=workflows_scrollbar.set)

        self.workflows_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        workflows_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 右键菜单
        self.workflows_context_menu = tk.Menu(self.root, tearoff=0)
        self.workflows_context_menu.add_command(label="编辑", command=self._edit_selected_workflow)
        self.workflows_context_menu.add_command(label="复制", command=self._copy_selected_workflow)
        self.workflows_context_menu.add_command(label="删除", command=self._delete_selected_workflow)
        self.workflows_context_menu.add_separator()
        self.workflows_context_menu.add_command(label="启动调度", command=self._start_selected_workflow)
        self.workflows_context_menu.add_command(label="停止调度", command=self._stop_selected_workflow)
        self.workflows_context_menu.add_command(label="立即执行", command=self._run_selected_workflow)

        self.workflows_tree.bind("<Button-3>", self._show_workflows_context_menu)
        self.workflows_tree.bind("<Double-1>", lambda e: self._edit_selected_workflow())

        # 操作按钮
        workflow_buttons_frame = ttk.Frame(self.workflows_frame)
        workflow_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(workflow_buttons_frame, text="新建", command=self._new_workflow).pack(side=tk.LEFT, padx=2)
        ttk.Button(workflow_buttons_frame, text="编辑", command=self._edit_selected_workflow).pack(side=tk.LEFT, padx=2)
        ttk.Button(workflow_buttons_frame, text="删除", command=self._delete_selected_workflow).pack(side=tk.LEFT, padx=2)
        ttk.Button(workflow_buttons_frame, text="刷新", command=self._refresh_workflows).pack(side=tk.RIGHT, padx=2)

    def _create_center_panel(self):
        """创建中央面板 - 可视化编辑器"""
        self.center_panel = ttk.Frame(self.main_paned)

        # 创建笔记本控件
        self.center_notebook = ttk.Notebook(self.center_panel)

        # 任务流编辑器标签页
        self._create_flow_editor_tab()

        # 工作流编辑器标签页
        self._create_workflow_editor_tab()

        # 任务编辑器标签页
        self._create_task_editor_tab()

        self.center_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def _create_flow_editor_tab(self):
        """创建任务流编辑器标签页"""
        self.flow_editor_frame = ttk.Frame(self.center_notebook)
        self.center_notebook.add(self.flow_editor_frame, text="任务流编辑器")

        # 工具栏
        editor_toolbar = ttk.Frame(self.flow_editor_frame)
        editor_toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(editor_toolbar, text="新建流程", command=self._new_flow_in_editor).pack(side=tk.LEFT, padx=2)
        ttk.Button(editor_toolbar, text="保存", command=self._save_current_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(editor_toolbar, text="另存为", command=self._save_flow_as).pack(side=tk.LEFT, padx=2)

        ttk.Separator(editor_toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        ttk.Button(editor_toolbar, text="撤销", command=self._undo_flow_edit).pack(side=tk.LEFT, padx=2)
        ttk.Button(editor_toolbar, text="重做", command=self._redo_flow_edit).pack(side=tk.LEFT, padx=2)

        ttk.Separator(editor_toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        ttk.Button(editor_toolbar, text="验证", command=self._validate_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(editor_toolbar, text="预览", command=self._preview_flow).pack(side=tk.LEFT, padx=2)
        ttk.Button(editor_toolbar, text="测试执行", command=self._test_flow).pack(side=tk.LEFT, padx=2)

        # 编辑区域
        editor_main = ttk.PanedWindow(self.flow_editor_frame, orient=tk.HORIZONTAL)
        editor_main.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # 左侧 - 流程步骤列表
        steps_frame = ttk.LabelFrame(editor_main, text="流程步骤")
        editor_main.add(steps_frame, weight=1)

        self.flow_steps_tree = ttk.Treeview(steps_frame, columns=("order", "delay"), show="tree headings")
        self.flow_steps_tree.heading("#0", text="任务名称")
        self.flow_steps_tree.heading("order", text="顺序")
        self.flow_steps_tree.heading("delay", text="延迟")

        self.flow_steps_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 右侧 - 步骤配置
        config_frame = ttk.LabelFrame(editor_main, text="步骤配置")
        editor_main.add(config_frame, weight=1)

        # 这里将添加步骤配置的详细界面
        ttk.Label(config_frame, text="选择左侧步骤进行配置").pack(pady=20)

    def _create_workflow_editor_tab(self):
        """创建工作流编辑器标签页"""
        self.workflow_editor_frame = ttk.Frame(self.center_notebook)
        self.center_notebook.add(self.workflow_editor_frame, text="工作流编辑器")

        # 工具栏
        workflow_toolbar = ttk.Frame(self.workflow_editor_frame)
        workflow_toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(workflow_toolbar, text="新建工作流", command=self._new_workflow_in_editor).pack(side=tk.LEFT, padx=2)
        ttk.Button(workflow_toolbar, text="保存", command=self._save_current_workflow).pack(side=tk.LEFT, padx=2)
        ttk.Button(workflow_toolbar, text="调度设置", command=self._configure_schedule).pack(side=tk.LEFT, padx=2)

        # 编辑区域
        ttk.Label(self.workflow_editor_frame, text="工作流编辑器 - 开发中").pack(pady=50)

    def _create_task_editor_tab(self):
        """创建任务编辑器标签页"""
        self.task_editor_frame = ttk.Frame(self.center_notebook)
        self.center_notebook.add(self.task_editor_frame, text="任务编辑器")

        # 任务编辑器内容
        ttk.Label(self.task_editor_frame, text="任务编辑器 - 开发中").pack(pady=50)

    def _create_right_panel(self):
        """创建右侧面板 - 监控与日志"""
        self.right_panel = ttk.Frame(self.main_paned)

        # 创建笔记本控件
        self.right_notebook = ttk.Notebook(self.right_panel)

        # 执行监控标签页
        self._create_execution_monitor_tab()

        # 日志查看器标签页
        self._create_log_viewer_tab()

        # 统计分析标签页
        self._create_statistics_tab()

        self.right_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def _create_execution_monitor_tab(self):
        """创建执行监控标签页"""
        self.monitor_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(self.monitor_frame, text="执行监控")

        # 当前执行状态
        status_frame = ttk.LabelFrame(self.monitor_frame, text="当前状态")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        self.current_status_label = ttk.Label(status_frame, text="空闲", font=("Arial", 12, "bold"))
        self.current_status_label.pack(pady=5)

        # 执行进度
        progress_frame = ttk.LabelFrame(self.monitor_frame, text="执行进度")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        self.progress_label = ttk.Label(progress_frame, text="0/0 (0%)")
        self.progress_label.pack(pady=2)

        # 快速控制
        control_frame = ttk.LabelFrame(self.monitor_frame, text="快速控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        control_buttons = ttk.Frame(control_frame)
        control_buttons.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_buttons, text="▶", command=self._quick_start, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_buttons, text="⏸", command=self._quick_pause, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_buttons, text="⏹", command=self._quick_stop, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_buttons, text="🔄", command=self._quick_refresh, width=3).pack(side=tk.LEFT, padx=2)

        # 运行中的任务列表
        running_frame = ttk.LabelFrame(self.monitor_frame, text="运行中的任务")
        running_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.running_tasks_tree = ttk.Treeview(running_frame, columns=("status", "progress"), show="tree headings")
        self.running_tasks_tree.heading("#0", text="任务名称")
        self.running_tasks_tree.heading("status", text="状态")
        self.running_tasks_tree.heading("progress", text="进度")

        self.running_tasks_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def _create_log_viewer_tab(self):
        """创建日志查看器标签页"""
        self.log_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(self.log_frame, text="日志")

        # 日志控制栏
        log_control = ttk.Frame(self.log_frame)
        log_control.pack(fill=tk.X, padx=5, pady=5)

        # 日志级别过滤
        ttk.Label(log_control, text="级别:").pack(side=tk.LEFT)
        self.log_level_var = tk.StringVar(value="全部")
        log_level_combo = ttk.Combobox(log_control, textvariable=self.log_level_var,
                                      values=["全部", "DEBUG", "INFO", "WARNING", "ERROR"],
                                      state="readonly", width=8)
        log_level_combo.pack(side=tk.LEFT, padx=(2, 10))

        # 清空和导出按钮
        ttk.Button(log_control, text="清空", command=self._clear_logs).pack(side=tk.RIGHT, padx=2)
        ttk.Button(log_control, text="导出", command=self._export_logs).pack(side=tk.RIGHT, padx=2)
        ttk.Button(log_control, text="刷新", command=self._refresh_logs).pack(side=tk.RIGHT, padx=2)

        # 日志显示区域
        log_display_frame = ttk.Frame(self.log_frame)
        log_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        self.log_text = LogTextWidget(log_display_frame)
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def _create_statistics_tab(self):
        """创建统计分析标签页"""
        self.stats_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(self.stats_frame, text="统计")

        # 统计信息显示
        stats_info = ttk.LabelFrame(self.stats_frame, text="执行统计")
        stats_info.pack(fill=tk.X, padx=5, pady=5)

        # 创建统计标签
        stats_grid = ttk.Frame(stats_info)
        stats_grid.pack(fill=tk.X, padx=5, pady=5)

        # 第一行
        ttk.Label(stats_grid, text="总任务数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.total_tasks_label = ttk.Label(stats_grid, text="0", font=("Arial", 10, "bold"))
        self.total_tasks_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(stats_grid, text="成功执行:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.success_tasks_label = ttk.Label(stats_grid, text="0", font=("Arial", 10, "bold"), foreground="green")
        self.success_tasks_label.grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)

        # 第二行
        ttk.Label(stats_grid, text="任务流数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.total_flows_label = ttk.Label(stats_grid, text="0", font=("Arial", 10, "bold"))
        self.total_flows_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(stats_grid, text="失败执行:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        self.failed_tasks_label = ttk.Label(stats_grid, text="0", font=("Arial", 10, "bold"), foreground="red")
        self.failed_tasks_label.grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)

        # 第三行
        ttk.Label(stats_grid, text="工作流数:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.total_workflows_label = ttk.Label(stats_grid, text="0", font=("Arial", 10, "bold"))
        self.total_workflows_label.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(stats_grid, text="运行中:").grid(row=2, column=2, sticky=tk.W, padx=5, pady=2)
        self.running_tasks_label = ttk.Label(stats_grid, text="0", font=("Arial", 10, "bold"), foreground="blue")
        self.running_tasks_label.grid(row=2, column=3, sticky=tk.W, padx=5, pady=2)

        # 刷新按钮
        ttk.Button(stats_info, text="刷新统计", command=self._refresh_statistics).pack(pady=5)

        # 详细统计（预留空间）
        details_frame = ttk.LabelFrame(self.stats_frame, text="详细分析")
        details_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        ttk.Label(details_frame, text="详细统计图表 - 开发中").pack(pady=20)

    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = StatusBar(self.root)
        self.status_bar.set_status("就绪")

    def _setup_layout(self):
        """设置布局"""
        self.toolbar.pack(fill=tk.X, padx=5, pady=5)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

    def _bind_events(self):
        """绑定事件"""
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_closing)

        # 键盘快捷键
        self.root.bind('<Control-n>', lambda e: self._new_task())
        self.root.bind('<Control-Shift-N>', lambda e: self._new_task_flow())
        self.root.bind('<Control-Alt-n>', lambda e: self._new_workflow())
        self.root.bind('<Control-s>', lambda e: self._save_current())
        self.root.bind('<Control-q>', lambda e: self._quit_app())
        self.root.bind('<F5>', lambda e: self._refresh_all())
        self.root.bind('<F6>', lambda e: self._run_task_flow())
        self.root.bind('<F7>', lambda e: self._start_workflow())
        self.root.bind('<Delete>', lambda e: self._delete_selected())

        # 拖拽支持（预留）
        # self._setup_drag_drop()

    def _init_ui_data(self):
        """初始化界面数据"""
        if self.task_integration:
            self._refresh_all()
        else:
            self._add_log("WARNING", "任务模块未加载，部分功能不可用")

    def _apply_styles(self):
        """应用样式"""
        # 应用GUI样式 - 简化版本，避免方法不存在的问题
        try:
            # 设置主题样式
            style = ttk.Style()
            style.theme_use('clam')  # 使用现代主题
        except Exception as e:
            print(f"应用样式失败: {e}")

    # ==================== 菜单和工具栏事件处理 ====================

    def _new_task(self):
        """新建任务"""
        if not self.task_integration:
            show_error(self.root, "错误", "任务模块未加载")
            return

        # 切换到任务编辑器标签页
        self.center_notebook.select(2)  # 任务编辑器
        self._add_log("INFO", "开始创建新任务")

    def _new_task_flow(self):
        """新建任务流"""
        if not self.task_integration:
            show_error(self.root, "错误", "任务模块未加载")
            return

        # 切换到任务流编辑器标签页
        self.center_notebook.select(0)  # 任务流编辑器
        self._add_log("INFO", "开始创建新任务流")

    def _new_workflow(self):
        """新建工作流"""
        if not self.task_integration:
            show_error(self.root, "错误", "任务模块未加载")
            return

        # 切换到工作流编辑器标签页
        self.center_notebook.select(1)  # 工作流编辑器
        self._add_log("INFO", "开始创建新工作流")

    def _import_config(self):
        """导入配置"""
        show_info(self.root, "提示", "导入配置功能开发中")

    def _export_config(self):
        """导出配置"""
        show_info(self.root, "提示", "导出配置功能开发中")

    def _quit_app(self):
        """退出应用"""
        self._on_window_closing()

    def _undo(self):
        """撤销"""
        show_info(self.root, "提示", "撤销功能开发中")

    def _redo(self):
        """重做"""
        show_info(self.root, "提示", "重做功能开发中")

    def _copy(self):
        """复制"""
        show_info(self.root, "提示", "复制功能开发中")

    def _paste(self):
        """粘贴"""
        show_info(self.root, "提示", "粘贴功能开发中")

    def _delete(self):
        """删除"""
        # 根据当前焦点确定删除什么
        current_tab = self.left_notebook.index(self.left_notebook.select())
        if current_tab == 0:  # 基础任务
            self._delete_selected_task()
        elif current_tab == 1:  # 任务流
            self._delete_selected_flow()
        elif current_tab == 2:  # 工作流
            self._delete_selected_workflow()

    def _run_selected_task(self):
        """执行选中的任务"""
        if not self.task_integration:
            show_error(self.root, "错误", "任务模块未加载")
            return

        # 获取选中的任务
        selected = self.tasks_tree.selection()
        if not selected:
            show_info(self.root, "提示", "请先选择要执行的任务")
            return

        task_id = selected[0]
        self._add_log("INFO", f"开始执行任务: {task_id}")

        # 在后台线程中执行任务
        def execute_task():
            try:
                result = self.task_integration.execute_task(task_id)
                if result:
                    self.thread_safe_gui.call_later(
                        lambda: self._add_log("INFO", f"任务执行完成: {result.message}")
                    )
                else:
                    self.thread_safe_gui.call_later(
                        lambda: self._add_log("ERROR", "任务执行失败")
                    )
            except Exception as e:
                self.thread_safe_gui.call_later(
                    lambda: self._add_log("ERROR", f"任务执行异常: {e}")
                )

        threading.Thread(target=execute_task, daemon=True).start()

    def _run_task_flow(self):
        """执行任务流"""
        show_info(self.root, "提示", "任务流执行功能开发中")

    def _start_workflow(self):
        """启动工作流"""
        show_info(self.root, "提示", "工作流启动功能开发中")

    def _pause_execution(self):
        """暂停执行"""
        show_info(self.root, "提示", "暂停执行功能开发中")

    def _stop_execution(self):
        """停止执行"""
        show_info(self.root, "提示", "停止执行功能开发中")

    def _stop_all_execution(self):
        """停止所有执行"""
        show_info(self.root, "提示", "停止所有执行功能开发中")

    # ==================== 数据刷新方法 ====================

    def _refresh_all(self):
        """刷新所有数据"""
        self._refresh_tasks()
        self._refresh_flows()
        self._refresh_workflows()
        self._refresh_statistics()
        self._add_log("INFO", "界面数据已刷新")

    def _refresh_tasks(self):
        """刷新基础任务列表"""
        if not self.task_integration:
            return

        try:
            # 清空现有数据
            for item in self.tasks_tree.get_children():
                self.tasks_tree.delete(item)

            # 获取任务数据
            tasks = self.task_integration.get_available_tasks()

            # 按分类组织任务
            categories = {}
            for task in tasks:
                category = task.get('category', 'default')
                if category not in categories:
                    categories[category] = []
                categories[category].append(task)

            # 添加到树形控件
            for category, category_tasks in categories.items():
                # 添加分类节点
                category_node = self.tasks_tree.insert("", "end", text=f"📁 {category}",
                                                      values=("", category), tags=("category",))

                # 添加任务节点
                for task in category_tasks:
                    status = "✅" if task.get('enabled', True) else "❌"
                    self.tasks_tree.insert(category_node, "end", iid=task['id'],
                                         text=task['name'],
                                         values=(status, task['category']),
                                         tags=("task",))

            # 展开所有分类
            for item in self.tasks_tree.get_children():
                self.tasks_tree.item(item, open=True)

        except Exception as e:
            self._add_log("ERROR", f"刷新任务列表失败: {e}")

    def _refresh_flows(self):
        """刷新任务流列表"""
        if not self.task_integration:
            return

        try:
            # 清空现有数据
            for item in self.flows_tree.get_children():
                self.flows_tree.delete(item)

            # 获取任务流数据
            flows = self.task_integration.get_available_flows()

            # 添加到树形控件
            for flow in flows:
                status = "🟢" if flow.get('enabled', True) else "🔴"
                self.flows_tree.insert("", "end", iid=flow['flow_id'],
                                     text=flow['name'],
                                     values=(flow.get('steps_count', 0), status))

        except Exception as e:
            self._add_log("ERROR", f"刷新任务流列表失败: {e}")

    def _refresh_workflows(self):
        """刷新工作流列表"""
        if not self.task_integration:
            return

        try:
            # 清空现有数据
            for item in self.workflows_tree.get_children():
                self.workflows_tree.delete(item)

            # 获取工作流数据
            workflows = self.task_integration.get_available_workflows()

            # 添加到树形控件
            for workflow in workflows:
                status = "🟢" if workflow.get('enabled', True) else "🔴"
                schedule = "⏰" if workflow.get('schedule_enabled', False) else "📋"
                self.workflows_tree.insert("", "end", iid=workflow['workflow_id'],
                                         text=workflow['name'],
                                         values=(workflow.get('steps_count', 0), status, schedule))

        except Exception as e:
            self._add_log("ERROR", f"刷新工作流列表失败: {e}")

    def _refresh_statistics(self):
        """刷新统计信息"""
        if not self.task_integration:
            return

        try:
            stats = self.task_integration.get_task_statistics()

            # 更新统计标签
            self.total_tasks_label.config(text=str(stats.get('base_tasks_count', 0)))
            self.total_flows_label.config(text=str(stats.get('task_flows_count', 0)))
            self.total_workflows_label.config(text=str(stats.get('workflows_count', 0)))

            self.success_tasks_label.config(text=str(stats.get('successful_executions', 0)))
            self.failed_tasks_label.config(text=str(stats.get('failed_executions', 0)))
            self.running_tasks_label.config(text=str(stats.get('running_tasks', 0)))

        except Exception as e:
            self._add_log("ERROR", f"刷新统计信息失败: {e}")

    # ==================== 搜索和过滤 ====================

    def _on_task_search_changed(self, *args):
        """任务搜索变化事件"""
        search_text = self.task_search_var.get().lower()
        category = self.category_var.get()

        # 实现搜索过滤逻辑
        # 这里可以添加更复杂的搜索逻辑
        pass

    def _on_category_changed(self, event):
        """分类过滤变化事件"""
        category = self.category_var.get()
        # 实现分类过滤逻辑
        pass

    # ==================== 右键菜单事件 ====================

    def _show_tasks_context_menu(self, event):
        """显示任务右键菜单"""
        item = self.tasks_tree.identify_row(event.y)
        if item:
            self.tasks_tree.selection_set(item)
            self.tasks_context_menu.post(event.x_root, event.y_root)

    def _show_flows_context_menu(self, event):
        """显示任务流右键菜单"""
        item = self.flows_tree.identify_row(event.y)
        if item:
            self.flows_tree.selection_set(item)
            self.flows_context_menu.post(event.x_root, event.y_root)

    def _show_workflows_context_menu(self, event):
        """显示工作流右键菜单"""
        item = self.workflows_tree.identify_row(event.y)
        if item:
            self.workflows_tree.selection_set(item)
            self.workflows_context_menu.post(event.x_root, event.y_root)

    # ==================== 编辑操作 ====================

    def _edit_selected_task(self):
        """编辑选中的任务"""
        selected = self.tasks_tree.selection()
        if not selected:
            show_info(self.root, "提示", "请先选择要编辑的任务")
            return

        task_id = selected[0]
        # 检查是否是分类节点
        if self.tasks_tree.parent(task_id) == "":
            return  # 分类节点不能编辑

        self._add_log("INFO", f"编辑任务: {task_id}")
        # 这里可以打开任务编辑对话框
        show_info(self.root, "提示", f"编辑任务功能开发中: {task_id}")

    def _edit_selected_flow(self):
        """编辑选中的任务流"""
        selected = self.flows_tree.selection()
        if not selected:
            show_info(self.root, "提示", "请先选择要编辑的任务流")
            return

        flow_id = selected[0]
        self._add_log("INFO", f"编辑任务流: {flow_id}")
        show_info(self.root, "提示", f"编辑任务流功能开发中: {flow_id}")

    def _edit_selected_workflow(self):
        """编辑选中的工作流"""
        selected = self.workflows_tree.selection()
        if not selected:
            show_info(self.root, "提示", "请先选择要编辑的工作流")
            return

        workflow_id = selected[0]
        self._add_log("INFO", f"编辑工作流: {workflow_id}")
        show_info(self.root, "提示", f"编辑工作流功能开发中: {workflow_id}")

    # ==================== 删除操作 ====================

    def _delete_selected_task(self):
        """删除选中的任务"""
        selected = self.tasks_tree.selection()
        if not selected:
            show_info(self.root, "提示", "请先选择要删除的任务")
            return

        task_id = selected[0]
        # 检查是否是分类节点
        if self.tasks_tree.parent(task_id) == "":
            return  # 分类节点不能删除

        task_name = self.tasks_tree.item(task_id, "text")
        if ask_yes_no(self.root, "确认删除", f"确定要删除任务 '{task_name}' 吗？"):
            self._add_log("INFO", f"删除任务: {task_name}")
            # 这里添加实际的删除逻辑
            show_info(self.root, "提示", "删除任务功能开发中")

    def _delete_selected_flow(self):
        """删除选中的任务流"""
        selected = self.flows_tree.selection()
        if not selected:
            show_info(self.root, "提示", "请先选择要删除的任务流")
            return

        flow_id = selected[0]
        flow_name = self.flows_tree.item(flow_id, "text")
        if ask_yes_no(self.root, "确认删除", f"确定要删除任务流 '{flow_name}' 吗？"):
            self._add_log("INFO", f"删除任务流: {flow_name}")
            show_info(self.root, "提示", "删除任务流功能开发中")

    def _delete_selected_workflow(self):
        """删除选中的工作流"""
        selected = self.workflows_tree.selection()
        if not selected:
            show_info(self.root, "提示", "请先选择要删除的工作流")
            return

        workflow_id = selected[0]
        workflow_name = self.workflows_tree.item(workflow_id, "text")
        if ask_yes_no(self.root, "确认删除", f"确定要删除工作流 '{workflow_name}' 吗？"):
            self._add_log("INFO", f"删除工作流: {workflow_name}")
            show_info(self.root, "提示", "删除工作流功能开发中")

    # ==================== 其他操作方法 ====================

    def _copy_selected_task(self):
        """复制选中的任务"""
        show_info(self.root, "提示", "复制任务功能开发中")

    def _copy_selected_flow(self):
        """复制选中的任务流"""
        show_info(self.root, "提示", "复制任务流功能开发中")

    def _copy_selected_workflow(self):
        """复制选中的工作流"""
        show_info(self.root, "提示", "复制工作流功能开发中")

    def _add_task_to_flow(self):
        """将任务添加到流程"""
        show_info(self.root, "提示", "添加任务到流程功能开发中")

    def _add_flow_to_workflow(self):
        """将任务流添加到工作流"""
        show_info(self.root, "提示", "添加任务流到工作流功能开发中")

    def _run_single_task(self):
        """单独执行任务"""
        self._run_selected_task()

    def _run_selected_flow(self):
        """执行选中的任务流"""
        show_info(self.root, "提示", "执行任务流功能开发中")

    def _start_selected_workflow(self):
        """启动选中的工作流调度"""
        show_info(self.root, "提示", "启动工作流调度功能开发中")

    def _stop_selected_workflow(self):
        """停止选中的工作流调度"""
        show_info(self.root, "提示", "停止工作流调度功能开发中")

    def _run_selected_workflow(self):
        """立即执行选中的工作流"""
        show_info(self.root, "提示", "立即执行工作流功能开发中")

    # ==================== 编辑器相关方法 ====================

    def _new_flow_in_editor(self):
        """在编辑器中新建流程"""
        show_info(self.root, "提示", "流程编辑器功能开发中")

    def _save_current_flow(self):
        """保存当前流程"""
        show_info(self.root, "提示", "保存流程功能开发中")

    def _save_flow_as(self):
        """流程另存为"""
        show_info(self.root, "提示", "流程另存为功能开发中")

    def _undo_flow_edit(self):
        """撤销流程编辑"""
        show_info(self.root, "提示", "撤销编辑功能开发中")

    def _redo_flow_edit(self):
        """重做流程编辑"""
        show_info(self.root, "提示", "重做编辑功能开发中")

    def _validate_flow(self):
        """验证流程"""
        show_info(self.root, "提示", "验证流程功能开发中")

    def _preview_flow(self):
        """预览流程"""
        show_info(self.root, "提示", "预览流程功能开发中")

    def _test_flow(self):
        """测试执行流程"""
        show_info(self.root, "提示", "测试执行功能开发中")

    def _new_workflow_in_editor(self):
        """在编辑器中新建工作流"""
        show_info(self.root, "提示", "工作流编辑器功能开发中")

    def _save_current_workflow(self):
        """保存当前工作流"""
        show_info(self.root, "提示", "保存工作流功能开发中")

    def _configure_schedule(self):
        """配置调度"""
        show_info(self.root, "提示", "调度配置功能开发中")

    def _save_current(self):
        """保存当前内容"""
        # 根据当前活动的标签页决定保存什么
        current_tab = self.center_notebook.index(self.center_notebook.select())
        if current_tab == 0:  # 任务流编辑器
            self._save_current_flow()
        elif current_tab == 1:  # 工作流编辑器
            self._save_current_workflow()
        else:
            show_info(self.root, "提示", "当前没有可保存的内容")

    # ==================== 监控相关方法 ====================

    def _quick_start(self):
        """快速启动"""
        show_info(self.root, "提示", "快速启动功能开发中")

    def _quick_pause(self):
        """快速暂停"""
        show_info(self.root, "提示", "快速暂停功能开发中")

    def _quick_stop(self):
        """快速停止"""
        show_info(self.root, "提示", "快速停止功能开发中")

    def _quick_refresh(self):
        """快速刷新"""
        self._refresh_all()

    # ==================== 日志相关方法 ====================

    def _clear_logs(self):
        """清空日志"""
        self.log_text.clear_log()
        self._add_log("INFO", "日志已清空")

    def _export_logs(self):
        """导出日志"""
        show_info(self.root, "提示", "导出日志功能开发中")

    def _refresh_logs(self):
        """刷新日志"""
        show_info(self.root, "提示", "刷新日志功能开发中")

    def _add_log(self, level: str, message: str):
        """添加日志"""
        if hasattr(self, 'log_text'):
            self.log_text.append_log(level, message)

        # 更新状态栏
        if hasattr(self, 'status_bar'):
            if level == "ERROR":
                self.status_bar.set_status(f"错误: {message}")
            elif level == "WARNING":
                self.status_bar.set_status(f"警告: {message}")
            else:
                self.status_bar.set_status(message)

    # ==================== 视图控制方法 ====================

    def _toggle_left_panel(self):
        """切换左侧面板显示/隐藏"""
        show_info(self.root, "提示", "面板切换功能开发中")

    def _toggle_right_panel(self):
        """切换右侧面板显示/隐藏"""
        show_info(self.root, "提示", "面板切换功能开发中")

    def _toggle_fullscreen_edit(self):
        """切换全屏编辑模式"""
        show_info(self.root, "提示", "全屏编辑功能开发中")

    # ==================== 工具方法 ====================

    def _show_window_capture_test(self):
        """显示窗口捕获测试"""
        show_info(self.root, "提示", "窗口捕获测试功能开发中")

    def _show_ocr_test(self):
        """显示OCR测试"""
        show_info(self.root, "提示", "OCR测试功能开发中")

    def _show_task_statistics(self):
        """显示任务统计"""
        # 切换到统计标签页
        self.right_notebook.select(2)  # 统计标签页
        self._refresh_statistics()

    def _show_settings(self):
        """显示设置"""
        show_info(self.root, "提示", "设置功能开发中")

    def _show_help(self):
        """显示帮助"""
        help_text = """SnowZone OCR自动化工具 V2 使用说明

新功能特性：
• 三层任务架构：基础任务 → 任务流 → 工作流
• 可视化编辑器：拖拽式任务组合
• 实时监控：执行状态和日志显示
• 统计分析：性能和成功率统计

基本操作：
1. 创建基础任务：文件 → 新建任务
2. 组合任务流：拖拽任务到流程编辑器
3. 创建工作流：组合多个任务流
4. 执行监控：查看右侧监控面板

快捷键：
• Ctrl+N: 新建任务
• Ctrl+Shift+N: 新建任务流
• Ctrl+Alt+N: 新建工作流
• F5: 刷新界面
• F6: 执行任务流
• F7: 启动工作流"""

        messagebox.showinfo("帮助", help_text)

    def _show_about(self):
        """显示关于"""
        about_text = """SnowZone OCR自动化工具 V2

版本: 0.0002
开发: SnowZone Team

基于新的三层任务架构，提供更强大和灵活的自动化功能。

© 2025 SnowZone Project"""

        messagebox.showinfo("关于", about_text)

    # ==================== 窗口事件处理 ====================

    def _on_window_closing(self):
        """窗口关闭事件"""
        try:
            # 停止所有运行中的任务
            if self.task_integration:
                # 这里可以添加停止所有任务的逻辑
                pass

            self._add_log("INFO", "SnowZone OCR自动化工具 V2 已退出")

        except Exception as e:
            print(f"关闭程序时出错: {e}")
        finally:
            self.root.destroy()

    def run(self):
        """运行主窗口"""
        try:
            # 居中显示窗口
            center_window(self.root, 1400, 900)
            self.root.mainloop()
        except KeyboardInterrupt:
            self._on_window_closing()
        except Exception as e:
            self._add_log("ERROR", f"程序运行异常: {e}")
            show_error(self.root, "错误", f"程序运行异常: {e}")


def main():
    """主函数"""
    try:
        app = SnowZoneMainWindowV2()
        app.run()
    except Exception as e:
        print(f"启动GUI V2失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
