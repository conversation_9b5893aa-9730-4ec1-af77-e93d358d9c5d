# -*- coding: utf-8 -*-
"""
工作流管理界面组件
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional, Callable, Any

from ..utils.gui_helpers import GUIStyles, create_tooltip, show_error, ask_yes_no, show_info


class WorkflowWidget(ttk.Frame):
    """工作流管理控件"""
    
    def __init__(self, parent: tk.Widget, workflow_manager=None, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.workflow_manager = workflow_manager
        self.on_workflow_selected = None
        self.on_workflow_edited = None
        self.on_workflow_deleted = None
        self.on_workflow_executed = None
        
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        # 刷新工作流列表
        self.refresh_workflows()
    
    def _create_widgets(self):
        """创建控件"""
        # 工具栏
        self.toolbar = ttk.Frame(self)
        
        # 刷新按钮
        self.refresh_btn = ttk.Button(
            self.toolbar,
            text="刷新",
            command=self.refresh_workflows
        )
        create_tooltip(self.refresh_btn, "刷新工作流列表")
        
        # 新建按钮
        self.new_btn = ttk.Button(
            self.toolbar,
            text="新建工作流",
            command=self._on_new_workflow
        )
        create_tooltip(self.new_btn, "创建新工作流")
        
        # 编辑按钮
        self.edit_btn = ttk.Button(
            self.toolbar,
            text="编辑",
            command=self._on_edit_workflow,
            state=tk.DISABLED
        )
        create_tooltip(self.edit_btn, "编辑选中的工作流")
        
        # 删除按钮
        self.delete_btn = ttk.Button(
            self.toolbar,
            text="删除",
            command=self._on_delete_workflow,
            state=tk.DISABLED
        )
        create_tooltip(self.delete_btn, "删除选中的工作流")
        
        # 执行按钮
        self.execute_btn = ttk.Button(
            self.toolbar,
            text="执行工作流",
            command=self._on_execute_workflow,
            state=tk.DISABLED
        )
        create_tooltip(self.execute_btn, "执行选中的工作流")
        
        # 工作流列表
        self.tree_frame = ttk.Frame(self)
        
        # 创建Treeview
        columns = ('name', 'status', 'step_count', 'description')
        self.tree = ttk.Treeview(
            self.tree_frame,
            columns=columns,
            show='tree headings',
            selectmode='extended'
        )
        
        # 设置列标题
        self.tree.heading('#0', text='ID')
        self.tree.heading('name', text='工作流名称')
        self.tree.heading('status', text='状态')
        self.tree.heading('step_count', text='步骤数')
        self.tree.heading('description', text='描述')
        
        # 设置列宽
        self.tree.column('#0', width=80, minwidth=60)
        self.tree.column('name', width=150, minwidth=100)
        self.tree.column('status', width=60, minwidth=50)
        self.tree.column('step_count', width=80, minwidth=60)
        self.tree.column('description', width=200, minwidth=150)
        
        # 滚动条
        self.tree_scroll = ttk.Scrollbar(
            self.tree_frame,
            orient=tk.VERTICAL,
            command=self.tree.yview
        )
        self.tree.configure(yscrollcommand=self.tree_scroll.set)
        
        # 工作流详情面板
        self.details_frame = ttk.LabelFrame(self, text="工作流详情")
        
        # 步骤列表
        self.steps_frame = ttk.Frame(self.details_frame)
        
        ttk.Label(self.steps_frame, text="执行步骤:", 
                 font=GUIStyles.FONTS['subtitle']).pack(anchor=tk.W)
        
        # 步骤列表框
        self.steps_listbox = tk.Listbox(
            self.steps_frame,
            height=8,
            font=GUIStyles.FONTS['default']
        )
        
        # 步骤列表滚动条
        self.steps_scroll = ttk.Scrollbar(
            self.steps_frame,
            orient=tk.VERTICAL,
            command=self.steps_listbox.yview
        )
        self.steps_listbox.configure(yscrollcommand=self.steps_scroll.set)
        
        # 步骤操作按钮
        self.step_buttons_frame = ttk.Frame(self.steps_frame)
        
        self.move_up_btn = ttk.Button(
            self.step_buttons_frame,
            text="上移",
            command=self._move_step_up,
            state=tk.DISABLED
        )
        
        self.move_down_btn = ttk.Button(
            self.step_buttons_frame,
            text="下移",
            command=self._move_step_down,
            state=tk.DISABLED
        )
        
        self.remove_step_btn = ttk.Button(
            self.step_buttons_frame,
            text="移除",
            command=self._remove_step,
            state=tk.DISABLED
        )
        
        # 状态栏
        self.status_frame = ttk.Frame(self)
        self.status_label = ttk.Label(
            self.status_frame,
            text="工作流管理",
            font=GUIStyles.FONTS['small']
        )
    
    def _setup_layout(self):
        """设置布局"""
        # 工具栏布局
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        self.new_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        self.edit_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        self.delete_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        self.execute_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        
        # 主布局
        self.toolbar.pack(fill=tk.X, padx=GUIStyles.PADDING['medium'], 
                         pady=GUIStyles.PADDING['small'])
        
        # 工作流列表布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, 
                           padx=GUIStyles.PADDING['medium'])
        
        # 详情面板布局
        self.steps_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.steps_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 步骤操作按钮布局
        self.move_up_btn.pack(side=tk.TOP, pady=(0, GUIStyles.PADDING['small']))
        self.move_down_btn.pack(side=tk.TOP, pady=(0, GUIStyles.PADDING['small']))
        self.remove_step_btn.pack(side=tk.TOP)
        
        self.step_buttons_frame.pack(side=tk.RIGHT, fill=tk.Y, 
                                   padx=(GUIStyles.PADDING['small'], 0))
        self.steps_frame.pack(fill=tk.BOTH, expand=True, 
                            padx=GUIStyles.PADDING['small'],
                            pady=GUIStyles.PADDING['small'])
        
        self.details_frame.pack(fill=tk.BOTH, expand=True,
                              padx=GUIStyles.PADDING['medium'],
                              pady=GUIStyles.PADDING['small'])
        
        # 状态栏布局
        self.status_label.pack(side=tk.LEFT, padx=GUIStyles.PADDING['small'])
        self.status_frame.pack(fill=tk.X, padx=GUIStyles.PADDING['medium'],
                              pady=GUIStyles.PADDING['small'])
    
    def _bind_events(self):
        """绑定事件"""
        self.tree.bind('<<TreeviewSelect>>', self._on_selection_changed)
        self.tree.bind('<Double-1>', self._on_double_click)
        self.tree.bind('<Button-3>', self._on_right_click)
        self.steps_listbox.bind('<<ListboxSelect>>', self._on_step_selected)
    
    def _on_selection_changed(self, event):
        """选择改变事件"""
        selected = self.tree.selection()
        has_selection = len(selected) > 0
        
        # 更新按钮状态
        state = tk.NORMAL if has_selection else tk.DISABLED
        self.edit_btn.config(state=state)
        self.delete_btn.config(state=state)
        self.execute_btn.config(state=state)
        
        # 更新详情面板
        if has_selection:
            workflow_id = selected[0]
            self._update_workflow_details(workflow_id)
            
            # 触发回调
            if self.on_workflow_selected:
                self.on_workflow_selected(workflow_id)
        else:
            self._clear_workflow_details()
    
    def _on_step_selected(self, event):
        """步骤选择事件"""
        selected = self.steps_listbox.curselection()
        has_selection = len(selected) > 0
        
        # 更新步骤操作按钮状态
        state = tk.NORMAL if has_selection else tk.DISABLED
        self.move_up_btn.config(state=state)
        self.move_down_btn.config(state=state)
        self.remove_step_btn.config(state=state)
    
    def _on_double_click(self, event):
        """双击事件"""
        self._on_edit_workflow()
    
    def _on_right_click(self, event):
        """右键点击事件"""
        # 选择点击的项目
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self._show_context_menu(event)
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        context_menu = tk.Menu(self, tearoff=0)
        
        selected = self.tree.selection()
        if selected:
            workflow_id = selected[0]
            workflow = self._get_workflow(workflow_id)
            
            if workflow:
                # 启用/禁用
                status_text = "禁用" if workflow.enabled else "启用"
                context_menu.add_command(
                    label=status_text,
                    command=self._on_toggle_workflow
                )
                
                context_menu.add_separator()
                
                # 编辑
                context_menu.add_command(
                    label="编辑",
                    command=self._on_edit_workflow
                )
                
                # 复制
                context_menu.add_command(
                    label="复制",
                    command=lambda: self._on_copy_workflow(workflow_id)
                )
                
                # 执行
                context_menu.add_command(
                    label="执行",
                    command=self._on_execute_workflow
                )
                
                context_menu.add_separator()
                
                # 删除
                context_menu.add_command(
                    label="删除",
                    command=self._on_delete_workflow
                )
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def _on_new_workflow(self):
        """新建工作流"""
        if self.on_workflow_edited:
            self.on_workflow_edited(None)  # None表示新建
    
    def _on_edit_workflow(self):
        """编辑工作流"""
        selected = self.tree.selection()
        if selected and self.on_workflow_edited:
            workflow_id = selected[0]
            self.on_workflow_edited(workflow_id)
    
    def _on_delete_workflow(self):
        """删除工作流"""
        selected = self.tree.selection()
        if not selected:
            return
        
        workflow_id = selected[0]
        workflow = self._get_workflow(workflow_id)
        
        if workflow:
            if ask_yes_no(
                self,
                "确认删除",
                f"确定要删除工作流 '{workflow.name}' 吗？\n此操作不可撤销。"
            ):
                if self.on_workflow_deleted:
                    self.on_workflow_deleted(workflow_id)
                self.refresh_workflows()
    
    def _on_execute_workflow(self):
        """执行工作流"""
        selected = self.tree.selection()
        if selected and self.on_workflow_executed:
            workflow_id = selected[0]
            self.on_workflow_executed(workflow_id)
    
    def _on_toggle_workflow(self):
        """切换工作流状态"""
        selected = self.tree.selection()
        if not selected or not self.workflow_manager:
            return
        
        workflow_id = selected[0]
        workflow = self.workflow_manager.get_workflow(workflow_id)
        
        if workflow:
            workflow.enabled = not workflow.enabled
            if self.workflow_manager.save_workflow(workflow):
                self.refresh_workflows()
    
    def _on_copy_workflow(self, workflow_id: str):
        """复制工作流"""
        if self.workflow_manager:
            workflow = self.workflow_manager.get_workflow(workflow_id)
            if workflow:
                # 创建副本
                new_workflow = self.workflow_manager.create_workflow(
                    f"{workflow.name} - 副本",
                    workflow.description
                )
                new_workflow.steps = workflow.steps.copy()
                
                if self.workflow_manager.save_workflow(new_workflow):
                    self.refresh_workflows()
    
    def _move_step_up(self):
        """上移步骤"""
        selected = self.steps_listbox.curselection()
        if not selected or selected[0] == 0:
            return
        
        workflow_id = self.tree.selection()[0]
        step_index = selected[0]
        
        if self.workflow_manager:
            workflow = self.workflow_manager.get_workflow(workflow_id)
            if workflow and step_index > 0:
                # 交换步骤
                workflow.steps[step_index], workflow.steps[step_index - 1] = \
                    workflow.steps[step_index - 1], workflow.steps[step_index]
                
                # 更新顺序
                for i, step in enumerate(workflow.steps):
                    step.order = i
                
                if self.workflow_manager.save_workflow(workflow):
                    self._update_workflow_details(workflow_id)
                    self.steps_listbox.selection_set(step_index - 1)
    
    def _move_step_down(self):
        """下移步骤"""
        selected = self.steps_listbox.curselection()
        if not selected:
            return
        
        workflow_id = self.tree.selection()[0]
        step_index = selected[0]
        
        if self.workflow_manager:
            workflow = self.workflow_manager.get_workflow(workflow_id)
            if workflow and step_index < len(workflow.steps) - 1:
                # 交换步骤
                workflow.steps[step_index], workflow.steps[step_index + 1] = \
                    workflow.steps[step_index + 1], workflow.steps[step_index]
                
                # 更新顺序
                for i, step in enumerate(workflow.steps):
                    step.order = i
                
                if self.workflow_manager.save_workflow(workflow):
                    self._update_workflow_details(workflow_id)
                    self.steps_listbox.selection_set(step_index + 1)
    
    def _remove_step(self):
        """移除步骤"""
        selected = self.steps_listbox.curselection()
        if not selected:
            return
        
        workflow_id = self.tree.selection()[0]
        step_index = selected[0]
        
        if self.workflow_manager:
            workflow = self.workflow_manager.get_workflow(workflow_id)
            if workflow and 0 <= step_index < len(workflow.steps):
                step = workflow.steps[step_index]
                
                if ask_yes_no(
                    self,
                    "确认移除",
                    f"确定要移除步骤 '{step.task_name}' 吗？"
                ):
                    workflow.steps.pop(step_index)
                    
                    # 重新排序
                    for i, step in enumerate(workflow.steps):
                        step.order = i
                    
                    if self.workflow_manager.save_workflow(workflow):
                        self._update_workflow_details(workflow_id)
    
    def _get_workflow(self, workflow_id: str):
        """获取工作流"""
        if self.workflow_manager:
            return self.workflow_manager.get_workflow(workflow_id)
        return None
    
    def _update_workflow_details(self, workflow_id: str):
        """更新工作流详情"""
        workflow = self._get_workflow(workflow_id)
        if not workflow:
            self._clear_workflow_details()
            return
        
        # 清空步骤列表
        self.steps_listbox.delete(0, tk.END)
        
        # 添加步骤
        for i, step in enumerate(workflow.steps):
            status = "✓" if step.enabled else "✗"
            step_text = f"{i+1}. {status} {step.task_name}"
            self.steps_listbox.insert(tk.END, step_text)
    
    def _clear_workflow_details(self):
        """清空工作流详情"""
        self.steps_listbox.delete(0, tk.END)
    
    def refresh_workflows(self):
        """刷新工作流列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.workflow_manager:
            self.status_label.config(text="未连接工作流管理器")
            return
        
        try:
            # 获取所有工作流
            workflows = self.workflow_manager.get_all_workflows()
            
            # 添加工作流到列表
            for workflow_id, workflow in workflows.items():
                status = "启用" if workflow.enabled else "禁用"
                
                self.tree.insert(
                    '',
                    'end',
                    iid=workflow_id,
                    text=workflow_id,
                    values=(
                        workflow.name,
                        status,
                        len(workflow.steps),
                        workflow.description[:50] + "..." if len(workflow.description) > 50 else workflow.description
                    ),
                    tags=('enabled' if workflow.enabled else 'disabled',)
                )
            
            # 配置标签样式
            self.tree.tag_configure('enabled', foreground='black')
            self.tree.tag_configure('disabled', foreground='gray')
            
            # 更新状态
            self.status_label.config(text=f"共 {len(workflows)} 个工作流")
            
        except Exception as e:
            show_error(self, "错误", f"刷新工作流列表失败: {e}")
            self.status_label.config(text="刷新失败")
    
    def get_selected_workflow_id(self) -> Optional[str]:
        """获取选中的工作流ID"""
        selected = self.tree.selection()
        return selected[0] if selected else None
    
    def select_workflow(self, workflow_id: str):
        """选择指定工作流"""
        if workflow_id in [item for item in self.tree.get_children()]:
            self.tree.selection_set(workflow_id)
            self.tree.focus(workflow_id)
            self.tree.see(workflow_id)
    
    def set_workflow_manager(self, workflow_manager):
        """设置工作流管理器"""
        self.workflow_manager = workflow_manager
        self.refresh_workflows()
    
    def set_callbacks(self, on_workflow_selected: Optional[Callable] = None,
                     on_workflow_edited: Optional[Callable] = None,
                     on_workflow_deleted: Optional[Callable] = None,
                     on_workflow_executed: Optional[Callable] = None):
        """设置回调函数"""
        self.on_workflow_selected = on_workflow_selected
        self.on_workflow_edited = on_workflow_edited
        self.on_workflow_deleted = on_workflow_deleted
        self.on_workflow_executed = on_workflow_executed
    
    def add_task_to_workflow(self, task_id: str):
        """添加任务到当前选中的工作流"""
        selected = self.tree.selection()
        if not selected or not self.workflow_manager:
            show_info(self, "提示", "请先选择一个工作流")
            return
        
        workflow_id = selected[0]
        
        if self.workflow_manager.add_step(workflow_id, task_id):
            self._update_workflow_details(workflow_id)
            self.refresh_workflows()
            show_info(self, "成功", "任务已添加到工作流")
        else:
            show_error(self, "错误", "添加任务到工作流失败")
