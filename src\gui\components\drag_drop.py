# -*- coding: utf-8 -*-
"""
拖拽功能组件
提供任务和组件的拖拽操作支持
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


@dataclass
class DragData:
    """拖拽数据"""
    source_widget: tk.Widget
    data_type: str  # task, flow, workflow, component
    data: Dict[str, Any]
    position: tuple[int, int]


class DragDropMixin:
    """拖拽功能混入类"""
    
    def __init__(self):
        """初始化拖拽功能"""
        self.drag_data: Optional[DragData] = None
        self.drag_callbacks: Dict[str, List[Callable]] = {
            'drag_start': [],
            'drag_motion': [],
            'drag_end': [],
            'drop': []
        }
        self.drop_targets: List[tk.Widget] = []
        self.drag_threshold = 5  # 拖拽阈值（像素）
        
        # 拖拽状态
        self.is_dragging = False
        self.drag_start_pos = None
        self.drag_overlay = None
    
    def enable_drag(self, widget: tk.Widget, data_type: str, data: Dict[str, Any]):
        """
        启用组件的拖拽功能
        
        Args:
            widget: 要启用拖拽的组件
            data_type: 数据类型
            data: 拖拽数据
        """
        widget.bind('<Button-1>', lambda e: self._on_drag_start(e, widget, data_type, data))
        widget.bind('<B1-Motion>', lambda e: self._on_drag_motion(e, widget))
        widget.bind('<ButtonRelease-1>', lambda e: self._on_drag_end(e, widget))
    
    def enable_drop(self, widget: tk.Widget, accepted_types: List[str] = None):
        """
        启用组件的放置功能
        
        Args:
            widget: 要启用放置的组件
            accepted_types: 接受的数据类型列表
        """
        if widget not in self.drop_targets:
            self.drop_targets.append(widget)
        
        # 存储接受的类型
        if not hasattr(widget, '_accepted_drop_types'):
            widget._accepted_drop_types = accepted_types or []
        
        # 绑定放置事件
        widget.bind('<Enter>', lambda e: self._on_drop_enter(e, widget))
        widget.bind('<Leave>', lambda e: self._on_drop_leave(e, widget))
    
    def add_drag_callback(self, event_type: str, callback: Callable):
        """
        添加拖拽回调函数
        
        Args:
            event_type: 事件类型 (drag_start, drag_motion, drag_end, drop)
            callback: 回调函数
        """
        if event_type in self.drag_callbacks:
            self.drag_callbacks[event_type].append(callback)
    
    def _on_drag_start(self, event, widget: tk.Widget, data_type: str, data: Dict[str, Any]):
        """拖拽开始事件"""
        self.drag_start_pos = (event.x_root, event.y_root)
        self.drag_data = DragData(
            source_widget=widget,
            data_type=data_type,
            data=data,
            position=(event.x_root, event.y_root)
        )
        
        # 调用回调函数
        for callback in self.drag_callbacks['drag_start']:
            try:
                callback(self.drag_data)
            except Exception as e:
                print(f"拖拽开始回调错误: {e}")
    
    def _on_drag_motion(self, event, widget: tk.Widget):
        """拖拽移动事件"""
        if not self.drag_data or not self.drag_start_pos:
            return
        
        # 检查是否超过拖拽阈值
        dx = abs(event.x_root - self.drag_start_pos[0])
        dy = abs(event.y_root - self.drag_start_pos[1])
        
        if not self.is_dragging and (dx > self.drag_threshold or dy > self.drag_threshold):
            self.is_dragging = True
            self._create_drag_overlay(event.x_root, event.y_root)
        
        if self.is_dragging:
            # 更新拖拽位置
            self.drag_data.position = (event.x_root, event.y_root)
            
            # 更新拖拽覆盖层
            if self.drag_overlay:
                self.drag_overlay.geometry(f"+{event.x_root + 10}+{event.y_root + 10}")
            
            # 检查放置目标
            self._check_drop_targets(event.x_root, event.y_root)
            
            # 调用回调函数
            for callback in self.drag_callbacks['drag_motion']:
                try:
                    callback(self.drag_data)
                except Exception as e:
                    print(f"拖拽移动回调错误: {e}")
    
    def _on_drag_end(self, event, widget: tk.Widget):
        """拖拽结束事件"""
        if not self.drag_data:
            return
        
        try:
            # 检查是否在有效的放置目标上
            drop_target = self._find_drop_target(event.x_root, event.y_root)
            
            if drop_target and self.is_dragging:
                # 检查数据类型是否被接受
                accepted_types = getattr(drop_target, '_accepted_drop_types', [])
                if not accepted_types or self.drag_data.data_type in accepted_types:
                    # 执行放置操作
                    self._perform_drop(drop_target, self.drag_data)
            
            # 调用拖拽结束回调
            for callback in self.drag_callbacks['drag_end']:
                try:
                    callback(self.drag_data)
                except Exception as e:
                    print(f"拖拽结束回调错误: {e}")
        
        finally:
            # 清理拖拽状态
            self._cleanup_drag()
    
    def _create_drag_overlay(self, x: int, y: int):
        """创建拖拽覆盖层"""
        if self.drag_overlay:
            self.drag_overlay.destroy()
        
        self.drag_overlay = tk.Toplevel()
        self.drag_overlay.wm_overrideredirect(True)
        self.drag_overlay.wm_attributes('-topmost', True)
        self.drag_overlay.wm_attributes('-alpha', 0.8)
        
        # 创建拖拽内容
        frame = ttk.Frame(self.drag_overlay, relief=tk.RAISED, borderwidth=2)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 显示拖拽的内容
        if self.drag_data:
            data_type = self.drag_data.data_type
            data = self.drag_data.data
            
            if data_type == "task":
                text = f"📋 {data.get('name', '任务')}"
            elif data_type == "flow":
                text = f"🔄 {data.get('name', '任务流')}"
            elif data_type == "workflow":
                text = f"⚙️ {data.get('name', '工作流')}"
            else:
                text = f"📦 {data_type}"
            
            ttk.Label(frame, text=text, padding=5).pack()
        
        self.drag_overlay.geometry(f"+{x + 10}+{y + 10}")
    
    def _check_drop_targets(self, x: int, y: int):
        """检查放置目标"""
        for target in self.drop_targets:
            try:
                # 获取目标组件的屏幕坐标
                target_x = target.winfo_rootx()
                target_y = target.winfo_rooty()
                target_width = target.winfo_width()
                target_height = target.winfo_height()
                
                # 检查鼠标是否在目标区域内
                if (target_x <= x <= target_x + target_width and
                    target_y <= y <= target_y + target_height):
                    
                    # 高亮显示放置目标
                    self._highlight_drop_target(target, True)
                else:
                    # 取消高亮
                    self._highlight_drop_target(target, False)
                    
            except tk.TclError:
                # 组件可能已被销毁
                continue
    
    def _find_drop_target(self, x: int, y: int) -> Optional[tk.Widget]:
        """查找放置目标"""
        for target in self.drop_targets:
            try:
                target_x = target.winfo_rootx()
                target_y = target.winfo_rooty()
                target_width = target.winfo_width()
                target_height = target.winfo_height()
                
                if (target_x <= x <= target_x + target_width and
                    target_y <= y <= target_y + target_height):
                    return target
                    
            except tk.TclError:
                continue
        
        return None
    
    def _highlight_drop_target(self, target: tk.Widget, highlight: bool):
        """高亮显示放置目标"""
        try:
            if highlight:
                # 保存原始样式
                if not hasattr(target, '_original_bg'):
                    target._original_bg = target.cget('bg') if hasattr(target, 'cget') else None
                
                # 设置高亮样式
                if hasattr(target, 'configure'):
                    target.configure(bg='lightblue')
            else:
                # 恢复原始样式
                if hasattr(target, '_original_bg') and target._original_bg:
                    if hasattr(target, 'configure'):
                        target.configure(bg=target._original_bg)
                    delattr(target, '_original_bg')
                    
        except (tk.TclError, AttributeError):
            pass
    
    def _perform_drop(self, target: tk.Widget, drag_data: DragData):
        """执行放置操作"""
        # 调用放置回调函数
        for callback in self.drag_callbacks['drop']:
            try:
                callback(target, drag_data)
            except Exception as e:
                print(f"放置回调错误: {e}")
        
        # 取消目标高亮
        self._highlight_drop_target(target, False)
    
    def _on_drop_enter(self, event, widget: tk.Widget):
        """鼠标进入放置目标"""
        if self.is_dragging and self.drag_data:
            accepted_types = getattr(widget, '_accepted_drop_types', [])
            if not accepted_types or self.drag_data.data_type in accepted_types:
                self._highlight_drop_target(widget, True)
    
    def _on_drop_leave(self, event, widget: tk.Widget):
        """鼠标离开放置目标"""
        if self.is_dragging:
            self._highlight_drop_target(widget, False)
    
    def _cleanup_drag(self):
        """清理拖拽状态"""
        self.is_dragging = False
        self.drag_start_pos = None
        self.drag_data = None
        
        # 销毁拖拽覆盖层
        if self.drag_overlay:
            self.drag_overlay.destroy()
            self.drag_overlay = None
        
        # 清理所有目标的高亮
        for target in self.drop_targets:
            self._highlight_drop_target(target, False)


class DragDropTaskList(ttk.Treeview, DragDropMixin):
    """支持拖拽的任务列表"""
    
    def __init__(self, parent, **kwargs):
        """初始化任务列表"""
        ttk.Treeview.__init__(self, parent, **kwargs)
        DragDropMixin.__init__(self)
        
        self.tasks_data: Dict[str, Dict[str, Any]] = {}
        
        # 配置列
        self['columns'] = ('type', 'category', 'status')
        self.heading('#0', text='任务名称')
        self.heading('type', text='类型')
        self.heading('category', text='分类')
        self.heading('status', text='状态')
        
        self.column('#0', width=200)
        self.column('type', width=80)
        self.column('category', width=80)
        self.column('status', width=60)
        
        # 绑定选择事件
        self.bind('<<TreeviewSelect>>', self._on_select)
    
    def add_task(self, task_data: Dict[str, Any]):
        """添加任务到列表"""
        task_id = task_data.get('id', '')
        task_name = task_data.get('name', '未命名任务')
        task_type = task_data.get('type', 'basic')
        category = task_data.get('category', 'basic')
        enabled = task_data.get('enabled', True)
        
        status = '✅' if enabled else '❌'
        
        # 插入到树形视图
        item_id = self.insert('', 'end', iid=task_id, text=task_name,
                             values=(task_type, category, status))
        
        # 存储任务数据
        self.tasks_data[task_id] = task_data
        
        # 启用拖拽
        self.enable_drag_for_item(item_id, task_data)
    
    def enable_drag_for_item(self, item_id: str, task_data: Dict[str, Any]):
        """为特定项目启用拖拽"""
        # 由于Treeview的特殊性，我们需要重写拖拽处理
        pass
    
    def _on_select(self, event):
        """选择事件处理"""
        selection = self.selection()
        if selection:
            item_id = selection[0]
            if item_id in self.tasks_data:
                task_data = self.tasks_data[item_id]
                # 为选中的项目启用拖拽
                self.enable_drag(self, 'task', task_data)
    
    def get_selected_task(self) -> Optional[Dict[str, Any]]:
        """获取选中的任务"""
        selection = self.selection()
        if selection:
            item_id = selection[0]
            return self.tasks_data.get(item_id)
        return None
    
    def remove_task(self, task_id: str):
        """移除任务"""
        if task_id in self.tasks_data:
            self.delete(task_id)
            del self.tasks_data[task_id]
    
    def update_task(self, task_id: str, task_data: Dict[str, Any]):
        """更新任务"""
        if task_id in self.tasks_data:
            self.tasks_data[task_id] = task_data
            
            # 更新显示
            task_name = task_data.get('name', '未命名任务')
            task_type = task_data.get('type', 'basic')
            category = task_data.get('category', 'basic')
            enabled = task_data.get('enabled', True)
            status = '✅' if enabled else '❌'
            
            self.item(task_id, text=task_name, values=(task_type, category, status))


class DragDropCanvas(tk.Canvas, DragDropMixin):
    """支持拖拽的画布"""
    
    def __init__(self, parent, **kwargs):
        """初始化画布"""
        tk.Canvas.__init__(self, parent, **kwargs)
        DragDropMixin.__init__(self)
        
        # 启用放置功能
        self.enable_drop(self, ['task', 'flow', 'workflow'])
        
        # 添加放置回调
        self.add_drag_callback('drop', self._on_canvas_drop)
    
    def _on_canvas_drop(self, target: tk.Widget, drag_data: DragData):
        """画布放置事件处理"""
        if target == self:
            # 在画布上创建表示拖拽项目的图形
            x, y = self.canvasx(drag_data.position[0] - self.winfo_rootx()), \
                   self.canvasy(drag_data.position[1] - self.winfo_rooty())
            
            data_type = drag_data.data_type
            data = drag_data.data
            
            if data_type == "task":
                self._create_task_node(x, y, data)
            elif data_type == "flow":
                self._create_flow_node(x, y, data)
            elif data_type == "workflow":
                self._create_workflow_node(x, y, data)
    
    def _create_task_node(self, x: float, y: float, task_data: Dict[str, Any]):
        """在画布上创建任务节点"""
        task_name = task_data.get('name', '任务')
        
        # 创建矩形
        rect = self.create_rectangle(x-50, y-20, x+50, y+20, 
                                   fill='lightblue', outline='blue', width=2)
        
        # 创建文本
        text = self.create_text(x, y, text=task_name, font=('Arial', 10))
        
        # 绑定事件
        self.tag_bind(rect, '<Button-1>', lambda e: self._on_node_click(e, task_data))
        self.tag_bind(text, '<Button-1>', lambda e: self._on_node_click(e, task_data))
    
    def _create_flow_node(self, x: float, y: float, flow_data: Dict[str, Any]):
        """在画布上创建任务流节点"""
        flow_name = flow_data.get('name', '任务流')
        
        # 创建圆角矩形（用多边形模拟）
        points = [x-60, y-25, x+60, y-25, x+60, y+25, x-60, y+25]
        rect = self.create_polygon(points, fill='lightgreen', outline='green', width=2)
        
        # 创建文本
        text = self.create_text(x, y, text=flow_name, font=('Arial', 10))
        
        # 绑定事件
        self.tag_bind(rect, '<Button-1>', lambda e: self._on_node_click(e, flow_data))
        self.tag_bind(text, '<Button-1>', lambda e: self._on_node_click(e, flow_data))
    
    def _create_workflow_node(self, x: float, y: float, workflow_data: Dict[str, Any]):
        """在画布上创建工作流节点"""
        workflow_name = workflow_data.get('name', '工作流')
        
        # 创建菱形
        points = [x, y-30, x+70, y, x, y+30, x-70, y]
        diamond = self.create_polygon(points, fill='lightyellow', outline='orange', width=2)
        
        # 创建文本
        text = self.create_text(x, y, text=workflow_name, font=('Arial', 10))
        
        # 绑定事件
        self.tag_bind(diamond, '<Button-1>', lambda e: self._on_node_click(e, workflow_data))
        self.tag_bind(text, '<Button-1>', lambda e: self._on_node_click(e, workflow_data))
    
    def _on_node_click(self, event, data: Dict[str, Any]):
        """节点点击事件"""
        print(f"点击了节点: {data.get('name', '未知')}")
        # 这里可以添加节点编辑功能
