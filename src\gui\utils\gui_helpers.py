# -*- coding: utf-8 -*-
"""
GUI辅助工具函数
提供GUI开发中常用的工具函数和样式定义
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, Optional, Callable
import threading
import queue


class GUIStyles:
    """GUI样式定义"""
    
    # 颜色定义
    COLORS = {
        'primary': '#2196F3',
        'secondary': '#FFC107',
        'success': '#4CAF50',
        'danger': '#F44336',
        'warning': '#FF9800',
        'info': '#00BCD4',
        'light': '#F5F5F5',
        'dark': '#212121',
        'white': '#FFFFFF',
        'gray': '#9E9E9E'
    }
    
    # 字体定义
    FONTS = {
        'default': ('Microsoft YaHei', 9),
        'title': ('Microsoft YaHei', 12, 'bold'),
        'subtitle': ('Microsoft YaHei', 10, 'bold'),
        'small': ('Microsoft YaHei', 8),
        'code': ('Consolas', 9)
    }
    
    # 间距定义
    PADDING = {
        'small': 5,
        'medium': 10,
        'large': 15,
        'xlarge': 20
    }


class ThreadSafeGUI:
    """线程安全的GUI操作类"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.queue = queue.Queue()
        self._check_queue()
    
    def _check_queue(self):
        """检查队列中的GUI操作"""
        try:
            while True:
                func, args, kwargs = self.queue.get_nowait()
                func(*args, **kwargs)
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self._check_queue)
    
    def call_in_main_thread(self, func: Callable, *args, **kwargs):
        """在主线程中执行GUI操作"""
        self.queue.put((func, args, kwargs))


def center_window(window: tk.Toplevel, width: int, height: int):
    """将窗口居中显示"""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    window.geometry(f"{width}x{height}+{x}+{y}")


def create_tooltip(widget: tk.Widget, text: str):
    """为控件创建工具提示"""
    def on_enter(event):
        tooltip = tk.Toplevel()
        tooltip.wm_overrideredirect(True)
        tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
        
        label = tk.Label(
            tooltip,
            text=text,
            background='#FFFFE0',
            relief='solid',
            borderwidth=1,
            font=GUIStyles.FONTS['small']
        )
        label.pack()
        
        widget.tooltip = tooltip
    
    def on_leave(event):
        if hasattr(widget, 'tooltip'):
            widget.tooltip.destroy()
            del widget.tooltip
    
    widget.bind('<Enter>', on_enter)
    widget.bind('<Leave>', on_leave)


def show_error(parent: tk.Widget, title: str, message: str):
    """显示错误对话框"""
    messagebox.showerror(title, message, parent=parent)


def show_warning(parent: tk.Widget, title: str, message: str):
    """显示警告对话框"""
    messagebox.showwarning(title, message, parent=parent)


def show_info(parent: tk.Widget, title: str, message: str):
    """显示信息对话框"""
    messagebox.showinfo(title, message, parent=parent)


def ask_yes_no(parent: tk.Widget, title: str, message: str) -> bool:
    """显示是否确认对话框"""
    return messagebox.askyesno(title, message, parent=parent)


def ask_file_open(parent: tk.Widget, title: str, filetypes: list) -> Optional[str]:
    """显示文件打开对话框"""
    return filedialog.askopenfilename(
        parent=parent,
        title=title,
        filetypes=filetypes
    )


def ask_file_save(parent: tk.Widget, title: str, filetypes: list, defaultextension: str = '') -> Optional[str]:
    """显示文件保存对话框"""
    return filedialog.asksaveasfilename(
        parent=parent,
        title=title,
        filetypes=filetypes,
        defaultextension=defaultextension
    )


def create_scrollable_frame(parent: tk.Widget) -> tuple:
    """创建可滚动的框架"""
    # 创建Canvas和Scrollbar
    canvas = tk.Canvas(parent)
    scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)
    
    # 配置滚动
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # 布局
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    return canvas, scrollable_frame, scrollbar


def bind_mousewheel(canvas: tk.Canvas):
    """为Canvas绑定鼠标滚轮事件"""
    def on_mousewheel(event):
        canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    canvas.bind("<MouseWheel>", on_mousewheel)


class StatusBar(ttk.Frame):
    """状态栏组件"""
    
    def __init__(self, parent: tk.Widget):
        super().__init__(parent)
        
        # 状态标签
        self.status_label = ttk.Label(
            self,
            text="就绪",
            font=GUIStyles.FONTS['small']
        )
        self.status_label.pack(side=tk.LEFT, padx=GUIStyles.PADDING['small'])
        
        # 分隔符
        ttk.Separator(self, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=GUIStyles.PADDING['small']
        )
        
        # 版本信息
        self.version_label = ttk.Label(
            self,
            text="版本: 0_0001",
            font=GUIStyles.FONTS['small']
        )
        self.version_label.pack(side=tk.RIGHT, padx=GUIStyles.PADDING['small'])
    
    def set_status(self, text: str):
        """设置状态文本"""
        self.status_label.config(text=text)
    
    def set_version(self, version: str):
        """设置版本信息"""
        self.version_label.config(text=f"版本: {version}")


class ProgressDialog:
    """进度对话框"""
    
    def __init__(self, parent: tk.Widget, title: str, message: str):
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        center_window(self.dialog, 300, 120)
        
        # 消息标签
        self.message_label = ttk.Label(
            self.dialog,
            text=message,
            font=GUIStyles.FONTS['default']
        )
        self.message_label.pack(pady=GUIStyles.PADDING['medium'])
        
        # 进度条
        self.progress = ttk.Progressbar(
            self.dialog,
            mode='indeterminate'
        )
        self.progress.pack(
            padx=GUIStyles.PADDING['medium'],
            pady=GUIStyles.PADDING['small'],
            fill=tk.X
        )
        
        # 取消按钮
        self.cancel_button = ttk.Button(
            self.dialog,
            text="取消",
            command=self.cancel
        )
        self.cancel_button.pack(pady=GUIStyles.PADDING['small'])
        
        self.cancelled = False
        self.progress.start()
    
    def update_message(self, message: str):
        """更新消息"""
        self.message_label.config(text=message)
    
    def cancel(self):
        """取消操作"""
        self.cancelled = True
        self.close()
    
    def close(self):
        """关闭对话框"""
        self.progress.stop()
        self.dialog.destroy()
    
    def is_cancelled(self) -> bool:
        """检查是否被取消"""
        return self.cancelled


def run_in_thread(func: Callable, callback: Optional[Callable] = None, 
                 error_callback: Optional[Callable] = None):
    """在后台线程中运行函数"""
    def worker():
        try:
            result = func()
            if callback:
                callback(result)
        except Exception as e:
            if error_callback:
                error_callback(e)
            else:
                print(f"线程执行错误: {e}")
    
    thread = threading.Thread(target=worker, daemon=True)
    thread.start()
    return thread


def validate_json_config(config: Dict[str, Any]) -> tuple:
    """验证JSON配置格式"""
    errors = []
    
    # 检查必需字段
    required_fields = ['task_info', 'triggers', 'actions']
    for field in required_fields:
        if field not in config:
            errors.append(f"缺少必需字段: {field}")
    
    # 检查任务信息
    if 'task_info' in config:
        task_info = config['task_info']
        if 'name' not in task_info:
            errors.append("任务信息中缺少名称")
    
    # 检查触发器
    if 'triggers' in config:
        triggers = config['triggers']
        if not isinstance(triggers, list) or len(triggers) == 0:
            errors.append("触发器配置无效")
    
    # 检查动作
    if 'actions' in config:
        actions = config['actions']
        if not isinstance(actions, list) or len(actions) == 0:
            errors.append("动作配置无效")
    
    return len(errors) == 0, errors


class LogTextWidget(tk.Text):
    """日志文本控件"""
    
    def __init__(self, parent: tk.Widget, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 配置标签样式
        self.tag_config('INFO', foreground='black')
        self.tag_config('WARNING', foreground='orange')
        self.tag_config('ERROR', foreground='red')
        self.tag_config('DEBUG', foreground='gray')
        
        # 设置为只读
        self.config(state=tk.DISABLED)
    
    def append_log(self, level: str, message: str):
        """添加日志消息"""
        self.config(state=tk.NORMAL)
        
        # 添加时间戳
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # 插入日志
        self.insert(tk.END, f"[{timestamp}] ", 'INFO')
        self.insert(tk.END, f"{level}: ", level)
        self.insert(tk.END, f"{message}\n", 'INFO')
        
        # 自动滚动到底部
        self.see(tk.END)
        
        # 限制行数
        lines = int(self.index(tk.END).split('.')[0])
        if lines > 1000:
            self.delete('1.0', '100.0')
        
        self.config(state=tk.DISABLED)
    
    def clear_log(self):
        """清空日志"""
        self.config(state=tk.NORMAL)
        self.delete('1.0', tk.END)
        self.config(state=tk.DISABLED)
