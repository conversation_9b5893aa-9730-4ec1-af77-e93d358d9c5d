{"workflow_id": "daily_workflow_001", "name": "每日例行工作流", "description": "包含所有每日任务的完整工作流", "enabled": true, "continue_on_error": true, "steps": [{"id": "workflow_step_001", "flow_id": "daily_task_flow_001", "flow_name": "每日任务流程", "order": 1, "enabled": true, "delay_before": 0.0, "delay_after": 2.0, "continue_on_error": true, "parameters": {}}], "schedule": {"schedule_type": "interval", "enabled": true, "start_time": "2025-07-11T00:00:00", "end_time": null, "interval_seconds": 86400, "max_executions": null, "cron_expression": null}, "conditions": {"time_range": {"start": "08:00", "end": "22:00"}, "max_executions_per_day": 3, "cooldown": 3600}, "metadata": {"version": "0_0002", "created_time": "2025-07-11T17:01:00", "category": "daily", "tags": ["daily", "workflow", "automation"], "priority": 1}}