# -*- coding: utf-8 -*-
"""
错误处理机制
提供完整的错误捕获、恢复、重试、回滚功能
"""

import traceback
import time
import json
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import logging


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"           # 轻微错误，可以继续执行
    MEDIUM = "medium"     # 中等错误，需要重试
    HIGH = "high"         # 严重错误，需要停止当前任务
    CRITICAL = "critical" # 致命错误，需要停止整个流程


class ErrorCategory(Enum):
    """错误分类"""
    SYSTEM = "system"           # 系统错误
    NETWORK = "network"         # 网络错误
    TIMEOUT = "timeout"         # 超时错误
    VALIDATION = "validation"   # 验证错误
    PERMISSION = "permission"   # 权限错误
    RESOURCE = "resource"       # 资源错误
    LOGIC = "logic"            # 逻辑错误
    USER = "user"              # 用户错误


class RecoveryAction(Enum):
    """恢复动作"""
    RETRY = "retry"             # 重试
    SKIP = "skip"               # 跳过
    ROLLBACK = "rollback"       # 回滚
    STOP = "stop"               # 停止
    CONTINUE = "continue"       # 继续
    MANUAL = "manual"           # 手动处理


@dataclass
class ErrorInfo:
    """错误信息"""
    error_id: str
    timestamp: datetime
    error_type: str
    error_message: str
    error_details: str
    severity: ErrorSeverity
    category: ErrorCategory
    context: Dict[str, Any]
    stack_trace: str
    recovery_action: Optional[RecoveryAction] = None
    retry_count: int = 0
    max_retries: int = 3
    resolved: bool = False


@dataclass
class RecoveryStrategy:
    """恢复策略"""
    error_pattern: str
    category: ErrorCategory
    severity: ErrorSeverity
    action: RecoveryAction
    max_retries: int = 3
    retry_delay: float = 1.0
    condition: Optional[Callable[[ErrorInfo], bool]] = None
    handler: Optional[Callable[[ErrorInfo], bool]] = None


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        """初始化错误处理器"""
        self.logger = logging.getLogger(__name__)
        self.error_history: List[ErrorInfo] = []
        self.recovery_strategies: List[RecoveryStrategy] = []
        self.error_callbacks: List[Callable[[ErrorInfo], None]] = []
        
        # 注册默认恢复策略
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """注册默认恢复策略"""
        # 网络超时错误 - 重试
        self.register_strategy(RecoveryStrategy(
            error_pattern="timeout",
            category=ErrorCategory.TIMEOUT,
            severity=ErrorSeverity.MEDIUM,
            action=RecoveryAction.RETRY,
            max_retries=3,
            retry_delay=2.0
        ))
        
        # 网络连接错误 - 重试
        self.register_strategy(RecoveryStrategy(
            error_pattern="connection",
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.MEDIUM,
            action=RecoveryAction.RETRY,
            max_retries=5,
            retry_delay=1.0
        ))
        
        # 权限错误 - 停止
        self.register_strategy(RecoveryStrategy(
            error_pattern="permission",
            category=ErrorCategory.PERMISSION,
            severity=ErrorSeverity.HIGH,
            action=RecoveryAction.STOP,
            max_retries=0
        ))
        
        # 验证错误 - 跳过
        self.register_strategy(RecoveryStrategy(
            error_pattern="validation",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            action=RecoveryAction.SKIP,
            max_retries=1
        ))
        
        # 系统错误 - 手动处理
        self.register_strategy(RecoveryStrategy(
            error_pattern="system",
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.CRITICAL,
            action=RecoveryAction.MANUAL,
            max_retries=0
        ))
    
    def register_strategy(self, strategy: RecoveryStrategy):
        """注册恢复策略"""
        self.recovery_strategies.append(strategy)
    
    def register_callback(self, callback: Callable[[ErrorInfo], None]):
        """注册错误回调"""
        self.error_callbacks.append(callback)
    
    def handle_error(self, exception: Exception, context: Dict[str, Any] = None) -> ErrorInfo:
        """
        处理错误
        
        Args:
            exception: 异常对象
            context: 错误上下文
            
        Returns:
            错误信息
        """
        import uuid
        
        # 创建错误信息
        error_info = ErrorInfo(
            error_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            error_type=type(exception).__name__,
            error_message=str(exception),
            error_details=self._extract_error_details(exception),
            severity=self._determine_severity(exception),
            category=self._categorize_error(exception),
            context=context or {},
            stack_trace=traceback.format_exc()
        )
        
        # 查找匹配的恢复策略
        strategy = self._find_recovery_strategy(error_info)
        if strategy:
            error_info.recovery_action = strategy.action
            error_info.max_retries = strategy.max_retries
        
        # 记录错误
        self.error_history.append(error_info)
        self.logger.error(f"错误处理: {error_info.error_message}", extra={
            'error_id': error_info.error_id,
            'category': error_info.category.value,
            'severity': error_info.severity.value
        })
        
        # 调用错误回调
        for callback in self.error_callbacks:
            try:
                callback(error_info)
            except Exception as e:
                self.logger.error(f"错误回调执行失败: {e}")
        
        return error_info
    
    def _extract_error_details(self, exception: Exception) -> str:
        """提取错误详情"""
        details = {
            'type': type(exception).__name__,
            'message': str(exception),
            'args': exception.args
        }
        
        # 添加特定异常的详细信息
        if hasattr(exception, 'errno'):
            details['errno'] = exception.errno
        
        if hasattr(exception, 'strerror'):
            details['strerror'] = exception.strerror
        
        if hasattr(exception, 'filename'):
            details['filename'] = exception.filename
        
        return json.dumps(details, ensure_ascii=False, indent=2)
    
    def _determine_severity(self, exception: Exception) -> ErrorSeverity:
        """确定错误严重程度"""
        error_type = type(exception).__name__.lower()
        error_message = str(exception).lower()
        
        # 致命错误
        if any(keyword in error_type for keyword in ['system', 'memory', 'fatal']):
            return ErrorSeverity.CRITICAL
        
        # 严重错误
        if any(keyword in error_type for keyword in ['permission', 'access', 'security']):
            return ErrorSeverity.HIGH
        
        # 中等错误
        if any(keyword in error_type for keyword in ['timeout', 'connection', 'network']):
            return ErrorSeverity.MEDIUM
        
        # 轻微错误
        if any(keyword in error_type for keyword in ['validation', 'format', 'value']):
            return ErrorSeverity.LOW
        
        # 默认中等严重程度
        return ErrorSeverity.MEDIUM
    
    def _categorize_error(self, exception: Exception) -> ErrorCategory:
        """错误分类"""
        error_type = type(exception).__name__.lower()
        error_message = str(exception).lower()
        
        # 网络错误
        if any(keyword in error_type for keyword in ['connection', 'network', 'socket']):
            return ErrorCategory.NETWORK
        
        # 超时错误
        if any(keyword in error_type for keyword in ['timeout']):
            return ErrorCategory.TIMEOUT
        
        # 权限错误
        if any(keyword in error_type for keyword in ['permission', 'access', 'forbidden']):
            return ErrorCategory.PERMISSION
        
        # 验证错误
        if any(keyword in error_type for keyword in ['validation', 'value', 'format']):
            return ErrorCategory.VALIDATION
        
        # 资源错误
        if any(keyword in error_type for keyword in ['memory', 'disk', 'resource']):
            return ErrorCategory.RESOURCE
        
        # 系统错误
        if any(keyword in error_type for keyword in ['system', 'os', 'environment']):
            return ErrorCategory.SYSTEM
        
        # 默认逻辑错误
        return ErrorCategory.LOGIC
    
    def _find_recovery_strategy(self, error_info: ErrorInfo) -> Optional[RecoveryStrategy]:
        """查找匹配的恢复策略"""
        for strategy in self.recovery_strategies:
            # 检查错误模式匹配
            if strategy.error_pattern.lower() in error_info.error_message.lower():
                # 检查分类匹配
                if strategy.category == error_info.category:
                    # 检查自定义条件
                    if strategy.condition is None or strategy.condition(error_info):
                        return strategy
        
        return None
    
    def should_retry(self, error_info: ErrorInfo) -> bool:
        """判断是否应该重试"""
        if error_info.recovery_action != RecoveryAction.RETRY:
            return False
        
        return error_info.retry_count < error_info.max_retries
    
    def execute_recovery(self, error_info: ErrorInfo, recovery_context: Dict[str, Any] = None) -> bool:
        """
        执行恢复动作
        
        Args:
            error_info: 错误信息
            recovery_context: 恢复上下文
            
        Returns:
            恢复是否成功
        """
        if not error_info.recovery_action:
            return False
        
        try:
            if error_info.recovery_action == RecoveryAction.RETRY:
                return self._execute_retry(error_info, recovery_context)
            
            elif error_info.recovery_action == RecoveryAction.SKIP:
                return self._execute_skip(error_info, recovery_context)
            
            elif error_info.recovery_action == RecoveryAction.ROLLBACK:
                return self._execute_rollback(error_info, recovery_context)
            
            elif error_info.recovery_action == RecoveryAction.STOP:
                return self._execute_stop(error_info, recovery_context)
            
            elif error_info.recovery_action == RecoveryAction.CONTINUE:
                return self._execute_continue(error_info, recovery_context)
            
            elif error_info.recovery_action == RecoveryAction.MANUAL:
                return self._execute_manual(error_info, recovery_context)
            
        except Exception as e:
            self.logger.error(f"执行恢复动作失败: {e}")
            return False
        
        return False
    
    def _execute_retry(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """执行重试"""
        if not self.should_retry(error_info):
            return False
        
        error_info.retry_count += 1
        
        # 获取重试延迟
        strategy = self._find_recovery_strategy(error_info)
        delay = strategy.retry_delay if strategy else 1.0
        
        self.logger.info(f"重试错误 {error_info.error_id}，第 {error_info.retry_count} 次，延迟 {delay} 秒")
        
        if delay > 0:
            time.sleep(delay)
        
        return True
    
    def _execute_skip(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """执行跳过"""
        self.logger.info(f"跳过错误 {error_info.error_id}")
        error_info.resolved = True
        return True
    
    def _execute_rollback(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """执行回滚"""
        self.logger.info(f"回滚错误 {error_info.error_id}")
        
        # 这里应该实现具体的回滚逻辑
        # 例如：恢复之前的状态、撤销已执行的操作等
        
        rollback_handler = context.get('rollback_handler') if context else None
        if rollback_handler and callable(rollback_handler):
            try:
                rollback_handler(error_info)
                error_info.resolved = True
                return True
            except Exception as e:
                self.logger.error(f"回滚处理器执行失败: {e}")
                return False
        
        return False
    
    def _execute_stop(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """执行停止"""
        self.logger.warning(f"停止执行，错误 {error_info.error_id}")
        return False
    
    def _execute_continue(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """执行继续"""
        self.logger.info(f"忽略错误继续执行 {error_info.error_id}")
        error_info.resolved = True
        return True
    
    def _execute_manual(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """执行手动处理"""
        self.logger.warning(f"错误 {error_info.error_id} 需要手动处理")
        
        # 这里可以触发用户界面提示或发送通知
        manual_handler = context.get('manual_handler') if context else None
        if manual_handler and callable(manual_handler):
            try:
                return manual_handler(error_info)
            except Exception as e:
                self.logger.error(f"手动处理器执行失败: {e}")
        
        return False
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        if not self.error_history:
            return {
                'total_errors': 0,
                'by_category': {},
                'by_severity': {},
                'resolved_count': 0,
                'unresolved_count': 0
            }
        
        stats = {
            'total_errors': len(self.error_history),
            'by_category': {},
            'by_severity': {},
            'resolved_count': 0,
            'unresolved_count': 0
        }
        
        for error in self.error_history:
            # 按分类统计
            category = error.category.value
            stats['by_category'][category] = stats['by_category'].get(category, 0) + 1
            
            # 按严重程度统计
            severity = error.severity.value
            stats['by_severity'][severity] = stats['by_severity'].get(severity, 0) + 1
            
            # 解决状态统计
            if error.resolved:
                stats['resolved_count'] += 1
            else:
                stats['unresolved_count'] += 1
        
        return stats
    
    def clear_error_history(self):
        """清空错误历史"""
        self.error_history.clear()
        self.logger.info("错误历史已清空")
    
    def export_error_report(self, file_path: str):
        """导出错误报告"""
        report = {
            'generated_time': datetime.now().isoformat(),
            'statistics': self.get_error_statistics(),
            'errors': [asdict(error) for error in self.error_history]
        }
        
        # 转换datetime对象为字符串
        for error in report['errors']:
            error['timestamp'] = error['timestamp'].isoformat()
            error['severity'] = error['severity'].value
            error['category'] = error['category'].value
            if error['recovery_action']:
                error['recovery_action'] = error['recovery_action'].value
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"错误报告已导出到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出错误报告失败: {e}")
            return False


# 全局错误处理器实例
_global_error_handler = None


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler()
    return _global_error_handler


def handle_error(exception: Exception, context: Dict[str, Any] = None) -> ErrorInfo:
    """处理错误的便捷函数"""
    return get_error_handler().handle_error(exception, context)
