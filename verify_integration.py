# -*- coding: utf-8 -*-
"""
快速验证任务编辑器集成
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主验证函数"""
    print("🔍 快速验证任务编辑器集成")
    print("=" * 40)
    
    try:
        # 1. 验证主窗口导入
        print("1. 验证主窗口导入...")
        from src.gui.main_window_v2 import MainWindowV2
        print("✅ 主窗口V2导入成功")
        
        # 2. 验证编辑器组件可用性
        print("\n2. 验证编辑器组件...")
        import src.gui.main_window_v2 as main_module
        if hasattr(main_module, 'EDITOR_COMPONENTS_AVAILABLE'):
            available = main_module.EDITOR_COMPONENTS_AVAILABLE
            print(f"✅ 编辑器组件可用性: {available}")
        else:
            print("❌ 编辑器组件可用性标志缺失")
        
        # 3. 验证任务编辑器方法
        print("\n3. 验证任务编辑器方法...")
        required_methods = [
            '_create_task_editor_tab',
            '_create_integrated_task_editor',
            '_new_task_in_editor',
            '_edit_task_in_editor',
            '_refresh_editor_task_list'
        ]
        
        missing_methods = []
        for method in required_methods:
            if hasattr(MainWindowV2, method):
                print(f"✅ {method}")
            else:
                print(f"❌ {method}")
                missing_methods.append(method)
        
        # 4. 验证任务编辑器对话框
        print("\n4. 验证任务编辑器对话框...")
        try:
            from src.gui.components.task_editor import TaskEditorDialog
            print("✅ TaskEditorDialog导入成功")
        except ImportError as e:
            print(f"❌ TaskEditorDialog导入失败: {e}")
        
        # 5. 验证配置管理器
        print("\n5. 验证配置管理器...")
        try:
            from task.config_manager import ConfigManager
            config_manager = ConfigManager()
            templates = config_manager.get_available_templates()
            print(f"✅ 配置管理器正常，找到 {len(templates)} 个模板")
        except Exception as e:
            print(f"❌ 配置管理器异常: {e}")
        
        # 6. 验证文件结构
        print("\n6. 验证关键文件...")
        key_files = [
            'src/gui/components/task_editor.py',
            'task/config_manager.py',
            'task/templates/click_task_template.json'
        ]
        
        for file_path in key_files:
            full_path = project_root / file_path
            if full_path.exists():
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path}")
        
        # 总结
        print("\n" + "=" * 40)
        print("📊 验证结果总结")
        print("=" * 40)
        
        if not missing_methods:
            print("🎉 任务编辑器已成功集成到GUI V2！")
            print("\n💡 可用功能:")
            print("   ✅ 任务编辑器标签页")
            print("   ✅ 新建/编辑任务功能")
            print("   ✅ 配置验证和保存")
            print("   ✅ 模板系统支持")
            print("\n🚀 使用方法:")
            print("   1. 启动GUI V2: python gui_main_v2.py")
            print("   2. 切换到'任务编辑器'标签页")
            print("   3. 点击'新建任务'创建任务")
            print("   4. 选择任务后点击'编辑任务'")
        else:
            print(f"⚠️ 发现 {len(missing_methods)} 个缺失的方法")
            print("需要检查任务编辑器集成")
        
        return len(missing_methods) == 0
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 验证完成 - 任务编辑器集成成功！")
    else:
        print("\n❌ 验证失败 - 需要检查集成问题")
    
    sys.exit(0 if success else 1)
