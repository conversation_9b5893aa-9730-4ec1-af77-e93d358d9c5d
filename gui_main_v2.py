# -*- coding: utf-8 -*-
"""
SnowZone OCR自动化工具 V2 - GUI启动脚本
基于新任务模块架构的优化版本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    try:
        print("启动SnowZone OCR自动化工具 V2...")
        print("正在加载新的任务模块架构...")
        
        # 导入GUI主窗口V2
        from src.gui.main_window_v2 import SnowZoneMainWindowV2
        
        print("✅ GUI模块加载成功")
        print("🚀 启动主窗口...")
        
        # 创建并运行主窗口
        app = SnowZoneMainWindowV2()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        print("请确保已安装所有必需的依赖包")
        print("运行: pip install -r requirements.txt")
        
        # 尝试启动原版GUI作为备选
        try:
            print("\n尝试启动原版GUI...")
            from src.gui.main_window import SnowZoneMainWindow
            app = SnowZoneMainWindow()
            app.run()
        except Exception as fallback_error:
            print(f"❌ 原版GUI也无法启动: {fallback_error}")
            sys.exit(1)
        
    except Exception as e:
        print(f"❌ 启动GUI V2失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
