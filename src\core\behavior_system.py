# -*- coding: utf-8 -*-
"""
行为系统
扩展的触发器行为配置
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import time


class BehaviorType(Enum):
    """行为类型枚举"""
    CLICK = "click"
    DOUBLE_CLICK = "double_click"
    RIGHT_CLICK = "right_click"
    LONG_PRESS = "long_press"
    SWIPE = "swipe"
    DRAG = "drag"
    HOVER = "hover"
    CUSTOM = "custom"


class ClickType(Enum):
    """点击类型枚举"""
    LEFT = "left"
    RIGHT = "right"
    MIDDLE = "middle"


@dataclass
class Position:
    """位置数据类"""
    x: int
    y: int
    
    def to_tuple(self) -> Tuple[int, int]:
        return (self.x, self.y)


@dataclass
class BehaviorConfig:
    """行为配置数据类"""
    id: str
    type: BehaviorType
    enabled: bool
    delay_before: float
    delay_after: float
    parameters: Dict[str, Any]


class ClickBehavior(BehaviorConfig):
    """点击行为配置"""
    
    def __init__(self, behavior_id: str, click_type: ClickType = ClickType.LEFT,
                 position: Optional[Position] = None, offset_x: int = 0, offset_y: int = 0,
                 delay_before: float = 0.0, delay_after: float = 0.5):
        super().__init__(
            id=behavior_id,
            type=BehaviorType.CLICK,
            enabled=True,
            delay_before=delay_before,
            delay_after=delay_after,
            parameters={
                "click_type": click_type.value,
                "position": position.to_tuple() if position else None,
                "offset_x": offset_x,
                "offset_y": offset_y
            }
        )


class LongPressBehavior(BehaviorConfig):
    """长按行为配置"""
    
    def __init__(self, behavior_id: str, duration: float = 1.0,
                 position: Optional[Position] = None, offset_x: int = 0, offset_y: int = 0,
                 delay_before: float = 0.0, delay_after: float = 0.5):
        super().__init__(
            id=behavior_id,
            type=BehaviorType.LONG_PRESS,
            enabled=True,
            delay_before=delay_before,
            delay_after=delay_after,
            parameters={
                "duration": duration,
                "position": position.to_tuple() if position else None,
                "offset_x": offset_x,
                "offset_y": offset_y
            }
        )


class SwipeBehavior(BehaviorConfig):
    """滑动行为配置"""
    
    def __init__(self, behavior_id: str, start_pos: Position, end_pos: Position,
                 duration: float = 1.0, delay_before: float = 0.0, delay_after: float = 0.5):
        super().__init__(
            id=behavior_id,
            type=BehaviorType.SWIPE,
            enabled=True,
            delay_before=delay_before,
            delay_after=delay_after,
            parameters={
                "start_position": start_pos.to_tuple(),
                "end_position": end_pos.to_tuple(),
                "duration": duration
            }
        )


class DragBehavior(BehaviorConfig):
    """拖拽行为配置"""
    
    def __init__(self, behavior_id: str, start_pos: Position, end_pos: Position,
                 duration: float = 1.0, delay_before: float = 0.0, delay_after: float = 0.5):
        super().__init__(
            id=behavior_id,
            type=BehaviorType.DRAG,
            enabled=True,
            delay_before=delay_before,
            delay_after=delay_after,
            parameters={
                "start_position": start_pos.to_tuple(),
                "end_position": end_pos.to_tuple(),
                "duration": duration
            }
        )


class BehaviorExecutor:
    """行为执行器"""
    
    def __init__(self, action_executor=None):
        """
        初始化行为执行器
        
        Args:
            action_executor: 动作执行器实例
        """
        self.action_executor = action_executor
    
    def execute_behavior(self, behavior: BehaviorConfig, 
                        trigger_position: Optional[Position] = None) -> bool:
        """
        执行行为
        
        Args:
            behavior: 行为配置
            trigger_position: 触发器识别到的位置
            
        Returns:
            执行是否成功
        """
        if not behavior.enabled:
            return True
        
        try:
            # 执行前延时
            if behavior.delay_before > 0:
                time.sleep(behavior.delay_before)
            
            # 根据行为类型执行相应操作
            success = False
            
            if behavior.type == BehaviorType.CLICK:
                success = self._execute_click(behavior, trigger_position)
            elif behavior.type == BehaviorType.DOUBLE_CLICK:
                success = self._execute_double_click(behavior, trigger_position)
            elif behavior.type == BehaviorType.RIGHT_CLICK:
                success = self._execute_right_click(behavior, trigger_position)
            elif behavior.type == BehaviorType.LONG_PRESS:
                success = self._execute_long_press(behavior, trigger_position)
            elif behavior.type == BehaviorType.SWIPE:
                success = self._execute_swipe(behavior)
            elif behavior.type == BehaviorType.DRAG:
                success = self._execute_drag(behavior)
            elif behavior.type == BehaviorType.HOVER:
                success = self._execute_hover(behavior, trigger_position)
            elif behavior.type == BehaviorType.CUSTOM:
                success = self._execute_custom(behavior, trigger_position)
            
            # 执行后延时
            if behavior.delay_after > 0:
                time.sleep(behavior.delay_after)
            
            return success
            
        except Exception as e:
            print(f"执行行为失败: {behavior.id} - {e}")
            return False
    
    def _calculate_position(self, behavior: BehaviorConfig, 
                          trigger_position: Optional[Position] = None) -> Position:
        """
        计算实际执行位置
        
        Args:
            behavior: 行为配置
            trigger_position: 触发器位置
            
        Returns:
            实际执行位置
        """
        params = behavior.parameters
        
        # 如果指定了固定位置
        if params.get("position"):
            x, y = params["position"]
            return Position(x, y)
        
        # 如果有触发器位置，使用偏移
        if trigger_position:
            offset_x = params.get("offset_x", 0)
            offset_y = params.get("offset_y", 0)
            return Position(
                trigger_position.x + offset_x,
                trigger_position.y + offset_y
            )
        
        # 默认位置
        return Position(0, 0)
    
    def _execute_click(self, behavior: BehaviorConfig, 
                      trigger_position: Optional[Position] = None) -> bool:
        """执行点击行为"""
        if not self.action_executor:
            print("动作执行器未初始化")
            return False
        
        position = self._calculate_position(behavior, trigger_position)
        click_type = behavior.parameters.get("click_type", "left")
        
        try:
            if hasattr(self.action_executor, 'click_at_position'):
                return self.action_executor.click_at_position(
                    position.x, position.y, click_type
                )
            else:
                print("动作执行器不支持点击操作")
                return False
        except Exception as e:
            print(f"执行点击失败: {e}")
            return False
    
    def _execute_double_click(self, behavior: BehaviorConfig, 
                            trigger_position: Optional[Position] = None) -> bool:
        """执行双击行为"""
        if not self.action_executor:
            return False
        
        position = self._calculate_position(behavior, trigger_position)
        
        try:
            if hasattr(self.action_executor, 'double_click_at_position'):
                return self.action_executor.double_click_at_position(
                    position.x, position.y
                )
            else:
                # 模拟双击：两次快速点击
                success1 = self._execute_click(behavior, trigger_position)
                time.sleep(0.1)
                success2 = self._execute_click(behavior, trigger_position)
                return success1 and success2
        except Exception as e:
            print(f"执行双击失败: {e}")
            return False
    
    def _execute_right_click(self, behavior: BehaviorConfig, 
                           trigger_position: Optional[Position] = None) -> bool:
        """执行右键点击行为"""
        if not self.action_executor:
            return False
        
        position = self._calculate_position(behavior, trigger_position)
        
        try:
            if hasattr(self.action_executor, 'right_click_at_position'):
                return self.action_executor.right_click_at_position(
                    position.x, position.y
                )
            else:
                # 修改行为参数为右键点击
                right_click_behavior = BehaviorConfig(
                    id=behavior.id,
                    type=BehaviorType.CLICK,
                    enabled=True,
                    delay_before=0,
                    delay_after=0,
                    parameters={**behavior.parameters, "click_type": "right"}
                )
                return self._execute_click(right_click_behavior, trigger_position)
        except Exception as e:
            print(f"执行右键点击失败: {e}")
            return False
    
    def _execute_long_press(self, behavior: BehaviorConfig, 
                          trigger_position: Optional[Position] = None) -> bool:
        """执行长按行为"""
        if not self.action_executor:
            return False
        
        position = self._calculate_position(behavior, trigger_position)
        duration = behavior.parameters.get("duration", 1.0)
        
        try:
            if hasattr(self.action_executor, 'long_press_at_position'):
                return self.action_executor.long_press_at_position(
                    position.x, position.y, duration
                )
            else:
                print("动作执行器不支持长按操作")
                return False
        except Exception as e:
            print(f"执行长按失败: {e}")
            return False
    
    def _execute_swipe(self, behavior: BehaviorConfig) -> bool:
        """执行滑动行为"""
        if not self.action_executor:
            return False
        
        params = behavior.parameters
        start_x, start_y = params["start_position"]
        end_x, end_y = params["end_position"]
        duration = params.get("duration", 1.0)
        
        try:
            if hasattr(self.action_executor, 'swipe'):
                return self.action_executor.swipe(
                    start_x, start_y, end_x, end_y, duration
                )
            else:
                print("动作执行器不支持滑动操作")
                return False
        except Exception as e:
            print(f"执行滑动失败: {e}")
            return False
    
    def _execute_drag(self, behavior: BehaviorConfig) -> bool:
        """执行拖拽行为"""
        if not self.action_executor:
            return False
        
        params = behavior.parameters
        start_x, start_y = params["start_position"]
        end_x, end_y = params["end_position"]
        duration = params.get("duration", 1.0)
        
        try:
            if hasattr(self.action_executor, 'drag'):
                return self.action_executor.drag(
                    start_x, start_y, end_x, end_y, duration
                )
            else:
                print("动作执行器不支持拖拽操作")
                return False
        except Exception as e:
            print(f"执行拖拽失败: {e}")
            return False
    
    def _execute_hover(self, behavior: BehaviorConfig, 
                      trigger_position: Optional[Position] = None) -> bool:
        """执行悬停行为"""
        if not self.action_executor:
            return False
        
        position = self._calculate_position(behavior, trigger_position)
        duration = behavior.parameters.get("duration", 1.0)
        
        try:
            if hasattr(self.action_executor, 'hover_at_position'):
                return self.action_executor.hover_at_position(
                    position.x, position.y, duration
                )
            else:
                print("动作执行器不支持悬停操作")
                return False
        except Exception as e:
            print(f"执行悬停失败: {e}")
            return False
    
    def _execute_custom(self, behavior: BehaviorConfig, 
                       trigger_position: Optional[Position] = None) -> bool:
        """执行自定义行为"""
        # 预留自定义行为扩展接口
        print(f"执行自定义行为: {behavior.id}")
        return True
    
    def execute_behavior_sequence(self, behaviors: List[BehaviorConfig],
                                trigger_position: Optional[Position] = None) -> bool:
        """
        执行行为序列
        
        Args:
            behaviors: 行为配置列表
            trigger_position: 触发器位置
            
        Returns:
            所有行为是否都执行成功
        """
        success_count = 0
        
        for behavior in behaviors:
            if self.execute_behavior(behavior, trigger_position):
                success_count += 1
            else:
                print(f"行为执行失败: {behavior.id}")
        
        return success_count == len(behaviors)


if __name__ == "__main__":
    # 测试行为系统
    print("测试行为系统...")
    
    try:
        # 创建行为配置
        click_behavior = ClickBehavior("test_click", ClickType.LEFT, Position(100, 200))
        long_press_behavior = LongPressBehavior("test_long_press", 2.0, Position(300, 400))
        swipe_behavior = SwipeBehavior("test_swipe", Position(100, 100), Position(200, 200))
        
        # 创建行为执行器
        executor = BehaviorExecutor()
        
        # 测试行为执行（没有实际的动作执行器，只是测试配置）
        behaviors = [click_behavior, long_press_behavior, swipe_behavior]
        
        print("行为配置创建成功:")
        for behavior in behaviors:
            print(f"  - {behavior.id}: {behavior.type.value}")
        
        print("行为系统测试完成")
        
    except Exception as e:
        print(f"行为系统测试失败: {e}")
