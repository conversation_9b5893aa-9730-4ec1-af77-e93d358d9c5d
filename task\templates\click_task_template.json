{"template_id": "click_task_basic", "name": "基础点击任务模板", "description": "用于创建基础点击任务的模板", "category": "basic", "schema": {"type": "object", "required": ["id", "name", "type", "parameters"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "const": "click"}, "class_name": {"type": "string", "const": "ClickTask"}, "enabled": {"type": "boolean"}, "priority": {"type": "integer", "minimum": 1, "maximum": 10}, "timeout": {"type": "number", "minimum": 0.1}, "retry_count": {"type": "integer", "minimum": 0, "maximum": 10}, "retry_delay": {"type": "number", "minimum": 0}, "parameters": {"type": "object", "required": ["position"], "properties": {"position": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["absolute", "relative", "auto"]}, "x": {"type": "integer"}, "y": {"type": "integer"}, "x_percent": {"type": "number", "minimum": 0, "maximum": 1}, "y_percent": {"type": "number", "minimum": 0, "maximum": 1}, "offset_x": {"type": "integer"}, "offset_y": {"type": "integer"}}}, "click_type": {"type": "string", "enum": ["left", "right", "middle"]}, "double_click": {"type": "boolean"}, "delay_before": {"type": "number", "minimum": 0}, "delay_after": {"type": "number", "minimum": 0}, "target_text": {"type": "string"}}}}}, "default_values": {"name": "新建点击任务", "description": "点击指定位置或文字", "type": "click", "class_name": "ClickTask", "enabled": true, "priority": 1, "timeout": 10.0, "retry_count": 3, "retry_delay": 1.0, "parameters": {"position": {"type": "absolute", "x": 100, "y": 100, "offset_x": 0, "offset_y": 0}, "click_type": "left", "double_click": false, "delay_before": 0.5, "delay_after": 0.5, "target_text": ""}, "triggers": [], "validation": {"expected_result": "success", "timeout": 5.0}}, "created_time": "2025-07-11T18:00:00", "updated_time": "2025-07-11T18:00:00"}