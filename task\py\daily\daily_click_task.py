# -*- coding: utf-8 -*-
"""
每日任务点击实现
集成OCR识别的智能点击任务
"""

import time
import pyautogui
from typing import Dict, Any, Tuple, Optional

from ...base_task import BaseTask, TaskResult, TaskStatus


class DailyClickTask(BaseTask):
    """每日任务点击类"""
    
    def execute(self) -> TaskResult:
        """
        执行点击操作
        
        Returns:
            任务执行结果
        """
        try:
            params = self.config.parameters
            target_text = params.get('target_text', '')
            
            # 首先尝试通过OCR找到目标文字
            position = self._find_text_position(target_text)
            
            if not position:
                # 如果OCR未找到，使用配置的位置
                position = self._get_click_position(params.get('position', {}))
            
            if not position:
                return TaskResult(
                    task_id=self.task_id,
                    status=TaskStatus.FAILED,
                    message=f"无法找到目标文字 '{target_text}' 的位置"
                )
            
            x, y = position
            
            # 应用偏移
            offset_x = params.get('position', {}).get('offset_x', 0)
            offset_y = params.get('position', {}).get('offset_y', 0)
            x += offset_x
            y += offset_y
            
            # 执行前延迟
            delay_before = params.get('delay_before', 0.5)
            if delay_before > 0:
                time.sleep(delay_before)
            
            # 执行点击
            click_type = params.get('click_type', 'left')
            double_click = params.get('double_click', False)
            
            if double_click:
                pyautogui.doubleClick(x, y, button=click_type)
            else:
                pyautogui.click(x, y, button=click_type)
            
            # 执行后延迟
            delay_after = params.get('delay_after', 0.5)
            if delay_after > 0:
                time.sleep(delay_after)
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.SUCCESS,
                message=f"成功点击 '{target_text}' 位置 ({x}, {y})",
                data={
                    'position': {'x': x, 'y': y}, 
                    'target_text': target_text,
                    'click_type': click_type
                }
            )
            
        except Exception as e:
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                message=f"点击操作失败: {str(e)}"
            )
    
    def _find_text_position(self, target_text: str) -> Optional[Tuple[int, int]]:
        """
        通过OCR查找文字位置
        
        Args:
            target_text: 目标文字
            
        Returns:
            文字中心位置坐标，如果未找到则返回None
        """
        if not target_text:
            return None
        
        try:
            # 这里应该集成现有的OCR系统
            # 暂时返回None，表示未找到
            # 实际实现中应该调用 src/core/ocr_engine.py
            
            # 示例代码结构：
            # from src.core.ocr_engine import OCREngine
            # ocr = OCREngine()
            # results = ocr.recognize_text()
            # for result in results:
            #     if target_text in result.text:
            #         return (result.center_x, result.center_y)
            
            return None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"OCR识别失败: {e}")
            return None
    
    def _get_click_position(self, position_config: Dict[str, Any]) -> Optional[Tuple[int, int]]:
        """
        获取点击位置
        
        Args:
            position_config: 位置配置
            
        Returns:
            点击坐标 (x, y)，如果无法确定则返回 None
        """
        position_type = position_config.get('type', 'absolute')
        
        if position_type == 'absolute':
            # 绝对坐标
            x = position_config.get('x', 0)
            y = position_config.get('y', 0)
            return (x, y)
        
        elif position_type == 'relative':
            # 相对于屏幕的百分比坐标
            screen_width, screen_height = pyautogui.size()
            x_percent = position_config.get('x_percent', 0.5)
            y_percent = position_config.get('y_percent', 0.5)
            x = int(screen_width * x_percent)
            y = int(screen_height * y_percent)
            return (x, y)
        
        elif position_type == 'auto':
            # 自动识别位置（通过OCR）
            return None  # 由上层调用OCR识别
        
        return None
    
    def validate_config(self) -> bool:
        """验证任务配置"""
        if not super().validate_config():
            return False
        
        params = self.config.parameters
        
        # 验证目标文字
        target_text = params.get('target_text', '')
        if not target_text:
            return False
        
        return True
