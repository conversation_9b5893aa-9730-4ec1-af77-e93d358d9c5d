{"id": "wait_task_001", "name": "等待任务", "description": "基础等待操作任务", "type": "wait", "class_name": "WaitTask", "enabled": true, "priority": 1, "timeout": 60.0, "retry_count": 1, "retry_delay": 0.0, "parameters": {"wait_type": "time", "duration": 1.0, "target_text": "", "target_image": "", "max_wait_time": 30.0, "check_interval": 0.5}, "triggers": [], "validation": {"expected_result": "success", "timeout": 5.0}}