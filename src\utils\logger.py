# -*- coding: utf-8 -*-
"""
日志工具模块
提供统一的日志记录功能，支持文件和控制台输出
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional

try:
    import colorlog
    COLORLOG_AVAILABLE = True
except ImportError:
    COLORLOG_AVAILABLE = False


class SnowZoneLogger:
    """SnowZone项目专用日志器"""
    
    def __init__(self, name: str = "SnowZone", config: Optional[dict] = None):
        """
        初始化日志器
        
        Args:
            name: 日志器名称
            config: 日志配置字典
        """
        self.name = name
        self.config = config or self._get_default_config()
        self.logger = None
        self._setup_logger()
    
    def _get_default_config(self) -> dict:
        """获取默认日志配置"""
        return {
            "level": "INFO",
            "console_output": True,
            "file_output": True,
            "max_file_size": "10MB",
            "backup_count": 5,
            "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "date_format": "%Y-%m-%d %H:%M:%S"
        }
    
    def _setup_logger(self):
        """设置日志器"""
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(getattr(logging, self.config["level"].upper()))
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 设置控制台输出
        if self.config.get("console_output", True):
            self._setup_console_handler()
        
        # 设置文件输出
        if self.config.get("file_output", True):
            self._setup_file_handler()
    
    def _setup_console_handler(self):
        """设置控制台处理器"""
        console_handler = logging.StreamHandler()

        if COLORLOG_AVAILABLE:
            # 彩色日志格式
            color_formatter = colorlog.ColoredFormatter(
                "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                datefmt=self.config.get("date_format", "%Y-%m-%d %H:%M:%S"),
                log_colors={
                    'DEBUG': 'cyan',
                    'INFO': 'green',
                    'WARNING': 'yellow',
                    'ERROR': 'red',
                    'CRITICAL': 'red,bg_white',
                }
            )
            console_handler.setFormatter(color_formatter)
        else:
            # 普通日志格式
            formatter = logging.Formatter(
                self.config.get("log_format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
                datefmt=self.config.get("date_format", "%Y-%m-%d %H:%M:%S")
            )
            console_handler.setFormatter(formatter)

        self.logger.addHandler(console_handler)
    
    def _setup_file_handler(self):
        """设置文件处理器"""
        # 确保日志目录存在
        logs_dir = "logs"
        os.makedirs(logs_dir, exist_ok=True)
        
        # 当前日志文件
        current_log_file = os.path.join(logs_dir, "current.log")
        
        # 解析文件大小
        max_bytes = self._parse_file_size(self.config.get("max_file_size", "10MB"))
        backup_count = self.config.get("backup_count", 5)
        
        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            current_log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        # 文件日志格式
        file_formatter = logging.Formatter(
            self.config.get("log_format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            datefmt=self.config.get("date_format", "%Y-%m-%d %H:%M:%S")
        )
        
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
    
    def _parse_file_size(self, size_str: str) -> int:
        """
        解析文件大小字符串
        
        Args:
            size_str: 大小字符串，如 "10MB", "1GB"
            
        Returns:
            字节数
        """
        size_str = size_str.upper().strip()
        
        if size_str.endswith('KB'):
            return int(float(size_str[:-2]) * 1024)
        elif size_str.endswith('MB'):
            return int(float(size_str[:-2]) * 1024 * 1024)
        elif size_str.endswith('GB'):
            return int(float(size_str[:-2]) * 1024 * 1024 * 1024)
        else:
            # 默认为字节
            return int(size_str)
    
    def get_logger(self) -> logging.Logger:
        """获取日志器实例"""
        return self.logger
    
    def debug(self, message: str, *args, **kwargs):
        """记录调试信息"""
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """记录信息"""
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """记录警告"""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """记录错误"""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """记录严重错误"""
        self.logger.critical(message, *args, **kwargs)
    
    def log_system_info(self):
        """记录系统信息"""
        import platform
        import sys
        
        self.info("=" * 50)
        self.info("SnowZone OCR自动化工具启动")
        self.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.info(f"Python版本: {sys.version}")
        self.info(f"操作系统: {platform.system()} {platform.release()}")
        self.info(f"处理器架构: {platform.machine()}")
        self.info("=" * 50)
    
    def log_version_info(self, version: str, description: str = ""):
        """记录版本信息"""
        self.info(f"当前版本: {version}")
        if description:
            self.info(f"版本描述: {description}")
    
    def log_performance(self, operation: str, duration: float, details: str = ""):
        """记录性能信息"""
        self.info(f"性能统计 - {operation}: {duration:.3f}秒 {details}")
    
    def log_ocr_result(self, text: str, confidence: float, region: tuple = None):
        """记录OCR识别结果"""
        region_str = f" 区域{region}" if region else ""
        self.debug(f"OCR识别{region_str}: '{text}' (置信度: {confidence:.2f})")
    
    def log_action_execution(self, action_type: str, details: str = "", success: bool = True):
        """记录动作执行"""
        status = "成功" if success else "失败"
        self.info(f"动作执行 - {action_type}: {status} {details}")


# 全局日志器实例
_global_logger = None


def get_logger(name: str = "SnowZone", config: Optional[dict] = None) -> SnowZoneLogger:
    """
    获取全局日志器实例
    
    Args:
        name: 日志器名称
        config: 日志配置
        
    Returns:
        日志器实例
    """
    global _global_logger
    if _global_logger is None:
        _global_logger = SnowZoneLogger(name, config)
    return _global_logger


def setup_logging(config: dict):
    """
    设置全局日志配置
    
    Args:
        config: 日志配置字典
    """
    global _global_logger
    _global_logger = SnowZoneLogger("SnowZone", config)


if __name__ == "__main__":
    # 测试日志功能
    logger = get_logger()
    logger.log_system_info()
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    logger.log_performance("测试操作", 1.234, "详细信息")
