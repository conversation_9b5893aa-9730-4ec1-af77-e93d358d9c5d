# -*- coding: utf-8 -*-
"""
完整系统集成测试
测试所有新增功能的完整性和稳定性
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_task_flow_advanced_features():
    """测试任务流高级功能"""
    print("=" * 60)
    print("测试任务流高级功能")
    print("=" * 60)
    
    try:
        from task.task_flow import TaskFlow, ExecutionMode
        from task.base_task import create_task_config, BaseTask, TaskResult, TaskStatus
        
        # 创建测试任务类
        class TestTask(BaseTask):
            def execute(self):
                import time
                time.sleep(0.1)  # 模拟执行时间
                return TaskResult(
                    task_id=self.task_id,
                    status=TaskStatus.SUCCESS,
                    message=f"测试任务 {self.task_name} 执行完成"
                )
        
        # 创建任务流
        flow = TaskFlow("test_flow_001", "测试任务流", "测试高级功能")
        
        # 添加测试任务
        for i in range(3):
            config = create_task_config(f"测试任务{i+1}", f"测试任务{i+1}描述")
            task = TestTask(config)
            flow.add_task(task, order=i, delay_after=0.1)
        
        print(f"✅ 创建任务流成功，包含 {len(flow.steps)} 个步骤")
        
        # 测试验证功能
        is_valid, errors = flow.validate()
        print(f"✅ 任务流验证: {'通过' if is_valid else '失败'}")
        if errors:
            for error in errors:
                print(f"   - {error}")
        
        # 测试执行计划
        plan = flow.get_execution_plan()
        print(f"✅ 执行计划生成成功")
        print(f"   - 总步骤数: {plan['total_steps']}")
        print(f"   - 预计耗时: {plan['estimated_duration']:.2f}秒")
        
        # 测试顺序执行
        print("🔄 测试顺序执行...")
        flow.set_execution_mode(ExecutionMode.SEQUENTIAL)
        result = flow.run()
        print(f"✅ 顺序执行完成: {result.status.value}")
        print(f"   - 成功步骤: {result.success_count}")
        print(f"   - 失败步骤: {result.failed_count}")
        print(f"   - 执行时间: {result.duration:.2f}秒")
        
        # 重置状态
        flow.reset()
        
        # 测试并行执行
        print("🔄 测试并行执行...")
        flow.set_execution_mode(ExecutionMode.PARALLEL)
        result = flow.run()
        print(f"✅ 并行执行完成: {result.status.value}")
        print(f"   - 成功步骤: {result.success_count}")
        print(f"   - 失败步骤: {result.failed_count}")
        print(f"   - 执行时间: {result.duration:.2f}秒")
        
        # 测试克隆功能
        cloned_flow = flow.clone("test_flow_clone", "克隆的任务流")
        print(f"✅ 任务流克隆成功: {cloned_flow.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务流高级功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_workflow_scheduling():
    """测试工作流调度功能"""
    print("\n" + "=" * 60)
    print("测试工作流调度功能")
    print("=" * 60)
    
    try:
        from task.workflow import Workflow, ScheduleConfig, ScheduleType
        from task.task_flow import TaskFlow
        from datetime import datetime, timedelta
        
        # 创建工作流
        workflow = Workflow("test_workflow_001", "测试工作流", "测试调度功能")
        
        # 创建简单任务流
        flow = TaskFlow("simple_flow", "简单流程", "用于测试的简单流程")
        
        # 添加任务流到工作流
        workflow.add_flow(flow, order=1)
        
        print(f"✅ 创建工作流成功，包含 {len(workflow.steps)} 个流程")
        
        # 测试验证功能
        is_valid, errors = workflow.validate()
        print(f"✅ 工作流验证: {'通过' if is_valid else '失败'}")
        if errors:
            for error in errors:
                print(f"   - {error}")
        
        # 测试执行计划
        plan = workflow.get_execution_plan()
        print(f"✅ 执行计划生成成功")
        print(f"   - 总步骤数: {plan['total_steps']}")
        print(f"   - 预计耗时: {plan['estimated_duration']:.2f}秒")
        
        # 测试间隔调度配置
        schedule_config = ScheduleConfig(
            schedule_type=ScheduleType.INTERVAL,
            start_time=datetime.now(),
            interval_seconds=60,
            max_executions=5,
            enabled=True
        )
        workflow.set_schedule(schedule_config)
        print(f"✅ 间隔调度配置成功: 每60秒执行一次，最多5次")
        
        # 测试Cron调度配置
        cron_schedule = ScheduleConfig(
            schedule_type=ScheduleType.CRON,
            cron_expression="0 9 * * *",  # 每天9点执行
            enabled=True
        )
        workflow.set_schedule(cron_schedule)
        print(f"✅ Cron调度配置成功: 每天9点执行")
        
        # 测试下次执行时间计算
        workflow._calculate_next_execution_time()
        if workflow.next_execution_time:
            print(f"✅ 下次执行时间: {workflow.next_execution_time}")
        
        # 测试克隆功能
        cloned_workflow = workflow.clone("test_workflow_clone", "克隆的工作流")
        print(f"✅ 工作流克隆成功: {cloned_workflow.name}")
        
        # 测试配置导出
        config = workflow.export_config()
        print(f"✅ 配置导出成功，包含 {len(config)} 个配置项")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流调度功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_management():
    """测试配置管理功能"""
    print("\n" + "=" * 60)
    print("测试配置管理功能")
    print("=" * 60)
    
    try:
        from task.config_manager import ConfigManager, ConfigValidator
        
        # 创建配置管理器
        config_manager = ConfigManager()
        print("✅ 配置管理器创建成功")
        
        # 测试配置验证
        validator = ConfigValidator()
        
        # 测试有效配置
        valid_config = {
            "id": "test_click_001",
            "name": "测试点击任务",
            "category": "basic",
            "type": "click",
            "parameters": {
                "position": {
                    "type": "absolute",
                    "x": 100,
                    "y": 100
                },
                "click_type": "left"
            }
        }
        
        is_valid, errors = validator.validate_config(valid_config, "click_task")
        print(f"✅ 配置验证测试: {'通过' if is_valid else '失败'}")
        if errors:
            for error in errors:
                print(f"   - {error}")
        
        # 测试模板功能
        templates = config_manager.get_available_templates()
        print(f"✅ 可用模板数量: {len(templates)}")
        for template in templates:
            print(f"   - {template['name']} ({template['category']})")
        
        # 测试从模板创建配置
        if templates:
            template_id = templates[0]['template_id']
            new_config = config_manager.create_config_from_template(
                template_id, 
                name="从模板创建的任务",
                description="测试从模板创建配置"
            )
            print(f"✅ 从模板创建配置成功: {new_config['name']}")
        
        # 测试配置保存
        success, errors, file_path = config_manager.validate_and_save_config(
            valid_config, "basic", "test_config.json"
        )
        print(f"✅ 配置保存测试: {'成功' if success else '失败'}")
        if file_path:
            print(f"   - 保存路径: {file_path}")
        
        # 测试配置列表
        configs = config_manager.list_configs("basic")
        print(f"✅ 配置列表获取成功，共 {len(configs)} 个配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理功能"""
    print("\n" + "=" * 60)
    print("测试错误处理功能")
    print("=" * 60)
    
    try:
        from task.error_handler import ErrorHandler, ErrorSeverity, ErrorCategory, RecoveryAction
        
        # 创建错误处理器
        error_handler = ErrorHandler()
        print("✅ 错误处理器创建成功")
        
        # 测试不同类型的错误
        test_errors = [
            (ConnectionError("网络连接失败"), "网络错误"),
            (TimeoutError("操作超时"), "超时错误"),
            (PermissionError("权限不足"), "权限错误"),
            (ValueError("参数值错误"), "验证错误"),
            (RuntimeError("运行时错误"), "运行时错误")
        ]
        
        for exception, description in test_errors:
            error_info = error_handler.handle_error(exception, {"test": description})
            print(f"✅ {description}处理成功:")
            print(f"   - 错误ID: {error_info.error_id}")
            print(f"   - 严重程度: {error_info.severity.value}")
            print(f"   - 错误分类: {error_info.category.value}")
            print(f"   - 恢复动作: {error_info.recovery_action.value if error_info.recovery_action else 'None'}")
        
        # 测试错误统计
        stats = error_handler.get_error_statistics()
        print(f"✅ 错误统计:")
        print(f"   - 总错误数: {stats['total_errors']}")
        print(f"   - 按分类统计: {stats['by_category']}")
        print(f"   - 按严重程度统计: {stats['by_severity']}")
        
        # 测试错误报告导出
        report_path = "error_report_test.json"
        success = error_handler.export_error_report(report_path)
        print(f"✅ 错误报告导出: {'成功' if success else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_persistence():
    """测试数据持久化功能"""
    print("\n" + "=" * 60)
    print("测试数据持久化功能")
    print("=" * 60)
    
    try:
        from task.persistence import PersistenceManager
        from task.task_flow import TaskFlow
        from task.workflow import Workflow
        
        # 创建持久化管理器
        persistence = PersistenceManager("test_data")
        print("✅ 持久化管理器创建成功")
        
        # 创建测试任务流
        test_flow = TaskFlow("persist_test_flow", "持久化测试流程", "用于测试持久化功能")
        
        # 保存任务流
        success, result = persistence.save_task_flow(test_flow, "初始版本", "test_user")
        print(f"✅ 任务流保存: {'成功' if success else '失败'}")
        if success:
            print(f"   - 保存路径: {result}")
        
        # 加载任务流
        loaded_flow = persistence.load_task_flow("persist_test_flow")
        if loaded_flow:
            print(f"✅ 任务流加载成功: {loaded_flow.name}")
        else:
            print("❌ 任务流加载失败")
        
        # 创建测试工作流
        test_workflow = Workflow("persist_test_workflow", "持久化测试工作流", "用于测试持久化功能")
        
        # 保存工作流
        success, result = persistence.save_workflow(test_workflow, "初始版本", "test_user")
        print(f"✅ 工作流保存: {'成功' if success else '失败'}")
        if success:
            print(f"   - 保存路径: {result}")
        
        # 加载工作流
        loaded_workflow = persistence.load_workflow("persist_test_workflow")
        if loaded_workflow:
            print(f"✅ 工作流加载成功: {loaded_workflow.name}")
        else:
            print("❌ 工作流加载失败")
        
        # 测试版本历史
        versions = persistence.get_version_history("task_flow", "persist_test_flow")
        print(f"✅ 版本历史获取成功，共 {len(versions)} 个版本")
        
        # 测试备份功能
        success, backup_path = persistence.create_backup("test")
        print(f"✅ 备份创建: {'成功' if success else '失败'}")
        if success:
            print(f"   - 备份路径: {backup_path}")
        
        # 列出备份
        backups = persistence.list_backups()
        print(f"✅ 备份列表获取成功，共 {len(backups)} 个备份")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据持久化功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_integration():
    """测试GUI集成功能"""
    print("\n" + "=" * 60)
    print("测试GUI集成功能")
    print("=" * 60)
    
    try:
        from src.gui.components.flow_visualizer import FlowVisualizerWidget
        from task.task_flow import TaskFlow
        import tkinter as tk
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("任务流可视化测试")
        root.geometry("800x600")
        
        # 创建可视化组件
        visualizer = FlowVisualizerWidget(root)
        visualizer.pack(fill=tk.BOTH, expand=True)
        
        # 创建测试任务流
        test_flow = TaskFlow("gui_test_flow", "GUI测试流程", "用于测试GUI集成")
        
        # 设置任务流
        visualizer.set_task_flow(test_flow)
        
        print("✅ GUI组件创建成功")
        print("✅ 任务流可视化组件集成成功")
        
        # 销毁测试窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI集成功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始完整系统集成测试")
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 1. 测试任务流高级功能
    test_results.append(("任务流高级功能", test_task_flow_advanced_features()))
    
    # 2. 测试工作流调度功能
    test_results.append(("工作流调度功能", test_workflow_scheduling()))
    
    # 3. 测试配置管理功能
    test_results.append(("配置管理功能", test_config_management()))
    
    # 4. 测试错误处理功能
    test_results.append(("错误处理功能", test_error_handling()))
    
    # 5. 测试数据持久化功能
    test_results.append(("数据持久化功能", test_persistence()))
    
    # 6. 测试GUI集成功能
    test_results.append(("GUI集成功能", test_gui_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("完整系统测试结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("🎉 所有测试通过！完整系统功能正常。")
        print("\n💡 系统已具备以下完整功能:")
        print("   ✅ 三层任务架构 (BaseTask → TaskFlow → Workflow)")
        print("   ✅ 任务流高级执行模式 (顺序、并行、条件)")
        print("   ✅ 工作流调度系统 (间隔、Cron、手动)")
        print("   ✅ 配置管理和验证系统")
        print("   ✅ 完整的错误处理机制")
        print("   ✅ 数据持久化和版本管理")
        print("   ✅ GUI可视化组件")
        print("\n🚀 现在可以运行完整的SnowZone系统:")
        print("   python gui_main_v2.py")
    else:
        print(f"⚠️ 有 {failed} 个测试失败，需要检查相关功能。")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
