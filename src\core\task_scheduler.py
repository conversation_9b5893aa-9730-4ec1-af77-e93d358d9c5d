# -*- coding: utf-8 -*-
"""
任务调度器
负责管理和调度自动化任务的执行
"""

import time
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

from .window_capture import WindowCapture
from .image_processor import ImageProcessor
from .ocr_engine import OCREngine
from .action_executor import ActionExecutor


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskExecution:
    """任务执行记录"""
    task_id: str
    start_time: datetime
    end_time: Optional[datetime]
    status: TaskStatus
    result: Optional[Any]
    error_message: Optional[str]
    execution_count: int


class TaskScheduler:
    """任务调度器类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化任务调度器
        
        Args:
            config: 调度器配置
        """
        self.config = config or self._get_default_config()
        
        # 核心组件
        self.window_capture = None
        self.image_processor = None
        self.ocr_engine = None
        self.action_executor = None
        
        # 任务管理
        self.tasks = {}  # 任务配置
        self.executions = {}  # 执行记录
        self.running = False
        self.scheduler_thread = None
        
        # 回调函数
        self.callbacks = {
            'on_task_start': [],
            'on_task_complete': [],
            'on_task_error': [],
            'on_scheduler_start': [],
            'on_scheduler_stop': []
        }
        
        # 初始化组件
        self._initialize_components()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "check_interval": 1.0,
            "max_concurrent_tasks": 1,
            "task_timeout": 300.0,
            "retry_attempts": 3,
            "retry_delay": 5.0,
            "enable_logging": True
        }
    
    def _initialize_components(self):
        """初始化核心组件"""
        try:
            self.window_capture = WindowCapture()
            self.image_processor = ImageProcessor()
            self.ocr_engine = OCREngine()
            self.action_executor = ActionExecutor()
            print("任务调度器组件初始化完成")
        except Exception as e:
            print(f"组件初始化失败: {e}")
            raise
    
    def add_task(self, task_id: str, task_config: Dict[str, Any]) -> bool:
        """
        添加任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            
        Returns:
            添加是否成功
        """
        try:
            # 验证任务配置
            if not self._validate_task_config(task_config):
                print(f"任务配置验证失败: {task_id}")
                return False
            
            self.tasks[task_id] = task_config
            self.executions[task_id] = []
            
            print(f"任务已添加: {task_id}")
            return True
            
        except Exception as e:
            print(f"添加任务失败: {e}")
            return False
    
    def remove_task(self, task_id: str) -> bool:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            移除是否成功
        """
        if task_id in self.tasks:
            del self.tasks[task_id]
            if task_id in self.executions:
                del self.executions[task_id]
            print(f"任务已移除: {task_id}")
            return True
        return False
    
    def _validate_task_config(self, config: Dict[str, Any]) -> bool:
        """
        验证任务配置
        
        Args:
            config: 任务配置
            
        Returns:
            配置是否有效
        """
        required_fields = ["task_info", "triggers", "actions"]
        
        for field in required_fields:
            if field not in config:
                print(f"缺少必需字段: {field}")
                return False
        
        # 验证触发器
        triggers = config.get("triggers", [])
        if not isinstance(triggers, list) or len(triggers) == 0:
            print("触发器配置无效")
            return False
        
        # 验证动作
        actions = config.get("actions", [])
        if not isinstance(actions, list) or len(actions) == 0:
            print("动作配置无效")
            return False
        
        return True
    
    def start_scheduler(self) -> bool:
        """
        启动调度器
        
        Returns:
            启动是否成功
        """
        if self.running:
            print("调度器已在运行")
            return False
        
        try:
            # 查找游戏窗口
            if not self.window_capture.find_game_window():
                print("警告: 未找到游戏窗口，调度器将在后台运行")
            
            self.running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            
            # 触发启动回调
            self._trigger_callbacks('on_scheduler_start')
            
            print("任务调度器已启动")
            return True
            
        except Exception as e:
            print(f"启动调度器失败: {e}")
            return False
    
    def stop_scheduler(self):
        """停止调度器"""
        if not self.running:
            return
        
        self.running = False
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5.0)
        
        # 触发停止回调
        self._trigger_callbacks('on_scheduler_stop')
        
        print("任务调度器已停止")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        check_interval = self.config.get("check_interval", 1.0)
        
        while self.running:
            try:
                # 检查所有任务
                for task_id, task_config in self.tasks.items():
                    if self._should_execute_task(task_id, task_config):
                        self._execute_task(task_id, task_config)
                
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"调度器循环异常: {e}")
                time.sleep(check_interval)
    
    def _should_execute_task(self, task_id: str, task_config: Dict[str, Any]) -> bool:
        """
        检查任务是否应该执行
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            
        Returns:
            是否应该执行
        """
        # 检查任务是否启用
        if not task_config.get("task_info", {}).get("enabled", True):
            return False
        
        # 检查时间条件
        conditions = task_config.get("conditions", {})
        if not self._check_time_conditions(conditions):
            return False
        
        # 检查冷却时间
        if not self._check_cooldown(task_id, conditions):
            return False
        
        # 检查执行次数限制
        if not self._check_execution_limit(task_id, conditions):
            return False
        
        # 检查触发条件
        return self._check_triggers(task_config.get("triggers", []))
    
    def _check_time_conditions(self, conditions: Dict[str, Any]) -> bool:
        """检查时间条件"""
        time_range = conditions.get("time_range")
        if not time_range:
            return True
        
        current_time = datetime.now().time()
        start_time = datetime.strptime(time_range.get("start", "00:00"), "%H:%M").time()
        end_time = datetime.strptime(time_range.get("end", "23:59"), "%H:%M").time()
        
        if start_time <= end_time:
            return start_time <= current_time <= end_time
        else:
            # 跨天的时间范围
            return current_time >= start_time or current_time <= end_time
    
    def _check_cooldown(self, task_id: str, conditions: Dict[str, Any]) -> bool:
        """检查冷却时间"""
        cooldown = conditions.get("cooldown", 0)
        if cooldown <= 0:
            return True
        
        executions = self.executions.get(task_id, [])
        if not executions:
            return True
        
        last_execution = max(executions, key=lambda x: x.start_time)
        if last_execution.status != TaskStatus.COMPLETED:
            return True
        
        time_since_last = datetime.now() - last_execution.start_time
        return time_since_last.total_seconds() >= cooldown
    
    def _check_execution_limit(self, task_id: str, conditions: Dict[str, Any]) -> bool:
        """检查执行次数限制"""
        max_executions = conditions.get("max_executions_per_day", 0)
        if max_executions <= 0:
            return True
        
        today = datetime.now().date()
        executions = self.executions.get(task_id, [])
        
        today_executions = [
            ex for ex in executions 
            if ex.start_time.date() == today and ex.status == TaskStatus.COMPLETED
        ]
        
        return len(today_executions) < max_executions
    
    def _check_triggers(self, triggers: List[Dict[str, Any]]) -> bool:
        """
        检查触发条件
        
        Args:
            triggers: 触发器列表
            
        Returns:
            是否满足触发条件
        """
        if not triggers:
            return False
        
        # 捕获窗口画面
        window_image = self.window_capture.capture_window()
        if window_image is None:
            return False
        
        # 检查每个触发器
        for trigger in triggers:
            if self._check_single_trigger(trigger, window_image):
                return True
        
        return False
    
    def _check_single_trigger(self, trigger: Dict[str, Any], window_image) -> bool:
        """检查单个触发器"""
        trigger_type = trigger.get("type", "")
        
        if trigger_type == "text_recognition":
            return self._check_text_trigger(trigger, window_image)
        
        # 可以添加其他类型的触发器
        return False
    
    def _check_text_trigger(self, trigger: Dict[str, Any], window_image) -> bool:
        """检查文字识别触发器"""
        target_text = trigger.get("target_text", "")
        confidence = trigger.get("confidence", 0.7)
        region = trigger.get("region")
        
        if not target_text:
            return False
        
        # 如果指定了区域，只检查该区域
        if region:
            x = region.get("x", 0)
            y = region.get("y", 0)
            width = region.get("width", window_image.shape[1])
            height = region.get("height", window_image.shape[0])
            
            region_image = self.window_capture.capture_region((x, y, width, height))
            if region_image is None:
                return False
            
            check_image = region_image
        else:
            check_image = window_image
        
        # 预处理图像
        preprocessing = trigger.get("preprocessing", {})
        if preprocessing:
            check_image = self.image_processor.preprocess_image(check_image, preprocessing)
        
        # OCR识别
        return self.ocr_engine.is_text_present(check_image, target_text, confidence)
    
    def _execute_task(self, task_id: str, task_config: Dict[str, Any]):
        """
        执行任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
        """
        execution = TaskExecution(
            task_id=task_id,
            start_time=datetime.now(),
            end_time=None,
            status=TaskStatus.RUNNING,
            result=None,
            error_message=None,
            execution_count=len(self.executions.get(task_id, [])) + 1
        )
        
        self.executions.setdefault(task_id, []).append(execution)
        
        # 触发开始回调
        self._trigger_callbacks('on_task_start', task_id, execution)
        
        try:
            print(f"开始执行任务: {task_id}")
            
            # 执行动作序列
            actions = task_config.get("actions", [])
            results = self.action_executor.execute_task_sequence(
                actions, self.window_capture, self.ocr_engine
            )
            
            # 检查执行结果
            success = all(result.success for result in results)
            
            execution.end_time = datetime.now()
            execution.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
            execution.result = results
            
            if success:
                print(f"任务执行成功: {task_id}")
                self._trigger_callbacks('on_task_complete', task_id, execution)
            else:
                print(f"任务执行失败: {task_id}")
                execution.error_message = "部分动作执行失败"
                self._trigger_callbacks('on_task_error', task_id, execution)
            
        except Exception as e:
            execution.end_time = datetime.now()
            execution.status = TaskStatus.FAILED
            execution.error_message = str(e)
            
            print(f"任务执行异常: {task_id} - {e}")
            self._trigger_callbacks('on_task_error', task_id, execution)
    
    def _trigger_callbacks(self, event: str, *args):
        """触发回调函数"""
        callbacks = self.callbacks.get(event, [])
        for callback in callbacks:
            try:
                callback(*args)
            except Exception as e:
                print(f"回调函数执行失败: {e}")
    
    def add_callback(self, event: str, callback: Callable):
        """
        添加回调函数
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def get_task_status(self, task_id: str) -> Optional[List[TaskExecution]]:
        """
        获取任务执行状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            执行记录列表
        """
        return self.executions.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务配置"""
        return self.tasks.copy()


if __name__ == "__main__":
    # 测试任务调度器
    print("测试任务调度器...")
    
    try:
        scheduler = TaskScheduler()
        print("任务调度器初始化成功")
        
        # 添加测试任务
        test_task = {
            "task_info": {
                "name": "测试任务",
                "enabled": True
            },
            "triggers": [
                {
                    "type": "text_recognition",
                    "target_text": "测试",
                    "confidence": 0.8
                }
            ],
            "actions": [
                {
                    "type": "wait",
                    "delay": 1.0
                }
            ],
            "conditions": {
                "cooldown": 60
            }
        }
        
        scheduler.add_task("test_task", test_task)
        print("测试任务已添加")
        
    except Exception as e:
        print(f"任务调度器测试失败: {e}")
