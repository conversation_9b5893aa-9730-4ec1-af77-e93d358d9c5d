# -*- coding: utf-8 -*-
"""
任务编辑器
可视化编辑任务配置的对话框
"""

import tkinter as tk
from tkinter import ttk
import json
from typing import Dict, Any, Optional, List
import copy

from .utils.gui_helpers import (
    GUIStyles, center_window, create_tooltip, show_error, 
    show_info, ask_yes_no, validate_json_config
)


class TaskEditorDialog:
    """任务编辑器对话框"""
    
    def __init__(self, parent: tk.Widget, task_loader=None, task_id: Optional[str] = None):
        self.parent = parent
        self.task_loader = task_loader
        self.task_id = task_id
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("任务编辑器" if task_id else "新建任务")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        center_window(self.dialog, 800, 600)
        
        # 初始化数据
        self._init_data()
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        # 加载数据
        self._load_task_data()
    
    def _init_data(self):
        """初始化数据"""
        self.task_config = {
            "task_info": {
                "name": "",
                "description": "",
                "version": "0_0001",
                "enabled": True,
                "priority": 1
            },
            "triggers": [],
            "actions": [],
            "flow_control": {
                "execution_order": [],
                "loop_enabled": False,
                "loop_count": 1,
                "continue_on_error": True
            },
            "conditions": {
                "time_range": {
                    "start": "00:00",
                    "end": "23:59"
                },
                "cooldown": 3600,
                "max_executions_per_day": 1
            }
        }
    
    def _create_widgets(self):
        """创建控件"""
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.dialog)
        
        # 基本信息页面
        self._create_basic_info_page()
        
        # 触发器页面
        self._create_triggers_page()
        
        # 动作页面
        self._create_actions_page()
        
        # 执行条件页面
        self._create_conditions_page()
        
        # 预览页面
        self._create_preview_page()
        
        # 按钮栏
        self._create_button_bar()
    
    def _create_basic_info_page(self):
        """创建基本信息页面"""
        self.basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.basic_frame, text="基本信息")
        
        # 主框架
        main_frame = ttk.Frame(self.basic_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=GUIStyles.PADDING['medium'], 
                       pady=GUIStyles.PADDING['medium'])
        
        # 任务名称
        ttk.Label(main_frame, text="任务名称:").grid(
            row=0, column=0, sticky=tk.W, pady=GUIStyles.PADDING['small']
        )
        self.name_var = tk.StringVar()
        self.name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=40)
        self.name_entry.grid(row=0, column=1, sticky=tk.W+tk.E, 
                           padx=(GUIStyles.PADDING['small'], 0), 
                           pady=GUIStyles.PADDING['small'])
        
        # 任务描述
        ttk.Label(main_frame, text="任务描述:").grid(
            row=1, column=0, sticky=tk.NW, pady=GUIStyles.PADDING['small']
        )
        self.description_text = tk.Text(main_frame, height=4, width=40)
        self.description_text.grid(row=1, column=1, sticky=tk.W+tk.E, 
                                 padx=(GUIStyles.PADDING['small'], 0), 
                                 pady=GUIStyles.PADDING['small'])
        
        # 启用状态
        self.enabled_var = tk.BooleanVar(value=True)
        self.enabled_check = ttk.Checkbutton(
            main_frame, 
            text="启用任务", 
            variable=self.enabled_var
        )
        self.enabled_check.grid(row=2, column=1, sticky=tk.W, 
                              padx=(GUIStyles.PADDING['small'], 0), 
                              pady=GUIStyles.PADDING['small'])
        
        # 优先级
        ttk.Label(main_frame, text="优先级:").grid(
            row=3, column=0, sticky=tk.W, pady=GUIStyles.PADDING['small']
        )
        self.priority_var = tk.IntVar(value=1)
        self.priority_spin = ttk.Spinbox(
            main_frame, 
            from_=1, 
            to=10, 
            textvariable=self.priority_var,
            width=10
        )
        self.priority_spin.grid(row=3, column=1, sticky=tk.W, 
                              padx=(GUIStyles.PADDING['small'], 0), 
                              pady=GUIStyles.PADDING['small'])
        
        # 配置列权重
        main_frame.grid_columnconfigure(1, weight=1)
    
    def _create_triggers_page(self):
        """创建触发器页面"""
        self.triggers_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.triggers_frame, text="触发器")
        
        # 工具栏
        toolbar = ttk.Frame(self.triggers_frame)
        toolbar.pack(fill=tk.X, padx=GUIStyles.PADDING['medium'], 
                    pady=GUIStyles.PADDING['small'])
        
        ttk.Button(toolbar, text="添加触发器", 
                  command=self._add_trigger).pack(side=tk.LEFT, 
                                                 padx=(0, GUIStyles.PADDING['small']))
        ttk.Button(toolbar, text="删除触发器", 
                  command=self._remove_trigger).pack(side=tk.LEFT)
        
        # 触发器列表
        list_frame = ttk.Frame(self.triggers_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, 
                       padx=GUIStyles.PADDING['medium'])
        
        # 创建Treeview
        columns = ('type', 'target_text', 'confidence')
        self.triggers_tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show='tree headings',
            height=6
        )
        
        self.triggers_tree.heading('#0', text='ID')
        self.triggers_tree.heading('type', text='类型')
        self.triggers_tree.heading('target_text', text='目标文字')
        self.triggers_tree.heading('confidence', text='置信度')
        
        self.triggers_tree.column('#0', width=100)
        self.triggers_tree.column('type', width=100)
        self.triggers_tree.column('target_text', width=200)
        self.triggers_tree.column('confidence', width=80)
        
        # 滚动条
        triggers_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, 
                                      command=self.triggers_tree.yview)
        self.triggers_tree.configure(yscrollcommand=triggers_scroll.set)
        
        self.triggers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        triggers_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 触发器详情
        details_frame = ttk.LabelFrame(self.triggers_frame, text="触发器详情")
        details_frame.pack(fill=tk.X, padx=GUIStyles.PADDING['medium'], 
                          pady=GUIStyles.PADDING['small'])
        
        self._create_trigger_details(details_frame)
        
        # 绑定事件
        self.triggers_tree.bind('<<TreeviewSelect>>', self._on_trigger_selected)
    
    def _create_trigger_details(self, parent):
        """创建触发器详情控件"""
        # ID
        ttk.Label(parent, text="触发器ID:").grid(row=0, column=0, sticky=tk.W, 
                                              pady=GUIStyles.PADDING['small'])
        self.trigger_id_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.trigger_id_var, width=20).grid(
            row=0, column=1, sticky=tk.W, padx=(GUIStyles.PADDING['small'], 0),
            pady=GUIStyles.PADDING['small']
        )
        
        # 类型
        ttk.Label(parent, text="类型:").grid(row=0, column=2, sticky=tk.W, 
                                           padx=(GUIStyles.PADDING['medium'], 0),
                                           pady=GUIStyles.PADDING['small'])
        self.trigger_type_var = tk.StringVar(value="text_recognition")
        trigger_type_combo = ttk.Combobox(
            parent, 
            textvariable=self.trigger_type_var,
            values=["text_recognition"],
            state="readonly",
            width=15
        )
        trigger_type_combo.grid(row=0, column=3, sticky=tk.W, 
                              padx=(GUIStyles.PADDING['small'], 0),
                              pady=GUIStyles.PADDING['small'])
        
        # 目标文字
        ttk.Label(parent, text="目标文字:").grid(row=1, column=0, sticky=tk.W, 
                                              pady=GUIStyles.PADDING['small'])
        self.trigger_text_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.trigger_text_var, width=20).grid(
            row=1, column=1, sticky=tk.W, padx=(GUIStyles.PADDING['small'], 0),
            pady=GUIStyles.PADDING['small']
        )
        
        # 置信度
        ttk.Label(parent, text="置信度:").grid(row=1, column=2, sticky=tk.W, 
                                             padx=(GUIStyles.PADDING['medium'], 0),
                                             pady=GUIStyles.PADDING['small'])
        self.trigger_confidence_var = tk.DoubleVar(value=0.8)
        ttk.Spinbox(
            parent, 
            from_=0.1, 
            to=1.0, 
            increment=0.1,
            textvariable=self.trigger_confidence_var,
            width=10
        ).grid(row=1, column=3, sticky=tk.W, 
               padx=(GUIStyles.PADDING['small'], 0),
               pady=GUIStyles.PADDING['small'])
        
        # 识别区域
        region_frame = ttk.LabelFrame(parent, text="识别区域")
        region_frame.grid(row=2, column=0, columnspan=4, sticky=tk.W+tk.E, 
                         pady=GUIStyles.PADDING['small'])
        
        # X坐标
        ttk.Label(region_frame, text="X:").grid(row=0, column=0, sticky=tk.W)
        self.region_x_var = tk.IntVar(value=0)
        ttk.Entry(region_frame, textvariable=self.region_x_var, width=8).grid(
            row=0, column=1, padx=(GUIStyles.PADDING['small'], GUIStyles.PADDING['medium'])
        )
        
        # Y坐标
        ttk.Label(region_frame, text="Y:").grid(row=0, column=2, sticky=tk.W)
        self.region_y_var = tk.IntVar(value=0)
        ttk.Entry(region_frame, textvariable=self.region_y_var, width=8).grid(
            row=0, column=3, padx=(GUIStyles.PADDING['small'], GUIStyles.PADDING['medium'])
        )
        
        # 宽度
        ttk.Label(region_frame, text="宽度:").grid(row=0, column=4, sticky=tk.W)
        self.region_width_var = tk.IntVar(value=1920)
        ttk.Entry(region_frame, textvariable=self.region_width_var, width=8).grid(
            row=0, column=5, padx=(GUIStyles.PADDING['small'], GUIStyles.PADDING['medium'])
        )
        
        # 高度
        ttk.Label(region_frame, text="高度:").grid(row=0, column=6, sticky=tk.W)
        self.region_height_var = tk.IntVar(value=1080)
        ttk.Entry(region_frame, textvariable=self.region_height_var, width=8).grid(
            row=0, column=7, padx=(GUIStyles.PADDING['small'], 0)
        )
        
        # 更新按钮
        ttk.Button(parent, text="更新触发器",
                  command=self._update_trigger).grid(
            row=3, column=0, columnspan=4, pady=GUIStyles.PADDING['small']
        )

    def _create_actions_page(self):
        """创建动作页面"""
        self.actions_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.actions_frame, text="动作")

        # 工具栏
        toolbar = ttk.Frame(self.actions_frame)
        toolbar.pack(fill=tk.X, padx=GUIStyles.PADDING['medium'],
                    pady=GUIStyles.PADDING['small'])

        ttk.Button(toolbar, text="添加动作",
                  command=self._add_action).pack(side=tk.LEFT,
                                               padx=(0, GUIStyles.PADDING['small']))
        ttk.Button(toolbar, text="删除动作",
                  command=self._remove_action).pack(side=tk.LEFT)

        # 动作列表
        list_frame = ttk.Frame(self.actions_frame)
        list_frame.pack(fill=tk.BOTH, expand=True,
                       padx=GUIStyles.PADDING['medium'])

        # 创建Treeview
        columns = ('type', 'description', 'delay')
        self.actions_tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show='tree headings',
            height=6
        )

        self.actions_tree.heading('#0', text='ID')
        self.actions_tree.heading('type', text='类型')
        self.actions_tree.heading('description', text='描述')
        self.actions_tree.heading('delay', text='延时')

        self.actions_tree.column('#0', width=100)
        self.actions_tree.column('type', width=100)
        self.actions_tree.column('description', width=200)
        self.actions_tree.column('delay', width=80)

        # 滚动条
        actions_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL,
                                     command=self.actions_tree.yview)
        self.actions_tree.configure(yscrollcommand=actions_scroll.set)

        self.actions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        actions_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 动作详情
        details_frame = ttk.LabelFrame(self.actions_frame, text="动作详情")
        details_frame.pack(fill=tk.X, padx=GUIStyles.PADDING['medium'],
                          pady=GUIStyles.PADDING['small'])

        self._create_action_details(details_frame)

        # 绑定事件
        self.actions_tree.bind('<<TreeviewSelect>>', self._on_action_selected)

    def _create_action_details(self, parent):
        """创建动作详情控件"""
        # ID
        ttk.Label(parent, text="动作ID:").grid(row=0, column=0, sticky=tk.W,
                                             pady=GUIStyles.PADDING['small'])
        self.action_id_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.action_id_var, width=20).grid(
            row=0, column=1, sticky=tk.W, padx=(GUIStyles.PADDING['small'], 0),
            pady=GUIStyles.PADDING['small']
        )

        # 类型
        ttk.Label(parent, text="类型:").grid(row=0, column=2, sticky=tk.W,
                                           padx=(GUIStyles.PADDING['medium'], 0),
                                           pady=GUIStyles.PADDING['small'])
        self.action_type_var = tk.StringVar(value="click")
        action_type_combo = ttk.Combobox(
            parent,
            textvariable=self.action_type_var,
            values=["click", "wait", "key", "text", "wait_for_text"],
            state="readonly",
            width=15
        )
        action_type_combo.grid(row=0, column=3, sticky=tk.W,
                             padx=(GUIStyles.PADDING['small'], 0),
                             pady=GUIStyles.PADDING['small'])
        action_type_combo.bind('<<ComboboxSelected>>', self._on_action_type_changed)

        # 位置设置框架
        self.position_frame = ttk.LabelFrame(parent, text="位置设置")
        self.position_frame.grid(row=1, column=0, columnspan=4, sticky=tk.W+tk.E,
                               pady=GUIStyles.PADDING['small'])

        # 位置类型
        ttk.Label(self.position_frame, text="位置类型:").grid(row=0, column=0, sticky=tk.W)
        self.position_type_var = tk.StringVar(value="auto")
        position_type_combo = ttk.Combobox(
            self.position_frame,
            textvariable=self.position_type_var,
            values=["auto", "fixed"],
            state="readonly",
            width=10
        )
        position_type_combo.grid(row=0, column=1, padx=(GUIStyles.PADDING['small'], 0))
        position_type_combo.bind('<<ComboboxSelected>>', self._on_position_type_changed)

        # 固定坐标
        ttk.Label(self.position_frame, text="X:").grid(row=0, column=2, sticky=tk.W,
                                                      padx=(GUIStyles.PADDING['medium'], 0))
        self.position_x_var = tk.IntVar(value=0)
        self.position_x_entry = ttk.Entry(self.position_frame, textvariable=self.position_x_var, width=8)
        self.position_x_entry.grid(row=0, column=3, padx=(GUIStyles.PADDING['small'], 0))

        ttk.Label(self.position_frame, text="Y:").grid(row=0, column=4, sticky=tk.W,
                                                      padx=(GUIStyles.PADDING['medium'], 0))
        self.position_y_var = tk.IntVar(value=0)
        self.position_y_entry = ttk.Entry(self.position_frame, textvariable=self.position_y_var, width=8)
        self.position_y_entry.grid(row=0, column=5, padx=(GUIStyles.PADDING['small'], 0))

        # 目标文字（用于自动定位）
        ttk.Label(self.position_frame, text="目标文字:").grid(row=1, column=0, sticky=tk.W)
        self.action_target_text_var = tk.StringVar()
        self.action_target_text_entry = ttk.Entry(
            self.position_frame,
            textvariable=self.action_target_text_var,
            width=30
        )
        self.action_target_text_entry.grid(row=1, column=1, columnspan=3, sticky=tk.W+tk.E,
                                         padx=(GUIStyles.PADDING['small'], 0))

        # 延时设置
        timing_frame = ttk.LabelFrame(parent, text="延时设置")
        timing_frame.grid(row=2, column=0, columnspan=4, sticky=tk.W+tk.E,
                         pady=GUIStyles.PADDING['small'])

        ttk.Label(timing_frame, text="执行前延时:").grid(row=0, column=0, sticky=tk.W)
        self.delay_before_var = tk.DoubleVar(value=1.0)
        ttk.Spinbox(
            timing_frame,
            from_=0.0,
            to=10.0,
            increment=0.1,
            textvariable=self.delay_before_var,
            width=8
        ).grid(row=0, column=1, padx=(GUIStyles.PADDING['small'], GUIStyles.PADDING['medium']))

        ttk.Label(timing_frame, text="执行后延时:").grid(row=0, column=2, sticky=tk.W)
        self.delay_after_var = tk.DoubleVar(value=2.0)
        ttk.Spinbox(
            timing_frame,
            from_=0.0,
            to=10.0,
            increment=0.1,
            textvariable=self.delay_after_var,
            width=8
        ).grid(row=0, column=3, padx=(GUIStyles.PADDING['small'], 0))

        # 更新按钮
        ttk.Button(parent, text="更新动作",
                  command=self._update_action).grid(
            row=3, column=0, columnspan=4, pady=GUIStyles.PADDING['small']
        )

        # 初始化位置控件状态
        self._on_position_type_changed(None)

    def _create_conditions_page(self):
        """创建执行条件页面"""
        self.conditions_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.conditions_frame, text="执行条件")

        main_frame = ttk.Frame(self.conditions_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=GUIStyles.PADDING['medium'],
                       pady=GUIStyles.PADDING['medium'])

        # 时间范围
        time_frame = ttk.LabelFrame(main_frame, text="执行时间范围")
        time_frame.pack(fill=tk.X, pady=GUIStyles.PADDING['small'])

        ttk.Label(time_frame, text="开始时间:").grid(row=0, column=0, sticky=tk.W,
                                                  padx=GUIStyles.PADDING['small'],
                                                  pady=GUIStyles.PADDING['small'])
        self.start_time_var = tk.StringVar(value="00:00")
        ttk.Entry(time_frame, textvariable=self.start_time_var, width=10).grid(
            row=0, column=1, padx=GUIStyles.PADDING['small'], pady=GUIStyles.PADDING['small']
        )

        ttk.Label(time_frame, text="结束时间:").grid(row=0, column=2, sticky=tk.W,
                                                  padx=GUIStyles.PADDING['small'],
                                                  pady=GUIStyles.PADDING['small'])
        self.end_time_var = tk.StringVar(value="23:59")
        ttk.Entry(time_frame, textvariable=self.end_time_var, width=10).grid(
            row=0, column=3, padx=GUIStyles.PADDING['small'], pady=GUIStyles.PADDING['small']
        )

        # 冷却时间
        cooldown_frame = ttk.LabelFrame(main_frame, text="冷却设置")
        cooldown_frame.pack(fill=tk.X, pady=GUIStyles.PADDING['small'])

        ttk.Label(cooldown_frame, text="冷却时间(秒):").grid(row=0, column=0, sticky=tk.W,
                                                         padx=GUIStyles.PADDING['small'],
                                                         pady=GUIStyles.PADDING['small'])
        self.cooldown_var = tk.IntVar(value=3600)
        ttk.Spinbox(
            cooldown_frame,
            from_=0,
            to=86400,
            textvariable=self.cooldown_var,
            width=10
        ).grid(row=0, column=1, padx=GUIStyles.PADDING['small'], pady=GUIStyles.PADDING['small'])

        ttk.Label(cooldown_frame, text="每日最大执行次数:").grid(row=0, column=2, sticky=tk.W,
                                                           padx=GUIStyles.PADDING['small'],
                                                           pady=GUIStyles.PADDING['small'])
        self.max_executions_var = tk.IntVar(value=1)
        ttk.Spinbox(
            cooldown_frame,
            from_=0,
            to=100,
            textvariable=self.max_executions_var,
            width=10
        ).grid(row=0, column=3, padx=GUIStyles.PADDING['small'], pady=GUIStyles.PADDING['small'])

    def _create_preview_page(self):
        """创建预览页面"""
        self.preview_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.preview_frame, text="预览")

        # 工具栏
        toolbar = ttk.Frame(self.preview_frame)
        toolbar.pack(fill=tk.X, padx=GUIStyles.PADDING['medium'],
                    pady=GUIStyles.PADDING['small'])

        ttk.Button(toolbar, text="刷新预览",
                  command=self._refresh_preview).pack(side=tk.LEFT,
                                                    padx=(0, GUIStyles.PADDING['small']))
        ttk.Button(toolbar, text="验证配置",
                  command=self._validate_config).pack(side=tk.LEFT)

        # JSON预览
        preview_text_frame = ttk.Frame(self.preview_frame)
        preview_text_frame.pack(fill=tk.BOTH, expand=True,
                              padx=GUIStyles.PADDING['medium'])

        self.preview_text = tk.Text(
            preview_text_frame,
            font=GUIStyles.FONTS['code'],
            wrap=tk.NONE
        )

        # 滚动条
        preview_scroll_y = ttk.Scrollbar(preview_text_frame, orient=tk.VERTICAL,
                                       command=self.preview_text.yview)
        preview_scroll_x = ttk.Scrollbar(preview_text_frame, orient=tk.HORIZONTAL,
                                       command=self.preview_text.xview)

        self.preview_text.configure(
            yscrollcommand=preview_scroll_y.set,
            xscrollcommand=preview_scroll_x.set
        )

        self.preview_text.grid(row=0, column=0, sticky='nsew')
        preview_scroll_y.grid(row=0, column=1, sticky='ns')
        preview_scroll_x.grid(row=1, column=0, sticky='ew')

        preview_text_frame.grid_rowconfigure(0, weight=1)
        preview_text_frame.grid_columnconfigure(0, weight=1)

    def _create_button_bar(self):
        """创建按钮栏"""
        self.button_frame = ttk.Frame(self.dialog)

        # 保存按钮
        self.save_btn = ttk.Button(
            self.button_frame,
            text="保存",
            command=self._save_task
        )
        self.save_btn.pack(side=tk.RIGHT, padx=(GUIStyles.PADDING['small'], 0))

        # 取消按钮
        self.cancel_btn = ttk.Button(
            self.button_frame,
            text="取消",
            command=self._cancel
        )
        self.cancel_btn.pack(side=tk.RIGHT, padx=(GUIStyles.PADDING['small'], GUIStyles.PADDING['small']))

        # 应用按钮
        self.apply_btn = ttk.Button(
            self.button_frame,
            text="应用",
            command=self._apply_task
        )
        self.apply_btn.pack(side=tk.RIGHT, padx=(GUIStyles.PADDING['small'], GUIStyles.PADDING['small']))

    def _setup_layout(self):
        """设置布局"""
        self.notebook.pack(fill=tk.BOTH, expand=True,
                          padx=GUIStyles.PADDING['medium'],
                          pady=GUIStyles.PADDING['medium'])

        self.button_frame.pack(fill=tk.X,
                              padx=GUIStyles.PADDING['medium'],
                              pady=GUIStyles.PADDING['small'])

    def _bind_events(self):
        """绑定事件"""
        # 窗口关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._cancel)

        # 笔记本页面切换事件
        self.notebook.bind('<<NotebookTabChanged>>', self._on_tab_changed)

    def _load_task_data(self):
        """加载任务数据"""
        if self.task_id and self.task_loader:
            task_info = self.task_loader.get_task(self.task_id)
            if task_info:
                self.task_config = copy.deepcopy(task_info.config)
                self._update_ui_from_config()
        else:
            # 新建任务，使用默认配置
            self._update_ui_from_config()

    def _update_ui_from_config(self):
        """从配置更新UI"""
        # 基本信息
        task_info = self.task_config.get('task_info', {})
        self.name_var.set(task_info.get('name', ''))
        self.description_text.delete('1.0', tk.END)
        self.description_text.insert('1.0', task_info.get('description', ''))
        self.enabled_var.set(task_info.get('enabled', True))
        self.priority_var.set(task_info.get('priority', 1))

        # 触发器
        self._refresh_triggers_list()

        # 动作
        self._refresh_actions_list()

        # 执行条件
        conditions = self.task_config.get('conditions', {})
        time_range = conditions.get('time_range', {})
        self.start_time_var.set(time_range.get('start', '00:00'))
        self.end_time_var.set(time_range.get('end', '23:59'))
        self.cooldown_var.set(conditions.get('cooldown', 3600))
        self.max_executions_var.set(conditions.get('max_executions_per_day', 1))

        # 刷新预览
        self._refresh_preview()

    def _update_config_from_ui(self):
        """从UI更新配置"""
        # 基本信息
        self.task_config['task_info'].update({
            'name': self.name_var.get(),
            'description': self.description_text.get('1.0', tk.END).strip(),
            'enabled': self.enabled_var.get(),
            'priority': self.priority_var.get()
        })

        # 执行条件
        self.task_config['conditions'].update({
            'time_range': {
                'start': self.start_time_var.get(),
                'end': self.end_time_var.get()
            },
            'cooldown': self.cooldown_var.get(),
            'max_executions_per_day': self.max_executions_var.get()
        })

    def _on_tab_changed(self, event):
        """页面切换事件"""
        selected_tab = self.notebook.select()
        tab_text = self.notebook.tab(selected_tab, "text")

        if tab_text == "预览":
            self._refresh_preview()

    def _on_trigger_selected(self, event):
        """触发器选择事件"""
        selected = self.triggers_tree.selection()
        if selected:
            trigger_id = selected[0]
            trigger = self._find_trigger_by_id(trigger_id)
            if trigger:
                self._load_trigger_to_ui(trigger)

    def _on_action_selected(self, event):
        """动作选择事件"""
        selected = self.actions_tree.selection()
        if selected:
            action_id = selected[0]
            action = self._find_action_by_id(action_id)
            if action:
                self._load_action_to_ui(action)

    def _on_action_type_changed(self, event):
        """动作类型改变事件"""
        action_type = self.action_type_var.get()

        # 根据动作类型显示/隐藏相关控件
        if action_type == "click":
            self.position_frame.grid()
        elif action_type in ["wait", "key", "text"]:
            self.position_frame.grid_remove()
        elif action_type == "wait_for_text":
            self.position_frame.grid()

    def _on_position_type_changed(self, event):
        """位置类型改变事件"""
        position_type = self.position_type_var.get()

        if position_type == "auto":
            # 自动定位，显示目标文字输入框
            self.action_target_text_entry.config(state=tk.NORMAL)
            self.position_x_entry.config(state=tk.DISABLED)
            self.position_y_entry.config(state=tk.DISABLED)
        else:
            # 固定坐标，显示坐标输入框
            self.action_target_text_entry.config(state=tk.DISABLED)
            self.position_x_entry.config(state=tk.NORMAL)
            self.position_y_entry.config(state=tk.NORMAL)

    def _add_trigger(self):
        """添加触发器"""
        trigger_id = f"trigger_{len(self.task_config['triggers']) + 1}"

        new_trigger = {
            "id": trigger_id,
            "type": "text_recognition",
            "target_text": "目标文字",
            "confidence": 0.8,
            "region": {
                "x": 0,
                "y": 0,
                "width": 1920,
                "height": 1080
            }
        }

        self.task_config['triggers'].append(new_trigger)
        self._refresh_triggers_list()

        # 选择新添加的触发器
        self.triggers_tree.selection_set(trigger_id)
        self._load_trigger_to_ui(new_trigger)

    def _remove_trigger(self):
        """删除触发器"""
        selected = self.triggers_tree.selection()
        if selected:
            trigger_id = selected[0]
            self.task_config['triggers'] = [
                t for t in self.task_config['triggers']
                if t.get('id') != trigger_id
            ]
            self._refresh_triggers_list()
            self._clear_trigger_ui()

    def _update_trigger(self):
        """更新触发器"""
        selected = self.triggers_tree.selection()
        if selected:
            trigger_id = selected[0]
            trigger = self._find_trigger_by_id(trigger_id)
            if trigger:
                trigger.update({
                    "id": self.trigger_id_var.get(),
                    "type": self.trigger_type_var.get(),
                    "target_text": self.trigger_text_var.get(),
                    "confidence": self.trigger_confidence_var.get(),
                    "region": {
                        "x": self.region_x_var.get(),
                        "y": self.region_y_var.get(),
                        "width": self.region_width_var.get(),
                        "height": self.region_height_var.get()
                    }
                })
                self._refresh_triggers_list()
                show_info(self.dialog, "成功", "触发器已更新")

    def _add_action(self):
        """添加动作"""
        action_id = f"action_{len(self.task_config['actions']) + 1}"

        new_action = {
            "id": action_id,
            "type": "click",
            "position": {
                "type": "auto",
                "offset_x": 0,
                "offset_y": 0
            },
            "timing": {
                "delay_before": 1.0,
                "delay_after": 2.0
            }
        }

        self.task_config['actions'].append(new_action)
        self._refresh_actions_list()

        # 选择新添加的动作
        self.actions_tree.selection_set(action_id)
        self._load_action_to_ui(new_action)

    def _remove_action(self):
        """删除动作"""
        selected = self.actions_tree.selection()
        if selected:
            action_id = selected[0]
            self.task_config['actions'] = [
                a for a in self.task_config['actions']
                if a.get('id') != action_id
            ]
            self._refresh_actions_list()
            self._clear_action_ui()

    def _update_action(self):
        """更新动作"""
        selected = self.actions_tree.selection()
        if selected:
            action_id = selected[0]
            action = self._find_action_by_id(action_id)
            if action:
                # 更新基本信息
                action.update({
                    "id": self.action_id_var.get(),
                    "type": self.action_type_var.get(),
                    "timing": {
                        "delay_before": self.delay_before_var.get(),
                        "delay_after": self.delay_after_var.get()
                    }
                })

                # 更新位置信息
                if self.action_type_var.get() == "click":
                    if self.position_type_var.get() == "auto":
                        action["position"] = {
                            "type": "auto",
                            "target_text": self.action_target_text_var.get()
                        }
                    else:
                        action["position"] = {
                            "type": "fixed",
                            "x": self.position_x_var.get(),
                            "y": self.position_y_var.get()
                        }

                self._refresh_actions_list()
                show_info(self.dialog, "成功", "动作已更新")

    def _refresh_triggers_list(self):
        """刷新触发器列表"""
        # 清空现有项目
        for item in self.triggers_tree.get_children():
            self.triggers_tree.delete(item)

        # 添加触发器
        for trigger in self.task_config.get('triggers', []):
            trigger_id = trigger.get('id', '')
            self.triggers_tree.insert(
                '',
                'end',
                iid=trigger_id,
                text=trigger_id,
                values=(
                    trigger.get('type', ''),
                    trigger.get('target_text', ''),
                    trigger.get('confidence', '')
                )
            )

    def _refresh_actions_list(self):
        """刷新动作列表"""
        # 清空现有项目
        for item in self.actions_tree.get_children():
            self.actions_tree.delete(item)

        # 添加动作
        for action in self.task_config.get('actions', []):
            action_id = action.get('id', '')
            action_type = action.get('type', '')

            # 生成描述
            description = self._generate_action_description(action)

            # 获取延时信息
            timing = action.get('timing', {})
            delay_info = f"{timing.get('delay_before', 0)}/{timing.get('delay_after', 0)}"

            self.actions_tree.insert(
                '',
                'end',
                iid=action_id,
                text=action_id,
                values=(action_type, description, delay_info)
            )

    def _generate_action_description(self, action: Dict[str, Any]) -> str:
        """生成动作描述"""
        action_type = action.get('type', '')

        if action_type == "click":
            position = action.get('position', {})
            if position.get('type') == 'auto':
                return f"点击文字: {position.get('target_text', '')}"
            else:
                return f"点击坐标: ({position.get('x', 0)}, {position.get('y', 0)})"
        elif action_type == "wait":
            delay = action.get('delay', 1.0)
            return f"等待 {delay} 秒"
        elif action_type == "key":
            keys = action.get('keys', '')
            return f"按键: {keys}"
        elif action_type == "text":
            text = action.get('text', '')
            return f"输入文字: {text[:20]}..."
        elif action_type == "wait_for_text":
            target_text = action.get('target_text', '')
            return f"等待文字: {target_text}"
        else:
            return action_type

    def _find_trigger_by_id(self, trigger_id: str) -> Optional[Dict[str, Any]]:
        """根据ID查找触发器"""
        for trigger in self.task_config.get('triggers', []):
            if trigger.get('id') == trigger_id:
                return trigger
        return None

    def _find_action_by_id(self, action_id: str) -> Optional[Dict[str, Any]]:
        """根据ID查找动作"""
        for action in self.task_config.get('actions', []):
            if action.get('id') == action_id:
                return action
        return None

    def _load_trigger_to_ui(self, trigger: Dict[str, Any]):
        """将触发器数据加载到UI"""
        self.trigger_id_var.set(trigger.get('id', ''))
        self.trigger_type_var.set(trigger.get('type', 'text_recognition'))
        self.trigger_text_var.set(trigger.get('target_text', ''))
        self.trigger_confidence_var.set(trigger.get('confidence', 0.8))

        region = trigger.get('region', {})
        self.region_x_var.set(region.get('x', 0))
        self.region_y_var.set(region.get('y', 0))
        self.region_width_var.set(region.get('width', 1920))
        self.region_height_var.set(region.get('height', 1080))

    def _load_action_to_ui(self, action: Dict[str, Any]):
        """将动作数据加载到UI"""
        self.action_id_var.set(action.get('id', ''))
        self.action_type_var.set(action.get('type', 'click'))

        # 加载位置信息
        position = action.get('position', {})
        position_type = position.get('type', 'auto')
        self.position_type_var.set(position_type)

        if position_type == 'auto':
            self.action_target_text_var.set(position.get('target_text', ''))
        else:
            self.position_x_var.set(position.get('x', 0))
            self.position_y_var.set(position.get('y', 0))

        # 加载延时信息
        timing = action.get('timing', {})
        self.delay_before_var.set(timing.get('delay_before', 1.0))
        self.delay_after_var.set(timing.get('delay_after', 2.0))

        # 更新UI状态
        self._on_action_type_changed(None)
        self._on_position_type_changed(None)

    def _clear_trigger_ui(self):
        """清空触发器UI"""
        self.trigger_id_var.set('')
        self.trigger_type_var.set('text_recognition')
        self.trigger_text_var.set('')
        self.trigger_confidence_var.set(0.8)
        self.region_x_var.set(0)
        self.region_y_var.set(0)
        self.region_width_var.set(1920)
        self.region_height_var.set(1080)

    def _clear_action_ui(self):
        """清空动作UI"""
        self.action_id_var.set('')
        self.action_type_var.set('click')
        self.position_type_var.set('auto')
        self.action_target_text_var.set('')
        self.position_x_var.set(0)
        self.position_y_var.set(0)
        self.delay_before_var.set(1.0)
        self.delay_after_var.set(2.0)

    def _refresh_preview(self):
        """刷新预览"""
        self._update_config_from_ui()

        try:
            json_str = json.dumps(self.task_config, ensure_ascii=False, indent=2)

            self.preview_text.config(state=tk.NORMAL)
            self.preview_text.delete('1.0', tk.END)
            self.preview_text.insert('1.0', json_str)
            self.preview_text.config(state=tk.DISABLED)

        except Exception as e:
            self.preview_text.config(state=tk.NORMAL)
            self.preview_text.delete('1.0', tk.END)
            self.preview_text.insert('1.0', f"预览生成失败: {e}")
            self.preview_text.config(state=tk.DISABLED)

    def _validate_config(self):
        """验证配置"""
        self._update_config_from_ui()

        is_valid, errors = validate_json_config(self.task_config)

        if is_valid:
            show_info(self.dialog, "验证成功", "任务配置验证通过！")
        else:
            error_msg = "配置验证失败:\n\n" + "\n".join(f"• {error}" for error in errors)
            show_error(self.dialog, "验证失败", error_msg)

    def _save_task(self):
        """保存任务"""
        if self._apply_task():
            self.result = "save"
            self.dialog.destroy()

    def _apply_task(self):
        """应用任务"""
        self._update_config_from_ui()

        # 验证配置
        is_valid, errors = validate_json_config(self.task_config)
        if not is_valid:
            error_msg = "配置验证失败:\n\n" + "\n".join(f"• {error}" for error in errors)
            show_error(self.dialog, "验证失败", error_msg)
            return False

        # 检查任务名称
        task_name = self.task_config['task_info']['name'].strip()
        if not task_name:
            show_error(self.dialog, "错误", "请输入任务名称")
            return False

        # 保存任务
        if self.task_loader:
            try:
                # 确定任务ID
                if self.task_id:
                    save_task_id = self.task_id
                else:
                    # 新建任务，生成ID
                    save_task_id = task_name.lower().replace(' ', '_').replace('　', '_')
                    # 确保ID唯一
                    counter = 1
                    original_id = save_task_id
                    while self.task_loader.get_task(save_task_id):
                        save_task_id = f"{original_id}_{counter}"
                        counter += 1

                # 保存任务
                if self.task_loader.save_task(save_task_id, self.task_config):
                    show_info(self.dialog, "成功", f"任务已保存: {save_task_id}")
                    self.task_id = save_task_id
                    return True
                else:
                    show_error(self.dialog, "错误", "保存任务失败")
                    return False

            except Exception as e:
                show_error(self.dialog, "错误", f"保存任务时出错: {e}")
                return False
        else:
            show_error(self.dialog, "错误", "任务加载器未初始化")
            return False

    def _cancel(self):
        """取消编辑"""
        if self._has_unsaved_changes():
            if ask_yes_no(self.dialog, "确认", "有未保存的更改，确定要取消吗？"):
                self.result = "cancel"
                self.dialog.destroy()
        else:
            self.result = "cancel"
            self.dialog.destroy()

    def _has_unsaved_changes(self) -> bool:
        """检查是否有未保存的更改"""
        # 简单实现：总是返回True，实际应该比较当前配置和原始配置
        return True

    def show_modal(self) -> Optional[str]:
        """显示模态对话框"""
        self.dialog.wait_window()
        return self.result


def show_task_editor(parent: tk.Widget, task_loader=None, task_id: Optional[str] = None) -> Optional[str]:
    """显示任务编辑器对话框"""
    editor = TaskEditorDialog(parent, task_loader, task_id)
    return editor.show_modal()
