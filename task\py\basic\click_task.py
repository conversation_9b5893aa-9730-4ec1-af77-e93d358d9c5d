# -*- coding: utf-8 -*-
"""
点击任务实现
基础的鼠标点击操作
"""

import time
import pyautogui
from typing import Dict, Any, Tuple

from ...base_task import BaseTask, TaskResult, TaskStatus


class ClickTask(BaseTask):
    """点击任务类"""
    
    def execute(self) -> TaskResult:
        """
        执行点击操作
        
        Returns:
            任务执行结果
        """
        try:
            params = self.config.parameters
            
            # 获取点击位置
            position = self._get_click_position(params.get('position', {}))
            if not position:
                return TaskResult(
                    task_id=self.task_id,
                    status=TaskStatus.FAILED,
                    message="无法确定点击位置"
                )
            
            x, y = position
            
            # 执行前延迟
            delay_before = params.get('delay_before', 0.5)
            if delay_before > 0:
                time.sleep(delay_before)
            
            # 执行点击
            click_type = params.get('click_type', 'left')
            double_click = params.get('double_click', False)
            
            if double_click:
                pyautogui.doubleClick(x, y, button=click_type)
            else:
                pyautogui.click(x, y, button=click_type)
            
            # 执行后延迟
            delay_after = params.get('delay_after', 0.5)
            if delay_after > 0:
                time.sleep(delay_after)
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.SUCCESS,
                message=f"成功点击位置 ({x}, {y})",
                data={'position': {'x': x, 'y': y}, 'click_type': click_type}
            )
            
        except Exception as e:
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                message=f"点击操作失败: {str(e)}"
            )
    
    def _get_click_position(self, position_config: Dict[str, Any]) -> Tuple[int, int]:
        """
        获取点击位置
        
        Args:
            position_config: 位置配置
            
        Returns:
            点击坐标 (x, y)，如果无法确定则返回 None
        """
        position_type = position_config.get('type', 'absolute')
        
        if position_type == 'absolute':
            # 绝对坐标
            x = position_config.get('x', 0)
            y = position_config.get('y', 0)
            return (x, y)
        
        elif position_type == 'relative':
            # 相对于屏幕的百分比坐标
            screen_width, screen_height = pyautogui.size()
            x_percent = position_config.get('x_percent', 0.5)
            y_percent = position_config.get('y_percent', 0.5)
            x = int(screen_width * x_percent)
            y = int(screen_height * y_percent)
            return (x, y)
        
        elif position_type == 'auto':
            # 自动识别位置（需要结合触发器）
            # 这里可以集成OCR识别结果
            return self._get_auto_position()
        
        return None
    
    def _get_auto_position(self) -> Tuple[int, int]:
        """
        自动获取点击位置
        通常结合OCR识别结果
        
        Returns:
            点击坐标
        """
        # 这里可以集成现有的OCR系统
        # 暂时返回屏幕中心
        screen_width, screen_height = pyautogui.size()
        return (screen_width // 2, screen_height // 2)
    
    def validate_config(self) -> bool:
        """验证任务配置"""
        if not super().validate_config():
            return False
        
        params = self.config.parameters
        position = params.get('position', {})
        
        # 验证位置配置
        position_type = position.get('type', 'absolute')
        if position_type == 'absolute':
            if 'x' not in position or 'y' not in position:
                return False
        elif position_type == 'relative':
            if 'x_percent' not in position or 'y_percent' not in position:
                return False
        
        return True
