# -*- coding: utf-8 -*-
"""
任务流程管理器
管理任务执行流程
"""

import json
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass
import uuid
from datetime import datetime

from .task_library import TaskLibrary, TaskTemplate


@dataclass
class WorkflowStep:
    """工作流步骤数据类"""
    id: str
    task_id: str
    task_name: str
    order: int
    enabled: bool
    delay_before: float
    delay_after: float
    continue_on_error: bool
    parameters: Dict[str, Any]


@dataclass
class TaskWorkflow:
    """任务工作流数据类"""
    id: str
    name: str
    description: str
    enabled: bool
    steps: List[WorkflowStep]
    created_time: str
    modified_time: str


class WorkflowManager:
    """任务流程管理器类"""
    
    def __init__(self, task_library: Optional[TaskLibrary] = None,
                 workflow_dir: str = "config/workflows"):
        """
        初始化工作流管理器
        
        Args:
            task_library: 任务库实例
            workflow_dir: 工作流目录
        """
        self.task_library = task_library or TaskLibrary()
        self.workflow_dir = Path(workflow_dir)
        self.workflows = {}
        
        # 确保工作流目录存在
        self.workflow_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载所有工作流
        self.load_all_workflows()
    
    def load_all_workflows(self) -> Dict[str, TaskWorkflow]:
        """
        加载所有工作流
        
        Returns:
            工作流字典
        """
        self.workflows.clear()
        
        # 从工作流目录加载
        for workflow_file in self.workflow_dir.glob("*.json"):
            try:
                workflow = self._load_workflow_from_file(workflow_file)
                if workflow:
                    self.workflows[workflow.id] = workflow
                    print(f"工作流已加载: {workflow.id} - {workflow.name}")
            except Exception as e:
                print(f"加载工作流失败: {workflow_file} - {e}")
        
        print(f"共加载 {len(self.workflows)} 个工作流")
        return self.workflows
    
    def _load_workflow_from_file(self, file_path: Path) -> Optional[TaskWorkflow]:
        """
        从文件加载工作流
        
        Args:
            file_path: 文件路径
            
        Returns:
            工作流对象
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 验证必需字段
            required_fields = ['id', 'name', 'steps']
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 解析步骤
            steps = []
            for step_data in data['steps']:
                step = WorkflowStep(
                    id=step_data['id'],
                    task_id=step_data['task_id'],
                    task_name=step_data.get('task_name', ''),
                    order=step_data.get('order', 0),
                    enabled=step_data.get('enabled', True),
                    delay_before=step_data.get('delay_before', 0.0),
                    delay_after=step_data.get('delay_after', 1.0),
                    continue_on_error=step_data.get('continue_on_error', True),
                    parameters=step_data.get('parameters', {})
                )
                steps.append(step)
            
            # 按顺序排序
            steps.sort(key=lambda x: x.order)
            
            return TaskWorkflow(
                id=data['id'],
                name=data['name'],
                description=data.get('description', ''),
                enabled=data.get('enabled', True),
                steps=steps,
                created_time=data.get('created_time', ''),
                modified_time=data.get('modified_time', '')
            )
            
        except Exception as e:
            print(f"解析工作流文件失败: {file_path} - {e}")
            return None
    
    def get_workflow(self, workflow_id: str) -> Optional[TaskWorkflow]:
        """
        获取指定工作流
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            工作流对象
        """
        return self.workflows.get(workflow_id)
    
    def get_all_workflows(self) -> Dict[str, TaskWorkflow]:
        """获取所有工作流"""
        return self.workflows.copy()
    
    def get_enabled_workflows(self) -> Dict[str, TaskWorkflow]:
        """获取所有启用的工作流"""
        return {
            workflow_id: workflow 
            for workflow_id, workflow in self.workflows.items() 
            if workflow.enabled
        }
    
    def create_workflow(self, name: str, description: str = "") -> TaskWorkflow:
        """
        创建新工作流
        
        Args:
            name: 工作流名称
            description: 工作流描述
            
        Returns:
            新工作流对象
        """
        workflow_id = str(uuid.uuid4())[:8]  # 使用短UUID
        current_time = datetime.now().isoformat()
        
        workflow = TaskWorkflow(
            id=workflow_id,
            name=name,
            description=description,
            enabled=True,
            steps=[],
            created_time=current_time,
            modified_time=current_time
        )
        
        return workflow
    
    def save_workflow(self, workflow: TaskWorkflow) -> bool:
        """
        保存工作流
        
        Args:
            workflow: 工作流对象
            
        Returns:
            保存是否成功
        """
        try:
            # 更新修改时间
            workflow.modified_time = datetime.now().isoformat()
            
            # 构建保存数据
            save_data = {
                'id': workflow.id,
                'name': workflow.name,
                'description': workflow.description,
                'enabled': workflow.enabled,
                'steps': [
                    {
                        'id': step.id,
                        'task_id': step.task_id,
                        'task_name': step.task_name,
                        'order': step.order,
                        'enabled': step.enabled,
                        'delay_before': step.delay_before,
                        'delay_after': step.delay_after,
                        'continue_on_error': step.continue_on_error,
                        'parameters': step.parameters
                    }
                    for step in workflow.steps
                ],
                'created_time': workflow.created_time,
                'modified_time': workflow.modified_time
            }
            
            # 保存到文件
            workflow_file = self.workflow_dir / f"{workflow.id}.json"
            with open(workflow_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            # 更新内存中的工作流
            self.workflows[workflow.id] = workflow
            
            print(f"工作流已保存: {workflow.id}")
            return True
            
        except Exception as e:
            print(f"保存工作流失败: {workflow.id} - {e}")
            return False
    
    def delete_workflow(self, workflow_id: str) -> bool:
        """
        删除工作流
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            删除是否成功
        """
        try:
            # 从内存中删除
            if workflow_id in self.workflows:
                del self.workflows[workflow_id]
            
            # 删除文件
            workflow_file = self.workflow_dir / f"{workflow_id}.json"
            if workflow_file.exists():
                workflow_file.unlink()
            
            print(f"工作流已删除: {workflow_id}")
            return True
            
        except Exception as e:
            print(f"删除工作流失败: {workflow_id} - {e}")
            return False
    
    def add_step(self, workflow_id: str, task_id: str, 
                position: Optional[int] = None) -> bool:
        """
        向工作流添加步骤
        
        Args:
            workflow_id: 工作流ID
            task_id: 任务ID
            position: 插入位置（None表示添加到末尾）
            
        Returns:
            添加是否成功
        """
        workflow = self.workflows.get(workflow_id)
        if not workflow:
            print(f"工作流不存在: {workflow_id}")
            return False
        
        # 获取任务信息
        task_template = self.task_library.get_task(task_id)
        if not task_template:
            print(f"任务不存在: {task_id}")
            return False
        
        # 创建新步骤
        step_id = str(uuid.uuid4())[:8]
        
        if position is None:
            order = len(workflow.steps)
        else:
            order = position
            # 调整后续步骤的顺序
            for step in workflow.steps:
                if step.order >= order:
                    step.order += 1
        
        new_step = WorkflowStep(
            id=step_id,
            task_id=task_id,
            task_name=task_template.name,
            order=order,
            enabled=True,
            delay_before=0.0,
            delay_after=1.0,
            continue_on_error=True,
            parameters={}
        )
        
        workflow.steps.append(new_step)
        workflow.steps.sort(key=lambda x: x.order)
        
        return self.save_workflow(workflow)
    
    def remove_step(self, workflow_id: str, step_id: str) -> bool:
        """
        从工作流移除步骤
        
        Args:
            workflow_id: 工作流ID
            step_id: 步骤ID
            
        Returns:
            移除是否成功
        """
        workflow = self.workflows.get(workflow_id)
        if not workflow:
            print(f"工作流不存在: {workflow_id}")
            return False
        
        # 查找并移除步骤
        step_to_remove = None
        for step in workflow.steps:
            if step.id == step_id:
                step_to_remove = step
                break
        
        if not step_to_remove:
            print(f"步骤不存在: {step_id}")
            return False
        
        workflow.steps.remove(step_to_remove)
        
        # 重新排序
        for i, step in enumerate(workflow.steps):
            step.order = i
        
        return self.save_workflow(workflow)
    
    def move_step(self, workflow_id: str, step_id: str, new_position: int) -> bool:
        """
        移动工作流步骤
        
        Args:
            workflow_id: 工作流ID
            step_id: 步骤ID
            new_position: 新位置
            
        Returns:
            移动是否成功
        """
        workflow = self.workflows.get(workflow_id)
        if not workflow:
            print(f"工作流不存在: {workflow_id}")
            return False
        
        # 查找步骤
        step_to_move = None
        for step in workflow.steps:
            if step.id == step_id:
                step_to_move = step
                break
        
        if not step_to_move:
            print(f"步骤不存在: {step_id}")
            return False
        
        # 移除步骤
        workflow.steps.remove(step_to_move)
        
        # 插入到新位置
        workflow.steps.insert(new_position, step_to_move)
        
        # 重新排序
        for i, step in enumerate(workflow.steps):
            step.order = i
        
        return self.save_workflow(workflow)
    
    def get_workflow_summary(self) -> Dict[str, Any]:
        """
        获取工作流摘要信息
        
        Returns:
            工作流摘要
        """
        total_workflows = len(self.workflows)
        enabled_workflows = len(self.get_enabled_workflows())
        disabled_workflows = total_workflows - enabled_workflows
        
        return {
            "total_workflows": total_workflows,
            "enabled_workflows": enabled_workflows,
            "disabled_workflows": disabled_workflows,
            "workflow_list": [
                {
                    "id": workflow.id,
                    "name": workflow.name,
                    "enabled": workflow.enabled,
                    "step_count": len(workflow.steps)
                }
                for workflow in self.workflows.values()
            ]
        }


if __name__ == "__main__":
    # 测试工作流管理器
    print("测试工作流管理器...")
    
    try:
        workflow_manager = WorkflowManager()
        
        # 获取工作流摘要
        summary = workflow_manager.get_workflow_summary()
        print(f"工作流摘要: {summary}")
        
        print("工作流管理器测试完成")
        
    except Exception as e:
        print(f"工作流管理器测试失败: {e}")
