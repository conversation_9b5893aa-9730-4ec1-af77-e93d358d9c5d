{"id": "daily_login", "name": "每日登录", "description": "自动完成每日登录任务", "category": "daily", "enabled": true, "priority": 1, "config": {"task_info": {"name": "每日登录", "description": "自动完成每日登录任务", "version": "0_0001", "enabled": true, "priority": 1}, "triggers": [{"id": "login_button", "type": "text_recognition", "target_text": "登录", "confidence": 0.8, "region": {"x": 0, "y": 0, "width": 1920, "height": 1080}}], "actions": [{"id": "click_login", "type": "click", "position": {"type": "auto", "target_text": "登录"}, "timing": {"delay_before": 1.0, "delay_after": 2.0}}], "behaviors": [{"id": "login_click_behavior", "type": "click", "enabled": true, "delay_before": 0.5, "delay_after": 1.0, "parameters": {"click_type": "left", "position": null, "offset_x": 0, "offset_y": 0}}], "flow_control": {"execution_order": ["login_button"], "loop_enabled": false, "loop_count": 1, "continue_on_error": true}, "conditions": {"time_range": {"start": "06:00", "end": "23:59"}, "cooldown": 86400, "max_executions_per_day": 1}}, "created_time": "2025-07-11T15:43:00", "modified_time": "2025-07-11T15:43:00"}