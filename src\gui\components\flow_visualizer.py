# -*- coding: utf-8 -*-
"""
任务流可视化组件
提供任务流的图形化显示和编辑功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import math
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

# 导入任务模块
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from task.task_flow import TaskFlow, FlowStep
    from task.base_task import BaseTask, TaskStatus
    TASK_MODULE_AVAILABLE = True
except ImportError:
    TASK_MODULE_AVAILABLE = False


@dataclass
class NodePosition:
    """节点位置"""
    x: float
    y: float
    width: float = 120
    height: float = 60


@dataclass
class FlowNode:
    """流程节点"""
    step_id: str
    task_name: str
    position: NodePosition
    status: str = "pending"
    enabled: bool = True


@dataclass
class FlowConnection:
    """流程连接"""
    from_node: str
    to_node: str
    connection_type: str = "sequence"  # sequence, condition, parallel


class FlowVisualizerCanvas(tk.Canvas):
    """任务流可视化画布"""
    
    def __init__(self, parent, **kwargs):
        """初始化画布"""
        super().__init__(parent, bg='white', **kwargs)
        
        # 流程数据
        self.task_flow: Optional[TaskFlow] = None
        self.nodes: Dict[str, FlowNode] = {}
        self.connections: List[FlowConnection] = []
        
        # 画布状态
        self.scale = 1.0
        self.offset_x = 0
        self.offset_y = 0
        self.selected_node = None
        self.dragging_node = None
        self.drag_start_pos = None
        
        # 颜色配置
        self.colors = {
            'node_normal': '#E3F2FD',
            'node_selected': '#BBDEFB',
            'node_running': '#FFF3E0',
            'node_success': '#E8F5E8',
            'node_failed': '#FFEBEE',
            'node_disabled': '#F5F5F5',
            'border_normal': '#2196F3',
            'border_selected': '#1976D2',
            'border_disabled': '#BDBDBD',
            'text_normal': '#212121',
            'text_disabled': '#9E9E9E',
            'connection': '#757575',
            'connection_active': '#4CAF50'
        }
        
        # 绑定事件
        self.bind('<Button-1>', self._on_click)
        self.bind('<B1-Motion>', self._on_drag)
        self.bind('<ButtonRelease-1>', self._on_release)
        self.bind('<Double-Button-1>', self._on_double_click)
        self.bind('<Button-3>', self._on_right_click)
        self.bind('<MouseWheel>', self._on_scroll)
        
        # 创建右键菜单
        self._create_context_menu()
    
    def _create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="编辑节点", command=self._edit_node)
        self.context_menu.add_command(label="删除节点", command=self._delete_node)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="启用/禁用", command=self._toggle_node)
        self.context_menu.add_command(label="重置状态", command=self._reset_node_status)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="自动布局", command=self._auto_layout)
    
    def set_task_flow(self, task_flow: TaskFlow):
        """设置要显示的任务流"""
        self.task_flow = task_flow
        self._build_nodes_from_flow()
        self._auto_layout()
        self.redraw()
    
    def _build_nodes_from_flow(self):
        """从任务流构建节点"""
        if not self.task_flow:
            return
        
        self.nodes.clear()
        self.connections.clear()
        
        # 创建节点
        for i, step in enumerate(self.task_flow.steps):
            node = FlowNode(
                step_id=step.id,
                task_name=step.task.task_name,
                position=NodePosition(x=100 + i * 150, y=100),
                status="pending",
                enabled=step.enabled
            )
            self.nodes[step.id] = node
        
        # 创建连接（按顺序连接）
        sorted_steps = sorted(self.task_flow.steps, key=lambda s: s.order)
        for i in range(len(sorted_steps) - 1):
            connection = FlowConnection(
                from_node=sorted_steps[i].id,
                to_node=sorted_steps[i + 1].id
            )
            self.connections.append(connection)
    
    def _auto_layout(self):
        """自动布局节点"""
        if not self.nodes:
            return
        
        # 简单的网格布局
        nodes_per_row = max(1, int(math.sqrt(len(self.nodes))))
        
        for i, (node_id, node) in enumerate(self.nodes.items()):
            row = i // nodes_per_row
            col = i % nodes_per_row
            
            node.position.x = 50 + col * 180
            node.position.y = 50 + row * 120
    
    def redraw(self):
        """重绘画布"""
        self.delete("all")
        
        if not self.task_flow:
            self._draw_empty_state()
            return
        
        # 绘制连接线
        self._draw_connections()
        
        # 绘制节点
        self._draw_nodes()
        
        # 绘制网格（可选）
        # self._draw_grid()
    
    def _draw_empty_state(self):
        """绘制空状态"""
        canvas_width = self.winfo_width()
        canvas_height = self.winfo_height()
        
        if canvas_width > 1 and canvas_height > 1:
            self.create_text(
                canvas_width // 2, canvas_height // 2,
                text="拖拽任务到此处创建任务流",
                font=("Arial", 14),
                fill="#9E9E9E"
            )
    
    def _draw_connections(self):
        """绘制连接线"""
        for connection in self.connections:
            from_node = self.nodes.get(connection.from_node)
            to_node = self.nodes.get(connection.to_node)
            
            if not from_node or not to_node:
                continue
            
            # 计算连接点
            from_x = from_node.position.x + from_node.position.width
            from_y = from_node.position.y + from_node.position.height // 2
            
            to_x = to_node.position.x
            to_y = to_node.position.y + to_node.position.height // 2
            
            # 绘制箭头线
            self._draw_arrow(from_x, from_y, to_x, to_y, connection.connection_type)
    
    def _draw_arrow(self, x1: float, y1: float, x2: float, y2: float, connection_type: str):
        """绘制箭头"""
        # 计算箭头参数
        arrow_length = 10
        arrow_angle = math.pi / 6
        
        # 线条颜色
        color = self.colors['connection']
        if connection_type == "active":
            color = self.colors['connection_active']
        
        # 绘制主线
        self.create_line(x1, y1, x2, y2, fill=color, width=2, tags="connection")
        
        # 计算箭头方向
        angle = math.atan2(y2 - y1, x2 - x1)
        
        # 绘制箭头头部
        arrow_x1 = x2 - arrow_length * math.cos(angle - arrow_angle)
        arrow_y1 = y2 - arrow_length * math.sin(angle - arrow_angle)
        arrow_x2 = x2 - arrow_length * math.cos(angle + arrow_angle)
        arrow_y2 = y2 - arrow_length * math.sin(angle + arrow_angle)
        
        self.create_line(x2, y2, arrow_x1, arrow_y1, fill=color, width=2, tags="connection")
        self.create_line(x2, y2, arrow_x2, arrow_y2, fill=color, width=2, tags="connection")
    
    def _draw_nodes(self):
        """绘制节点"""
        for node_id, node in self.nodes.items():
            self._draw_node(node)
    
    def _draw_node(self, node: FlowNode):
        """绘制单个节点"""
        x, y = node.position.x, node.position.y
        w, h = node.position.width, node.position.height
        
        # 确定颜色
        if not node.enabled:
            fill_color = self.colors['node_disabled']
            border_color = self.colors['border_disabled']
            text_color = self.colors['text_disabled']
        elif node.step_id == self.selected_node:
            fill_color = self.colors['node_selected']
            border_color = self.colors['border_selected']
            text_color = self.colors['text_normal']
        elif node.status == "running":
            fill_color = self.colors['node_running']
            border_color = self.colors['border_normal']
            text_color = self.colors['text_normal']
        elif node.status == "success":
            fill_color = self.colors['node_success']
            border_color = self.colors['border_normal']
            text_color = self.colors['text_normal']
        elif node.status == "failed":
            fill_color = self.colors['node_failed']
            border_color = self.colors['border_normal']
            text_color = self.colors['text_normal']
        else:
            fill_color = self.colors['node_normal']
            border_color = self.colors['border_normal']
            text_color = self.colors['text_normal']
        
        # 绘制节点矩形
        rect_id = self.create_rectangle(
            x, y, x + w, y + h,
            fill=fill_color,
            outline=border_color,
            width=2,
            tags=("node", node.step_id)
        )
        
        # 绘制节点文本
        text_id = self.create_text(
            x + w // 2, y + h // 2,
            text=node.task_name,
            font=("Arial", 10),
            fill=text_color,
            width=w - 10,
            tags=("node", node.step_id)
        )
        
        # 绘制状态指示器
        if node.status != "pending":
            indicator_size = 8
            indicator_x = x + w - indicator_size - 5
            indicator_y = y + 5
            
            indicator_color = {
                "running": "#FF9800",
                "success": "#4CAF50",
                "failed": "#F44336"
            }.get(node.status, "#9E9E9E")
            
            self.create_oval(
                indicator_x, indicator_y,
                indicator_x + indicator_size, indicator_y + indicator_size,
                fill=indicator_color,
                outline="",
                tags=("node", node.step_id)
            )
    
    def _on_click(self, event):
        """鼠标点击事件"""
        try:
            closest_items = self.find_closest(event.x, event.y)
            if not closest_items:
                # 点击了空白区域
                self.selected_node = None
                self.redraw()
                return

            clicked_item = closest_items[0]
            tags = self.gettags(clicked_item)
        except (IndexError, tk.TclError):
            # 点击了空白区域或发生错误
            self.selected_node = None
            self.redraw()
            return
        
        if "node" in tags:
            # 点击了节点
            node_id = [tag for tag in tags if tag != "node"][0]
            self.selected_node = node_id
            self.dragging_node = node_id
            self.drag_start_pos = (event.x, event.y)
            self.redraw()
        else:
            # 点击了空白区域
            self.selected_node = None
            self.redraw()
    
    def _on_drag(self, event):
        """鼠标拖拽事件"""
        if self.dragging_node and self.drag_start_pos:
            node = self.nodes.get(self.dragging_node)
            if node:
                dx = event.x - self.drag_start_pos[0]
                dy = event.y - self.drag_start_pos[1]
                
                node.position.x += dx
                node.position.y += dy
                
                self.drag_start_pos = (event.x, event.y)
                self.redraw()
    
    def _on_release(self, event):
        """鼠标释放事件"""
        self.dragging_node = None
        self.drag_start_pos = None
    
    def _on_double_click(self, event):
        """双击事件"""
        if self.selected_node:
            self._edit_node()
    
    def _on_right_click(self, event):
        """右键点击事件"""
        clicked_item = self.find_closest(event.x, event.y)[0]
        tags = self.gettags(clicked_item)
        
        if "node" in tags:
            node_id = [tag for tag in tags if tag != "node"][0]
            self.selected_node = node_id
            self.redraw()
            
            # 显示右键菜单
            try:
                self.context_menu.post(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()
    
    def _on_scroll(self, event):
        """鼠标滚轮事件（缩放）"""
        # 简单的缩放实现
        if event.delta > 0:
            self.scale *= 1.1
        else:
            self.scale *= 0.9
        
        self.scale = max(0.1, min(3.0, self.scale))
        # 这里可以实现实际的缩放逻辑
    
    def _edit_node(self):
        """编辑节点"""
        if self.selected_node:
            messagebox.showinfo("编辑节点", f"编辑节点: {self.selected_node}")
    
    def _delete_node(self):
        """删除节点"""
        if self.selected_node:
            if messagebox.askyesno("确认删除", f"确定要删除节点 {self.selected_node} 吗？"):
                # 从任务流中删除步骤
                if self.task_flow:
                    self.task_flow.remove_task(self.selected_node)
                
                # 重新构建节点
                self._build_nodes_from_flow()
                self.selected_node = None
                self.redraw()
    
    def _toggle_node(self):
        """切换节点启用状态"""
        if self.selected_node and self.task_flow:
            for step in self.task_flow.steps:
                if step.id == self.selected_node:
                    step.enabled = not step.enabled
                    break
            
            self._build_nodes_from_flow()
            self.redraw()
    
    def _reset_node_status(self):
        """重置节点状态"""
        if self.selected_node:
            node = self.nodes.get(self.selected_node)
            if node:
                node.status = "pending"
                self.redraw()
    
    def update_node_status(self, step_id: str, status: str):
        """更新节点状态"""
        node = self.nodes.get(step_id)
        if node:
            node.status = status
            self.redraw()
    
    def highlight_current_step(self, step_id: str):
        """高亮当前执行步骤"""
        # 重置所有节点状态
        for node in self.nodes.values():
            if node.status == "running":
                node.status = "pending"
        
        # 设置当前步骤为运行状态
        current_node = self.nodes.get(step_id)
        if current_node:
            current_node.status = "running"
        
        self.redraw()


class FlowVisualizerWidget(ttk.Frame):
    """任务流可视化组件"""
    
    def __init__(self, parent, **kwargs):
        """初始化组件"""
        super().__init__(parent, **kwargs)
        
        self._create_widgets()
        self._setup_layout()
    
    def _create_widgets(self):
        """创建子组件"""
        # 工具栏
        self.toolbar = ttk.Frame(self)
        
        ttk.Button(self.toolbar, text="自动布局", command=self._auto_layout).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="重置视图", command=self._reset_view).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="导出图片", command=self._export_image).pack(side=tk.LEFT, padx=2)
        
        # 画布容器
        self.canvas_frame = ttk.Frame(self)
        
        # 创建画布和滚动条
        self.canvas = FlowVisualizerCanvas(self.canvas_frame, width=800, height=600)
        
        self.h_scrollbar = ttk.Scrollbar(self.canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.v_scrollbar = ttk.Scrollbar(self.canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=self.h_scrollbar.set, yscrollcommand=self.v_scrollbar.set)
    
    def _setup_layout(self):
        """设置布局"""
        self.toolbar.pack(fill=tk.X, padx=5, pady=5)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # 画布和滚动条布局
        self.canvas.grid(row=0, column=0, sticky="nsew")
        self.h_scrollbar.grid(row=1, column=0, sticky="ew")
        self.v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        self.canvas_frame.grid_rowconfigure(0, weight=1)
        self.canvas_frame.grid_columnconfigure(0, weight=1)
    
    def set_task_flow(self, task_flow: TaskFlow):
        """设置要显示的任务流"""
        self.canvas.set_task_flow(task_flow)
    
    def _auto_layout(self):
        """自动布局"""
        self.canvas._auto_layout()
        self.canvas.redraw()
    
    def _reset_view(self):
        """重置视图"""
        self.canvas.scale = 1.0
        self.canvas.offset_x = 0
        self.canvas.offset_y = 0
        self.canvas.redraw()
    
    def _export_image(self):
        """导出图片"""
        messagebox.showinfo("导出图片", "导出图片功能开发中")
