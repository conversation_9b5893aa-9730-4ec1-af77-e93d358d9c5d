# -*- coding: utf-8 -*-
"""
基础任务类
定义所有任务的基础接口和通用功能
"""

import json
import time
import uuid
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    SUCCESS = "success"      # 执行成功
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消
    TIMEOUT = "timeout"      # 执行超时


@dataclass
class TaskResult:
    """任务执行结果"""
    task_id: str
    status: TaskStatus
    message: str
    data: Optional[Dict[str, Any]] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None


@dataclass
class TaskConfig:
    """任务配置数据类"""
    id: str
    name: str
    description: str
    category: str
    enabled: bool
    priority: int
    timeout: float
    retry_count: int
    retry_delay: float
    parameters: Dict[str, Any]


class BaseTask(ABC):
    """
    基础任务抽象类
    所有具体任务都应继承此类
    """
    
    def __init__(self, config: TaskConfig):
        """
        初始化基础任务
        
        Args:
            config: 任务配置
        """
        self.config = config
        self.status = TaskStatus.PENDING
        self.result = None
        self.start_time = None
        self.end_time = None
        self.logger = None  # 将在子类中设置
        
    @property
    def task_id(self) -> str:
        """获取任务ID"""
        return self.config.id
        
    @property
    def task_name(self) -> str:
        """获取任务名称"""
        return self.config.name
        
    @abstractmethod
    def execute(self) -> TaskResult:
        """
        执行任务的抽象方法
        子类必须实现此方法
        
        Returns:
            任务执行结果
        """
        pass
    
    def run(self) -> TaskResult:
        """
        运行任务（包含重试逻辑）
        
        Returns:
            任务执行结果
        """
        if not self.config.enabled:
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.CANCELLED,
                message="任务已禁用"
            )
        
        # 执行任务（包含重试逻辑）
        for attempt in range(self.config.retry_count + 1):
            try:
                self.status = TaskStatus.RUNNING
                self.start_time = datetime.now()
                
                # 执行具体任务
                result = self.execute()
                
                self.end_time = datetime.now()
                result.start_time = self.start_time
                result.end_time = self.end_time
                result.duration = (self.end_time - self.start_time).total_seconds()
                
                self.status = result.status
                self.result = result
                
                if result.status == TaskStatus.SUCCESS:
                    return result
                    
            except Exception as e:
                self.end_time = datetime.now()
                error_result = TaskResult(
                    task_id=self.task_id,
                    status=TaskStatus.FAILED,
                    message=f"任务执行异常: {str(e)}",
                    start_time=self.start_time,
                    end_time=self.end_time,
                    duration=(self.end_time - self.start_time).total_seconds() if self.start_time else 0
                )
                
                if attempt < self.config.retry_count:
                    if self.logger:
                        self.logger.warning(f"任务 {self.task_name} 第 {attempt + 1} 次执行失败，{self.config.retry_delay}秒后重试")
                    time.sleep(self.config.retry_delay)
                    continue
                else:
                    self.status = TaskStatus.FAILED
                    self.result = error_result
                    return error_result
        
        # 不应该到达这里
        return TaskResult(
            task_id=self.task_id,
            status=TaskStatus.FAILED,
            message="未知错误"
        )
    
    def validate_config(self) -> bool:
        """
        验证任务配置
        
        Returns:
            配置是否有效
        """
        required_fields = ['id', 'name', 'category']
        for field in required_fields:
            if not hasattr(self.config, field) or not getattr(self.config, field):
                return False
        return True
    
    def get_status(self) -> TaskStatus:
        """获取任务状态"""
        return self.status
    
    def get_result(self) -> Optional[TaskResult]:
        """获取任务结果"""
        return self.result
    
    def to_dict(self) -> Dict[str, Any]:
        """将任务转换为字典格式"""
        return {
            'id': self.config.id,
            'name': self.config.name,
            'description': self.config.description,
            'category': self.config.category,
            'status': self.status.value,
            'enabled': self.config.enabled,
            'priority': self.config.priority
        }
    
    @classmethod
    def from_json_file(cls, json_file: str, py_module: str):
        """
        从JSON配置文件和Python模块创建任务实例
        
        Args:
            json_file: JSON配置文件路径
            py_module: Python实现模块路径
            
        Returns:
            任务实例
        """
        # 加载JSON配置
        with open(json_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 创建任务配置
        config = TaskConfig(
            id=config_data.get('id', str(uuid.uuid4())),
            name=config_data.get('name', ''),
            description=config_data.get('description', ''),
            category=config_data.get('category', 'default'),
            enabled=config_data.get('enabled', True),
            priority=config_data.get('priority', 1),
            timeout=config_data.get('timeout', 30.0),
            retry_count=config_data.get('retry_count', 3),
            retry_delay=config_data.get('retry_delay', 1.0),
            parameters=config_data.get('parameters', {})
        )
        
        # 动态导入Python模块并创建实例
        import importlib.util
        spec = importlib.util.spec_from_file_location("task_module", py_module)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 假设模块中有一个与文件名同名的类
        task_class = getattr(module, config_data.get('class_name', 'Task'))
        return task_class(config)


def create_task_config(name: str, description: str = "", category: str = "default",
                      enabled: bool = True, priority: int = 1, timeout: float = 30.0,
                      retry_count: int = 3, retry_delay: float = 1.0,
                      parameters: Optional[Dict[str, Any]] = None) -> TaskConfig:
    """
    创建任务配置的便捷函数
    
    Args:
        name: 任务名称
        description: 任务描述
        category: 任务分类
        enabled: 是否启用
        priority: 优先级
        timeout: 超时时间
        retry_count: 重试次数
        retry_delay: 重试延迟
        parameters: 任务参数
        
    Returns:
        任务配置对象
    """
    return TaskConfig(
        id=str(uuid.uuid4()),
        name=name,
        description=description,
        category=category,
        enabled=enabled,
        priority=priority,
        timeout=timeout,
        retry_count=retry_count,
        retry_delay=retry_delay,
        parameters=parameters or {}
    )
