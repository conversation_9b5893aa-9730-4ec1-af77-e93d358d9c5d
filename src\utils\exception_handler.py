# -*- coding: utf-8 -*-
"""
异常处理模块
提供统一的异常处理和错误恢复机制
"""

import sys
import traceback
import functools
from typing import Optional, Callable, Any, Dict
from datetime import datetime
from enum import Enum

from .logger import get_logger


class ErrorLevel(Enum):
    """错误级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SnowZoneException(Exception):
    """SnowZone项目基础异常类"""
    
    def __init__(self, message: str, error_code: str = "UNKNOWN", 
                 level: ErrorLevel = ErrorLevel.MEDIUM, details: Optional[Dict] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.level = level
        self.details = details or {}
        self.timestamp = datetime.now()


class WindowCaptureException(SnowZoneException):
    """窗口捕获异常"""
    
    def __init__(self, message: str, details: Optional[Dict] = None):
        super().__init__(message, "WINDOW_CAPTURE_ERROR", ErrorLevel.HIGH, details)


class OCRException(SnowZoneException):
    """OCR识别异常"""
    
    def __init__(self, message: str, details: Optional[Dict] = None):
        super().__init__(message, "OCR_ERROR", ErrorLevel.MEDIUM, details)


class ActionExecutionException(SnowZoneException):
    """动作执行异常"""
    
    def __init__(self, message: str, details: Optional[Dict] = None):
        super().__init__(message, "ACTION_ERROR", ErrorLevel.HIGH, details)


class ConfigurationException(SnowZoneException):
    """配置异常"""
    
    def __init__(self, message: str, details: Optional[Dict] = None):
        super().__init__(message, "CONFIG_ERROR", ErrorLevel.CRITICAL, details)


class TaskSchedulerException(SnowZoneException):
    """任务调度异常"""
    
    def __init__(self, message: str, details: Optional[Dict] = None):
        super().__init__(message, "SCHEDULER_ERROR", ErrorLevel.HIGH, details)


class ExceptionHandler:
    """异常处理器类"""
    
    def __init__(self, logger=None):
        """
        初始化异常处理器
        
        Args:
            logger: 日志器实例
        """
        self.logger = logger or get_logger()
        self.error_stats = {
            "total_errors": 0,
            "errors_by_type": {},
            "errors_by_level": {},
            "last_error_time": None
        }
        
        # 错误恢复策略
        self.recovery_strategies = {
            "WINDOW_CAPTURE_ERROR": self._recover_window_capture,
            "OCR_ERROR": self._recover_ocr,
            "ACTION_ERROR": self._recover_action,
            "CONFIG_ERROR": self._recover_config,
            "SCHEDULER_ERROR": self._recover_scheduler
        }
    
    def handle_exception(self, exception: Exception, context: Optional[Dict] = None) -> bool:
        """
        处理异常
        
        Args:
            exception: 异常对象
            context: 上下文信息
            
        Returns:
            是否成功恢复
        """
        # 更新错误统计
        self._update_error_stats(exception)
        
        # 记录异常
        self._log_exception(exception, context)
        
        # 尝试恢复
        if isinstance(exception, SnowZoneException):
            return self._attempt_recovery(exception, context)
        
        return False
    
    def _update_error_stats(self, exception: Exception):
        """更新错误统计"""
        self.error_stats["total_errors"] += 1
        self.error_stats["last_error_time"] = datetime.now()
        
        # 按类型统计
        error_type = type(exception).__name__
        self.error_stats["errors_by_type"][error_type] = \
            self.error_stats["errors_by_type"].get(error_type, 0) + 1
        
        # 按级别统计
        if isinstance(exception, SnowZoneException):
            level = exception.level.value
            self.error_stats["errors_by_level"][level] = \
                self.error_stats["errors_by_level"].get(level, 0) + 1
    
    def _log_exception(self, exception: Exception, context: Optional[Dict] = None):
        """记录异常信息"""
        error_msg = str(exception)
        error_type = type(exception).__name__

        if isinstance(exception, SnowZoneException):
            # 根据错误级别选择日志级别
            if exception.level == ErrorLevel.CRITICAL:
                self.logger.critical(f"严重错误[{exception.error_code}]: {error_msg}")
            elif exception.level == ErrorLevel.HIGH:
                self.logger.error(f"高级错误[{exception.error_code}]: {error_msg}")
            elif exception.level == ErrorLevel.MEDIUM:
                self.logger.warning(f"中级错误[{exception.error_code}]: {error_msg}")
            else:
                self.logger.info(f"低级错误[{exception.error_code}]: {error_msg}")
        else:
            self.logger.error(f"未处理异常[{error_type}]: {error_msg}")

        # 记录详细信息到调试日志
        if context:
            self.logger.debug(f"异常上下文: {context}")

        # 记录堆栈跟踪到调试日志
        self.logger.debug(f"堆栈跟踪:\n{traceback.format_exc()}")
    
    def _attempt_recovery(self, exception: SnowZoneException, context: Optional[Dict] = None) -> bool:
        """
        尝试错误恢复
        
        Args:
            exception: SnowZone异常
            context: 上下文信息
            
        Returns:
            是否成功恢复
        """
        recovery_func = self.recovery_strategies.get(exception.error_code)
        
        if recovery_func:
            try:
                self.logger.info(f"尝试恢复错误: {exception.error_code}")
                success = recovery_func(exception, context)
                
                if success:
                    self.logger.info(f"错误恢复成功: {exception.error_code}")
                else:
                    self.logger.warning(f"错误恢复失败: {exception.error_code}")
                
                return success
                
            except Exception as recovery_error:
                self.logger.error(f"恢复过程中发生异常: {recovery_error}")
                return False
        
        return False
    
    def _recover_window_capture(self, exception: WindowCaptureException, context: Optional[Dict] = None) -> bool:
        """恢复窗口捕获错误"""
        # 实现窗口捕获恢复逻辑
        # 例如：重新搜索窗口、重启捕获组件等
        return False
    
    def _recover_ocr(self, exception: OCRException, context: Optional[Dict] = None) -> bool:
        """恢复OCR错误"""
        # 实现OCR恢复逻辑
        # 例如：重新初始化OCR引擎、调整识别参数等
        return False
    
    def _recover_action(self, exception: ActionExecutionException, context: Optional[Dict] = None) -> bool:
        """恢复动作执行错误"""
        # 实现动作执行恢复逻辑
        # 例如：重试动作、调整执行参数等
        return False
    
    def _recover_config(self, exception: ConfigurationException, context: Optional[Dict] = None) -> bool:
        """恢复配置错误"""
        # 实现配置恢复逻辑
        # 例如：重新加载配置、使用默认配置等
        return False
    
    def _recover_scheduler(self, exception: TaskSchedulerException, context: Optional[Dict] = None) -> bool:
        """恢复调度器错误"""
        # 实现调度器恢复逻辑
        # 例如：重启调度器、清理任务队列等
        return False
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return self.error_stats.copy()
    
    def reset_error_stats(self):
        """重置错误统计"""
        self.error_stats = {
            "total_errors": 0,
            "errors_by_type": {},
            "errors_by_level": {},
            "last_error_time": None
        }
        self.logger.info("错误统计已重置")


def exception_handler(logger=None, reraise: bool = False):
    """
    异常处理装饰器
    
    Args:
        logger: 日志器实例
        reraise: 是否重新抛出异常
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            handler = ExceptionHandler(logger)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 处理异常
                recovered = handler.handle_exception(e, {
                    "function": func.__name__,
                    "args": str(args)[:100],  # 限制长度
                    "kwargs": str(kwargs)[:100]
                })
                
                if reraise or not recovered:
                    raise
                
                return None
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return=None, **kwargs) -> Any:
    """
    安全执行函数
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 异常时的默认返回值
        **kwargs: 函数关键字参数
        
    Returns:
        函数执行结果或默认值
    """
    handler = ExceptionHandler()
    
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handler.handle_exception(e, {
            "function": func.__name__ if hasattr(func, '__name__') else str(func),
            "safe_execute": True
        })
        return default_return


class GlobalExceptionHandler:
    """全局异常处理器"""
    
    def __init__(self):
        self.handler = ExceptionHandler()
        self.original_excepthook = sys.excepthook
    
    def install(self):
        """安装全局异常处理器"""
        sys.excepthook = self._handle_exception
        print("全局异常处理器已安装")
    
    def uninstall(self):
        """卸载全局异常处理器"""
        sys.excepthook = self.original_excepthook
        print("全局异常处理器已卸载")
    
    def _handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许键盘中断正常退出
            self.original_excepthook(exc_type, exc_value, exc_traceback)
            return
        
        # 处理其他异常
        self.handler.handle_exception(exc_value, {
            "global_handler": True,
            "exc_type": exc_type.__name__
        })
        
        # 调用原始异常处理器
        self.original_excepthook(exc_type, exc_value, exc_traceback)


# 全局异常处理器实例
global_exception_handler = GlobalExceptionHandler()


if __name__ == "__main__":
    # 测试异常处理功能
    print("测试异常处理功能...")
    
    handler = ExceptionHandler()
    
    # 测试自定义异常
    try:
        raise OCRException("测试OCR异常", {"confidence": 0.3})
    except Exception as e:
        handler.handle_exception(e)
    
    # 测试装饰器
    @exception_handler()
    def test_function():
        raise ValueError("测试异常")
    
    test_function()
    
    # 显示错误统计
    stats = handler.get_error_stats()
    print(f"错误统计: {stats}")
    
    print("异常处理测试完成")
