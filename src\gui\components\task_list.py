# -*- coding: utf-8 -*-
"""
任务列表组件
显示和管理任务列表
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional, Callable, Any
from ..utils.gui_helpers import GUIStyles, create_tooltip, show_error, ask_yes_no


class TaskListWidget(ttk.Frame):
    """任务列表控件"""
    
    def __init__(self, parent: tk.Widget, task_loader=None, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.task_loader = task_loader
        self.on_task_selected = None
        self.on_task_edited = None
        self.on_task_deleted = None
        self.on_task_toggled = None
        
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        # 刷新任务列表
        self.refresh_tasks()
    
    def _create_widgets(self):
        """创建控件"""
        # 工具栏
        self.toolbar = ttk.Frame(self)
        
        # 刷新按钮
        self.refresh_btn = ttk.Button(
            self.toolbar,
            text="刷新",
            command=self.refresh_tasks
        )
        create_tooltip(self.refresh_btn, "刷新任务列表")
        
        # 新建按钮
        self.new_btn = ttk.Button(
            self.toolbar,
            text="新建任务",
            command=self._on_new_task
        )
        create_tooltip(self.new_btn, "创建新任务")
        
        # 编辑按钮
        self.edit_btn = ttk.Button(
            self.toolbar,
            text="编辑",
            command=self._on_edit_task,
            state=tk.DISABLED
        )
        create_tooltip(self.edit_btn, "编辑选中的任务")
        
        # 删除按钮
        self.delete_btn = ttk.Button(
            self.toolbar,
            text="删除",
            command=self._on_delete_task,
            state=tk.DISABLED
        )
        create_tooltip(self.delete_btn, "删除选中的任务")
        
        # 启用/禁用按钮
        self.toggle_btn = ttk.Button(
            self.toolbar,
            text="启用/禁用",
            command=self._on_toggle_task,
            state=tk.DISABLED
        )
        create_tooltip(self.toggle_btn, "切换任务启用状态")
        
        # 任务列表
        self.tree_frame = ttk.Frame(self)
        
        # 创建Treeview
        columns = ('name', 'status', 'priority', 'description')
        self.tree = ttk.Treeview(
            self.tree_frame,
            columns=columns,
            show='tree headings',
            selectmode='extended'
        )
        
        # 设置列标题
        self.tree.heading('#0', text='ID')
        self.tree.heading('name', text='任务名称')
        self.tree.heading('status', text='状态')
        self.tree.heading('priority', text='优先级')
        self.tree.heading('description', text='描述')
        
        # 设置列宽
        self.tree.column('#0', width=120, minwidth=80)
        self.tree.column('name', width=150, minwidth=100)
        self.tree.column('status', width=80, minwidth=60)
        self.tree.column('priority', width=80, minwidth=60)
        self.tree.column('description', width=200, minwidth=150)
        
        # 滚动条
        self.tree_scroll = ttk.Scrollbar(
            self.tree_frame,
            orient=tk.VERTICAL,
            command=self.tree.yview
        )
        self.tree.configure(yscrollcommand=self.tree_scroll.set)
        
        # 状态栏
        self.status_frame = ttk.Frame(self)
        self.status_label = ttk.Label(
            self.status_frame,
            text="任务列表",
            font=GUIStyles.FONTS['small']
        )
    
    def _setup_layout(self):
        """设置布局"""
        # 工具栏布局
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        self.new_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        self.edit_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        self.delete_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        self.toggle_btn.pack(side=tk.LEFT, padx=(0, GUIStyles.PADDING['small']))
        
        # 主布局
        self.toolbar.pack(fill=tk.X, padx=GUIStyles.PADDING['medium'], 
                         pady=GUIStyles.PADDING['small'])
        
        # 任务列表布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, 
                           padx=GUIStyles.PADDING['medium'])
        
        # 状态栏布局
        self.status_label.pack(side=tk.LEFT, padx=GUIStyles.PADDING['small'])
        self.status_frame.pack(fill=tk.X, padx=GUIStyles.PADDING['medium'],
                              pady=GUIStyles.PADDING['small'])
    
    def _bind_events(self):
        """绑定事件"""
        self.tree.bind('<<TreeviewSelect>>', self._on_selection_changed)
        self.tree.bind('<Double-1>', self._on_double_click)
        self.tree.bind('<Button-3>', self._on_right_click)
    
    def _on_selection_changed(self, event):
        """选择改变事件"""
        selected = self.tree.selection()
        has_selection = len(selected) > 0
        
        # 更新按钮状态
        state = tk.NORMAL if has_selection else tk.DISABLED
        self.edit_btn.config(state=state)
        self.delete_btn.config(state=state)
        self.toggle_btn.config(state=state)
        
        # 触发回调
        if has_selection and self.on_task_selected:
            task_id = selected[0]
            self.on_task_selected(task_id)
    
    def _on_double_click(self, event):
        """双击事件"""
        self._on_edit_task()
    
    def _on_right_click(self, event):
        """右键点击事件"""
        # 选择点击的项目
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self._show_context_menu(event)
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        context_menu = tk.Menu(self, tearoff=0)
        
        selected = self.tree.selection()
        if selected:
            task_id = selected[0]
            task_info = self._get_task_info(task_id)
            
            if task_info:
                # 启用/禁用
                status_text = "禁用" if task_info.enabled else "启用"
                context_menu.add_command(
                    label=status_text,
                    command=self._on_toggle_task
                )
                
                context_menu.add_separator()
                
                # 编辑
                context_menu.add_command(
                    label="编辑",
                    command=self._on_edit_task
                )
                
                # 复制
                context_menu.add_command(
                    label="复制",
                    command=lambda: self._on_copy_task(task_id)
                )
                
                context_menu.add_separator()
                
                # 删除
                context_menu.add_command(
                    label="删除",
                    command=self._on_delete_task
                )
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def _on_new_task(self):
        """新建任务"""
        if self.on_task_edited:
            self.on_task_edited(None)  # None表示新建
    
    def _on_edit_task(self):
        """编辑任务"""
        selected = self.tree.selection()
        if selected and self.on_task_edited:
            task_id = selected[0]
            self.on_task_edited(task_id)
    
    def _on_delete_task(self):
        """删除任务"""
        selected = self.tree.selection()
        if not selected:
            return
        
        task_id = selected[0]
        task_info = self._get_task_info(task_id)
        
        if task_info:
            if ask_yes_no(
                self,
                "确认删除",
                f"确定要删除任务 '{task_info.name}' 吗？\n此操作不可撤销。"
            ):
                if self.on_task_deleted:
                    self.on_task_deleted(task_id)
                self.refresh_tasks()
    
    def _on_toggle_task(self):
        """切换任务状态"""
        selected = self.tree.selection()
        if selected and self.on_task_toggled:
            task_id = selected[0]
            self.on_task_toggled(task_id)
            self.refresh_tasks()
    
    def _on_copy_task(self, task_id: str):
        """复制任务"""
        if self.task_loader:
            task_info = self.task_loader.get_task(task_id)
            if task_info:
                # 创建副本
                new_id = f"{task_id}_copy"
                new_config = task_info.config.copy()
                new_config['task_info']['name'] = f"{task_info.name} - 副本"
                
                if self.task_loader.save_task(new_id, new_config):
                    self.refresh_tasks()
    
    def _get_task_info(self, task_id: str):
        """获取任务信息"""
        if self.task_loader:
            return self.task_loader.get_task(task_id)
        return None
    
    def refresh_tasks(self):
        """刷新任务列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.task_loader:
            self.status_label.config(text="未连接任务加载器")
            return
        
        try:
            # 获取所有任务
            tasks = self.task_loader.get_all_tasks()
            
            # 添加任务到列表
            for task_id, task_info in tasks.items():
                status = "启用" if task_info.enabled else "禁用"
                
                self.tree.insert(
                    '',
                    'end',
                    iid=task_id,
                    text=task_id,
                    values=(
                        task_info.name,
                        status,
                        task_info.priority,
                        task_info.description[:50] + "..." if len(task_info.description) > 50 else task_info.description
                    ),
                    tags=('enabled' if task_info.enabled else 'disabled',)
                )
            
            # 配置标签样式
            self.tree.tag_configure('enabled', foreground='black')
            self.tree.tag_configure('disabled', foreground='gray')
            
            # 更新状态
            self.status_label.config(text=f"共 {len(tasks)} 个任务")
            
        except Exception as e:
            show_error(self, "错误", f"刷新任务列表失败: {e}")
            self.status_label.config(text="刷新失败")
    
    def get_selected_task_id(self) -> Optional[str]:
        """获取选中的任务ID"""
        selected = self.tree.selection()
        return selected[0] if selected else None
    
    def select_task(self, task_id: str):
        """选择指定任务"""
        if task_id in [item for item in self.tree.get_children()]:
            self.tree.selection_set(task_id)
            self.tree.focus(task_id)
            self.tree.see(task_id)
    
    def set_task_loader(self, task_loader):
        """设置任务加载器"""
        self.task_loader = task_loader
        self.refresh_tasks()
    
    def set_callbacks(self, on_task_selected: Optional[Callable] = None,
                     on_task_edited: Optional[Callable] = None,
                     on_task_deleted: Optional[Callable] = None,
                     on_task_toggled: Optional[Callable] = None):
        """设置回调函数"""
        self.on_task_selected = on_task_selected
        self.on_task_edited = on_task_edited
        self.on_task_deleted = on_task_deleted
        self.on_task_toggled = on_task_toggled
