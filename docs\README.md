# SnowZone OCR自动化点击工具

## 项目简介

基于光学字符识别(OCR)技术的自动化点击工具，专为《尘白禁区》游戏设计。通过识别游戏界面中的特定文字内容，自动执行预定义的操作序列，提高游戏体验效率。

## 功能特性

- 🎯 **精准识别**: 基于Tesseract OCR引擎的中文文字识别
- 🎮 **游戏专用**: 专门针对《尘白禁区》游戏窗口优化
- ⚙️ **配置化**: 通过JSON文件灵活配置任务流程
- 📊 **版本管理**: 完整的版本控制和日志记录系统
- 🔧 **模块化**: 清晰的代码结构，易于维护和扩展
- 🛡️ **安全可靠**: 仅基于图像识别，不修改游戏文件

## 项目结构

```
SnowZone/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── 项目结构与实现流程.md    # 详细设计文档
├── config/                # 配置文件目录
│   ├── default_config.json # 默认配置
│   └── tasks/             # 任务配置
├── src/                   # 源代码目录
│   ├── core/              # 核心功能模块
│   ├── config/            # 配置管理
│   ├── utils/             # 工具模块
│   └── gui/               # 图形界面(可选)
├── logs/                  # 日志文件
└── tests/                 # 测试文件
```

## 快速开始

### 环境要求

- Python 3.8+
- Windows 操作系统
- 《尘白禁区》游戏客户端

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd SnowZone
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **安装Tesseract OCR**
   - 下载并安装 [Tesseract OCR](https://github.com/tesseract-ocr/tesseract)
   - 下载中文语言包 `chi_sim.traineddata`
   - 配置环境变量或在代码中指定路径

4. **运行程序**
   ```bash
   python main.py
   ```

### 命令行选项

```bash
python main.py                    # 正常启动
python main.py --debug            # 调试模式
python main.py --config custom.json  # 使用自定义配置
python main.py --version          # 显示版本信息
python main.py --no-gui           # 禁用图形界面
```

## 配置说明

### 主配置文件 (config/default_config.json)

```json
{
    "game_settings": {
        "window_title": "尘白禁区",
        "process_name": "game.exe",
        "capture_interval": 1.0
    },
    "ocr_settings": {
        "language": "chi_sim",
        "confidence_threshold": 0.7
    }
}
```

### 任务配置文件 (config/tasks/daily_task.json)

```json
{
    "task_info": {
        "name": "每日任务自动化",
        "description": "自动完成每日任务流程"
    },
    "triggers": [
        {
            "type": "text_recognition",
            "target_text": "每日任务",
            "confidence": 0.8
        }
    ],
    "actions": [
        {
            "type": "click",
            "position": {"type": "auto"}
        }
    ]
}
```

## 开发状态

### 当前版本: 0_0001

- [x] 项目架构设计
- [x] 基础目录结构
- [x] 配置管理系统
- [x] 日志记录功能
- [x] 版本管理系统
- [ ] OCR识别引擎 (开发中)
- [ ] 窗口捕获功能 (开发中)
- [ ] 自动化执行引擎 (计划中)
- [ ] 图形用户界面 (计划中)

### 开发路线图

- **0_0002**: OCR核心功能实现
- **0_0003**: 窗口捕获和图像处理
- **0_0004**: 自动化执行引擎
- **0_0005**: 配置管理完善
- **0_0006**: GUI界面开发
- **0_0007**: 性能优化
- **0_0008**: 功能测试
- **0_0009**: 文档完善
- **0_0010**: 正式发布

## 技术架构

### 核心模块

1. **窗口捕获模块**: 定位并捕获游戏窗口画面
2. **图像预处理模块**: 优化图像质量提高OCR识别率
3. **OCR识别引擎**: 执行文字识别并返回结果
4. **动作执行器**: 执行鼠标点击、键盘输入等操作
5. **配置管理器**: 加载和管理配置文件
6. **任务调度器**: 管理任务流程和执行顺序

### 技术栈

- **编程语言**: Python 3.8+
- **OCR引擎**: Tesseract + pytesseract
- **图像处理**: OpenCV
- **窗口操作**: pywin32
- **配置管理**: JSON
- **日志系统**: logging + colorlog

## 使用说明

### 基本使用流程

1. 启动《尘白禁区》游戏
2. 运行SnowZone工具
3. 工具自动检测游戏窗口
4. 根据配置的任务自动执行操作
5. 查看日志了解执行状态

### 自定义任务

1. 在 `config/tasks/` 目录下创建新的JSON配置文件
2. 定义触发条件和执行动作
3. 重启工具加载新配置

## 注意事项

### 使用声明

- 本工具仅供学习和研究目的使用
- 使用者需遵守游戏服务条款和相关法律法规
- 不得用于商业用途或违法活动

### 技术限制

- 仅支持Windows系统
- 需要游戏窗口可见且未被遮挡
- OCR识别准确率受图像质量影响
- 不支持游戏内部API调用

### 安全提示

- 建议在测试环境中先行验证
- 定期备份游戏存档
- 注意游戏更新可能影响工具兼容性

## 故障排除

### 常见问题

1. **OCR识别失败**
   - 检查Tesseract安装和语言包
   - 调整图像预处理参数
   - 提高置信度阈值

2. **窗口捕获失败**
   - 确认游戏窗口标题正确
   - 检查游戏是否以管理员权限运行
   - 尝试重启工具

3. **依赖包安装失败**
   - 使用国内镜像源
   - 检查Python版本兼容性
   - 手动安装问题包

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发环境设置

1. Fork项目到个人仓库
2. 创建开发分支
3. 安装开发依赖: `pip install -r requirements.txt`
4. 运行测试: `python -m pytest tests/`
5. 提交代码并创建Pull Request

### 代码规范

- 使用中文注释
- 遵循PEP 8代码风格
- 添加适当的类型提示
- 编写单元测试

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [Issues]
- 开发团队: SnowZone开发团队

---

**版本**: 0_0001  
**最后更新**: 2025-07-10  
**开发状态**: 积极开发中
