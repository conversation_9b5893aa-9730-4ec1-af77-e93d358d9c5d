{"template_id": "wait_task_basic", "name": "基础等待任务模板", "description": "用于创建基础等待任务的模板", "category": "basic", "schema": {"type": "object", "required": ["id", "name", "type", "parameters"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "const": "wait"}, "class_name": {"type": "string", "const": "WaitTask"}, "enabled": {"type": "boolean"}, "priority": {"type": "integer", "minimum": 1, "maximum": 10}, "timeout": {"type": "number", "minimum": 0.1}, "retry_count": {"type": "integer", "minimum": 0, "maximum": 10}, "retry_delay": {"type": "number", "minimum": 0}, "parameters": {"type": "object", "required": ["wait_type"], "properties": {"wait_type": {"type": "string", "enum": ["time", "text", "image"]}, "duration": {"type": "number", "minimum": 0}, "target_text": {"type": "string"}, "target_image": {"type": "string"}, "max_wait_time": {"type": "number", "minimum": 0.1}, "check_interval": {"type": "number", "minimum": 0.1}}}}}, "default_values": {"name": "新建等待任务", "description": "等待指定时间或条件", "type": "wait", "class_name": "WaitTask", "enabled": true, "priority": 1, "timeout": 60.0, "retry_count": 1, "retry_delay": 0.0, "parameters": {"wait_type": "time", "duration": 1.0, "target_text": "", "target_image": "", "max_wait_time": 30.0, "check_interval": 0.5}, "triggers": [], "validation": {"expected_result": "success", "timeout": 5.0}}, "created_time": "2025-07-11T18:00:00", "updated_time": "2025-07-11T18:00:00"}