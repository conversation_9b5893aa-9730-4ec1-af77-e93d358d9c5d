# -*- coding: utf-8 -*-
"""
任务模块集成接口
提供与现有SnowZone系统的集成功能
"""

import sys
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from .task_manager import TaskManager
from .base_task import BaseTask, TaskResult, TaskStatus
from .task_flow import TaskFlow, FlowResult, FlowStatus
from .workflow import Workflow, WorkflowResult, WorkflowStatus


class SnowZoneTaskIntegration:
    """
    SnowZone任务集成类
    提供与现有系统的集成接口
    """
    
    def __init__(self):
        """初始化集成接口"""
        self.task_manager = TaskManager()
        self.logger = None
        
        # 集成现有系统组件
        self.ocr_engine = None
        self.action_executor = None
        self.window_capture = None
        self.behavior_system = None
        
        # 初始化集成
        self._initialize_integration()
    
    def _initialize_integration(self):
        """初始化与现有系统的集成"""
        try:
            # 导入现有系统组件
            from src.core.ocr_engine import OCREngine
            from src.core.action_executor import ActionExecutor
            from src.core.window_capture import WindowCapture
            from src.core.behavior_system import BehaviorSystem
            from src.utils.logger import Logger
            
            # 初始化组件
            self.ocr_engine = OCREngine()
            self.action_executor = ActionExecutor()
            self.window_capture = WindowCapture()
            self.behavior_system = BehaviorSystem()
            self.logger = Logger().get_logger(__name__)
            
            # 设置任务管理器的日志
            self.task_manager.logger = self.logger
            
            if self.logger:
                self.logger.info("任务模块集成初始化成功")
                
        except ImportError as e:
            print(f"导入现有系统组件失败: {e}")
        except Exception as e:
            print(f"集成初始化失败: {e}")
    
    def get_task_manager(self) -> TaskManager:
        """获取任务管理器"""
        return self.task_manager
    
    def execute_task(self, task_id: str) -> Optional[TaskResult]:
        """
        执行单个任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务执行结果
        """
        task = self.task_manager.get_task(task_id)
        if not task:
            if self.logger:
                self.logger.error(f"未找到任务: {task_id}")
            return None
        
        # 为任务设置集成组件
        self._setup_task_integration(task)
        
        return task.run()
    
    def execute_task_flow(self, flow_id: str) -> Optional[FlowResult]:
        """
        执行任务流
        
        Args:
            flow_id: 任务流ID
            
        Returns:
            任务流执行结果
        """
        flow = self.task_manager.get_task_flow(flow_id)
        if not flow:
            if self.logger:
                self.logger.error(f"未找到任务流: {flow_id}")
            return None
        
        # 为任务流中的所有任务设置集成组件
        for step in flow.steps:
            self._setup_task_integration(step.task)
        
        flow.logger = self.logger
        return flow.run()
    
    def execute_workflow(self, workflow_id: str) -> Optional[WorkflowResult]:
        """
        执行工作流
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            工作流执行结果
        """
        workflow = self.task_manager.get_workflow(workflow_id)
        if not workflow:
            if self.logger:
                self.logger.error(f"未找到工作流: {workflow_id}")
            return None
        
        # 为工作流中的所有任务设置集成组件
        for step in workflow.steps:
            flow = step.flow
            for flow_step in flow.steps:
                self._setup_task_integration(flow_step.task)
        
        workflow.logger = self.logger
        return workflow.run()
    
    def _setup_task_integration(self, task: BaseTask):
        """
        为任务设置集成组件
        
        Args:
            task: 基础任务
        """
        # 设置日志
        task.logger = self.logger
        
        # 为特定类型的任务设置专用组件
        if hasattr(task, 'ocr_engine'):
            task.ocr_engine = self.ocr_engine
        
        if hasattr(task, 'action_executor'):
            task.action_executor = self.action_executor
        
        if hasattr(task, 'window_capture'):
            task.window_capture = self.window_capture
        
        if hasattr(task, 'behavior_system'):
            task.behavior_system = self.behavior_system
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        stats = self.task_manager.get_statistics()
        
        # 添加执行统计
        execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'running_tasks': 0
        }
        
        # 统计任务执行状态
        for task in self.task_manager.list_tasks():
            if task.result:
                execution_stats['total_executions'] += 1
                if task.result.status == TaskStatus.SUCCESS:
                    execution_stats['successful_executions'] += 1
                elif task.result.status == TaskStatus.FAILED:
                    execution_stats['failed_executions'] += 1
            
            if task.status == TaskStatus.RUNNING:
                execution_stats['running_tasks'] += 1
        
        stats.update(execution_stats)
        return stats
    
    def get_available_tasks(self, category: str = None) -> List[Dict[str, Any]]:
        """
        获取可用任务列表
        
        Args:
            category: 任务分类过滤
            
        Returns:
            任务信息列表
        """
        tasks = self.task_manager.list_tasks(category)
        return [task.to_dict() for task in tasks]
    
    def get_available_flows(self) -> List[Dict[str, Any]]:
        """获取可用任务流列表"""
        flows = self.task_manager.list_task_flows()
        return [flow.to_dict() for flow in flows]
    
    def get_available_workflows(self) -> List[Dict[str, Any]]:
        """获取可用工作流列表"""
        workflows = self.task_manager.list_workflows()
        return [workflow.to_dict() for workflow in workflows]
    
    def create_simple_flow(self, name: str, task_ids: List[str]) -> Optional[str]:
        """
        创建简单的任务流
        
        Args:
            name: 任务流名称
            task_ids: 任务ID列表
            
        Returns:
            创建的任务流ID
        """
        import uuid
        flow_id = f"flow_{uuid.uuid4().hex[:8]}"
        
        flow = self.task_manager.create_task_flow(
            flow_id=flow_id,
            name=name,
            description=f"由 {len(task_ids)} 个任务组成的流程"
        )
        
        for i, task_id in enumerate(task_ids):
            task = self.task_manager.get_task(task_id)
            if task:
                flow.add_task(task, order=i, delay_after=1.0)
            else:
                if self.logger:
                    self.logger.warning(f"创建任务流时未找到任务: {task_id}")
        
        return flow_id if len(flow.steps) > 0 else None
    
    def start_scheduled_workflow(self, workflow_id: str) -> bool:
        """
        启动调度工作流
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            是否启动成功
        """
        workflow = self.task_manager.get_workflow(workflow_id)
        if not workflow:
            return False
        
        # 设置集成组件
        for step in workflow.steps:
            flow = step.flow
            for flow_step in flow.steps:
                self._setup_task_integration(flow_step.task)
        
        workflow.logger = self.logger
        return workflow.start_scheduled_execution()
    
    def stop_workflow(self, workflow_id: str) -> bool:
        """
        停止工作流
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            是否停止成功
        """
        workflow = self.task_manager.get_workflow(workflow_id)
        if not workflow:
            return False
        
        workflow.stop()
        return True


# 全局集成实例
_integration_instance = None


def get_integration() -> SnowZoneTaskIntegration:
    """获取全局集成实例"""
    global _integration_instance
    if _integration_instance is None:
        _integration_instance = SnowZoneTaskIntegration()
    return _integration_instance
