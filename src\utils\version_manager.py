# -*- coding: utf-8 -*-
"""
版本管理器
负责版本控制、日志文件管理和版本信息记录
"""

import os
import json
import datetime
from typing import Dict, List, Optional


class VersionManager:
    """版本管理器类"""
    
    def __init__(self, base_path: str = "."):
        """
        初始化版本管理器
        
        Args:
            base_path: 项目根目录路径
        """
        self.base_path = base_path
        self.logs_dir = os.path.join(base_path, "logs")
        self.version_file = os.path.join(base_path, "version_info.json")
        self.current_version = "0_0001"
        
        # 确保日志目录存在
        os.makedirs(self.logs_dir, exist_ok=True)
        
        # 初始化版本信息
        self._initialize_version_info()
    
    def _initialize_version_info(self):
        """初始化版本信息文件"""
        if not os.path.exists(self.version_file):
            version_info = {
                "current_version": self.current_version,
                "created_date": datetime.datetime.now().isoformat(),
                "version_history": [
                    {
                        "version": "0_0001",
                        "date": datetime.datetime.now().isoformat(),
                        "description": "项目初始化和基础架构搭建",
                        "changes": [
                            "创建项目目录结构",
                            "实现版本管理系统",
                            "配置日志记录功能",
                            "建立基础配置文件"
                        ]
                    }
                ]
            }
            self._save_version_info(version_info)
    
    def get_current_version(self) -> str:
        """获取当前版本号"""
        version_info = self._load_version_info()
        return version_info.get("current_version", self.current_version)
    
    def get_version_history(self) -> List[Dict]:
        """获取版本历史记录"""
        version_info = self._load_version_info()
        return version_info.get("version_history", [])
    
    def create_new_version(self, description: str, changes: List[str]) -> str:
        """
        创建新版本
        
        Args:
            description: 版本描述
            changes: 变更列表
            
        Returns:
            新版本号
        """
        version_info = self._load_version_info()
        current_version = version_info.get("current_version", "0_0001")
        
        # 生成新版本号
        version_parts = current_version.split("_")
        if len(version_parts) == 2:
            prefix = version_parts[0]
            number = int(version_parts[1]) + 1
            new_version = f"{prefix}_{number:04d}"
        else:
            new_version = "0_0002"
        
        # 添加新版本记录
        new_version_record = {
            "version": new_version,
            "date": datetime.datetime.now().isoformat(),
            "description": description,
            "changes": changes
        }
        
        version_info["current_version"] = new_version
        version_info["version_history"].append(new_version_record)
        
        self._save_version_info(version_info)
        
        # 创建新版本的日志文件
        self.create_version_log(new_version)
        
        return new_version
    
    def create_version_log(self, version: str) -> str:
        """
        创建版本日志文件
        
        Args:
            version: 版本号
            
        Returns:
            日志文件路径
        """
        log_filename = f"{version}.log"
        log_filepath = os.path.join(self.logs_dir, log_filename)
        
        if not os.path.exists(log_filepath):
            with open(log_filepath, 'w', encoding='utf-8') as f:
                f.write(f"# SnowZone OCR自动化工具 - 版本 {version}\n")
                f.write(f"# 创建时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# ==========================================\n\n")
        
        return log_filepath
    
    def get_version_log_path(self, version: Optional[str] = None) -> str:
        """
        获取版本日志文件路径
        
        Args:
            version: 版本号，如果为None则使用当前版本
            
        Returns:
            日志文件路径
        """
        if version is None:
            version = self.get_current_version()
        
        log_filename = f"{version}.log"
        return os.path.join(self.logs_dir, log_filename)
    
    def get_current_log_path(self) -> str:
        """获取当前日志文件路径"""
        return os.path.join(self.logs_dir, "current.log")
    
    def _load_version_info(self) -> Dict:
        """加载版本信息"""
        try:
            with open(self.version_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {
                "current_version": self.current_version,
                "created_date": datetime.datetime.now().isoformat(),
                "version_history": []
            }
    
    def _save_version_info(self, version_info: Dict):
        """保存版本信息"""
        with open(self.version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
    
    def print_version_info(self):
        """打印版本信息"""
        version_info = self._load_version_info()
        print(f"当前版本: {version_info.get('current_version')}")
        print(f"项目创建时间: {version_info.get('created_date')}")
        print("\n版本历史:")
        
        for record in version_info.get("version_history", []):
            print(f"  版本 {record['version']} ({record['date'][:10]})")
            print(f"    描述: {record['description']}")
            if record.get('changes'):
                print("    变更:")
                for change in record['changes']:
                    print(f"      - {change}")
            print()


if __name__ == "__main__":
    # 测试版本管理器
    vm = VersionManager()
    vm.print_version_info()
